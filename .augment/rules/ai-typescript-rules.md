---
type: "manual"
---

# AI TypeScript Rules

- Define strong types for all model inputs and outputs without requiring manual adjustments.
- Validate and preprocess data before sending it to the model, using type guards where appropriate.
- Use TypeScript-friendly AI libraries. If none exist, generate complete and stable type definitions automatically.
- Do not pass entire API objects directly into Convex hooks if it creates deep generics. Always wrap calls in a thin contract layer with explicit types.
- Ensure performance optimizations (async patterns, avoiding redundant work) are applied automatically.
- Generate tests for preprocessing, contracts, and API interactions without requiring me to add them manually.
- Identify and prevent possible security risks (malicious input, injection, resource abuse).
