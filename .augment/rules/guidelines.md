---
type: "always_apply"
---

# User Guidelines

- When generating code, always prefer readability and maintainability over shortness.
- Remind me if I’m introducing bad practices (like disabling lint checks or commenting out large sections of code).
- Suggest optimizations if you see synchronous or inefficient patterns.
- If multiple solutions exist, explain trade-offs and recommend the most maintainable one.
- If I ask about AI projects, remind me of the `ai-typescript-rules.md` file and suggest @-mentioning it.
