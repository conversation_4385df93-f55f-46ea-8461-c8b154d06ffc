# JobbLogg Convex Functions Complete Export

**Export Date**: January 5, 2025  
**Total Functions**: 50+ TypeScript files  
**Architecture**: Queries, Mutations, Actions, HTTP Routes, Crons  

## 📁 Function Categories

### **🌐 HTTP Routes & API Endpoints**

#### `convex/http.ts` - Main HTTP Router
- **Stripe Webhook Processing**: `/stripe` endpoint
- **File Upload Handling**: File storage integration
- **Public Project Sharing**: Shared project access
- **CORS Configuration**: Cross-origin request handling

#### `convex/stripe/fulfill.ts` - Stripe Webhook Handler
- **Event Processing**: checkout.session.completed, subscription events
- **Trial Force-End Logic**: Immediate trial termination after payment
- **Database Sync**: Stripe → Convex subscription sync
- **Error Handling**: Comprehensive webhook error management

### **🏢 Core Business Logic**

#### `convex/projects.ts` - Project Management
- **CRUD Operations**: Create, read, update, delete projects
- **Sharing System**: Public project sharing with access control
- **Archive Management**: Project archiving/restoration
- **Customer Integration**: Project-customer relationships

#### `convex/customers.ts` - Customer Management
- **Customer CRUD**: Customer creation and management
- **Brønnøysundregisteret Integration**: Norwegian company data lookup
- **Address Management**: Multiple address types and overrides
- **Company Specialization**: Trade/industry categorization

#### `convex/logEntries.ts` - Activity Logging
- **Project Logs**: Activity tracking with images
- **File Attachments**: Image and document uploads
- **Edit History**: Log entry modification tracking
- **System Entries**: Automated system activity logs

#### `convex/messages.ts` - Chat System
- **Real-time Messaging**: Project-based chat threads
- **File Sharing**: Message attachments and media
- **Reactions**: Emoji reactions on messages
- **Read Status**: Message read tracking
- **Typing Indicators**: Real-time typing status

### **👥 Team & Collaboration**

#### `convex/teamManagement.ts` - Team Operations
- **User Roles**: Administrator, Project Manager, Worker roles
- **Team Invitations**: Email-based team member invites
- **Access Control**: Role-based permissions
- **Activity Tracking**: Team member activity monitoring

#### `convex/contractorOnboarding.ts` - Onboarding Workflow
- **Multi-step Wizard**: Guided contractor setup
- **Company Registration**: Business information collection
- **Plan Selection**: Subscription plan choice
- **Trial Activation**: Free trial initialization

#### `convex/subcontractorInvitations.ts` - Subcontractor System
- **Invitation Management**: Subcontractor project invites
- **Response Handling**: Accept/decline invitation processing
- **Specialization Matching**: Trade-based contractor matching
- **Project Preview**: Invitation preview data caching

#### `convex/projectsTeam.ts` - Project Team Management
- **Assignment Management**: Team member project assignments
- **Access Levels**: Owner, Collaborator, Subcontractor, Viewer
- **Invitation System**: Project-specific invitations
- **Permission Control**: Role-based project access

### **💳 Subscription & Billing**

#### `convex/subscriptions.ts` - Core Subscription Logic
- **Subscription CRUD**: Subscription management operations
- **Plan Management**: Basic, Professional, Enterprise plans
- **Trial System**: 7-day free trial management
- **Access Control**: Feature access based on subscription

#### `convex/subscriptions/upsertFromStripe.ts` - Database Sync
- **Idempotent Upsert**: Safe subscription data updates
- **Trial Conversion**: Trial to paid conversion handling
- **Status Synchronization**: Stripe → Convex status sync
- **Error Recovery**: Robust error handling and retry logic

#### `convex/trialManagement.ts` - Trial Lifecycle
- **Trial Creation**: New trial initialization
- **Expiration Handling**: Trial end processing
- **Notification System**: Trial reminder emails
- **Conversion Tracking**: Trial to paid conversion metrics

#### `convex/subscriptionDowngrade.ts` - Downgrade Management
- **Staged Downgrade**: Multi-stage downgrade process
- **Feature Restrictions**: Progressive feature limitation
- **Grace Periods**: Payment failure grace periods
- **Recovery Options**: Subscription recovery workflows

#### `convex/planChanges.ts` - Plan Management
- **Plan Upgrades**: Immediate plan upgrades
- **Plan Downgrades**: End-of-period downgrades
- **Billing Interval Changes**: Monthly/yearly switching
- **Proration Handling**: Stripe proration management

#### `convex/subscriptionCancellation.ts` - Cancellation System
- **Cancellation Requests**: User-initiated cancellations
- **Feedback Collection**: Cancellation reason tracking
- **Retention Offers**: Win-back attempt system
- **Data Retention**: Post-cancellation data handling

#### `convex/subscriptionReactivation.ts` - Reactivation System
- **Reactivation Requests**: Former customer return process
- **Welcome Back Offers**: Reactivation incentives
- **Plan Selection**: New plan choice for returning customers
- **Payment Method**: New payment method collection

### **💰 Payment Management**

#### `convex/paymentNotifications.ts` - Payment Notifications
- **Failure Notifications**: Payment failure alerts
- **Retry Notifications**: Payment retry status updates
- **Success Notifications**: Payment success confirmations
- **Preference Management**: User notification preferences

#### `convex/seatManagement.ts` - Seat Management
- **Seat Tracking**: Team member seat usage
- **Limit Enforcement**: Hard seat limit enforcement
- **Upgrade Prompts**: Seat limit upgrade suggestions
- **Usage Analytics**: Seat utilization reporting

#### `convex/billingPortal.ts` - Billing Portal
- **Portal Sessions**: Stripe billing portal access
- **Configuration Management**: Portal feature configuration
- **Session Tracking**: Portal usage analytics
- **Return Handling**: Post-portal return processing

### **📧 Communication & Notifications**

#### `convex/emails.ts` - Email System
- **Template Management**: Email template system
- **Delivery Tracking**: Email delivery status
- **Bounce Handling**: Email bounce processing
- **Engagement Tracking**: Open and click tracking

#### `convex/notifications.ts` - In-App Notifications
- **Notification Creation**: System notification generation
- **Read Status**: Notification read tracking
- **Type Management**: Different notification types
- **User Preferences**: Notification preference handling

#### `convex/emailTracking.ts` - Email Analytics
- **Delivery Analytics**: Email delivery metrics
- **Engagement Metrics**: Open and click rates
- **Bounce Analysis**: Bounce reason tracking
- **Campaign Performance**: Email campaign analytics

### **🔧 Utilities & Maintenance**

#### `convex/migrations.ts` - Database Migrations
- **Schema Updates**: Database schema evolution
- **Data Transformations**: Data format migrations
- **Version Control**: Migration version tracking
- **Rollback Support**: Migration rollback capabilities

#### `convex/crons.ts` - Scheduled Tasks
- **Trial Reminders**: Automated trial reminder emails
- **Cleanup Tasks**: Database cleanup operations
- **Analytics Updates**: Periodic analytics calculations
- **Health Checks**: System health monitoring

#### `convex/monitoring.ts` - System Monitoring
- **Performance Metrics**: System performance tracking
- **Error Monitoring**: Error rate and type tracking
- **Usage Analytics**: Feature usage analytics
- **Health Dashboards**: System health reporting

#### `convex/webhookDebug.ts` - Webhook Debugging
- **Event Logging**: Webhook event tracking
- **Error Analysis**: Webhook failure analysis
- **Retry Management**: Failed webhook retry logic
- **Status Monitoring**: Webhook delivery status

### **🔍 Development & Testing**

#### `convex/testDataUtilities.ts` - Test Data Management
- **Test Data Creation**: Development test data generation
- **Data Cleanup**: Test data cleanup utilities
- **Mock Data**: Realistic mock data generation
- **Environment Setup**: Development environment setup

#### `convex/adminUtils.ts` - Admin Utilities
- **Admin Operations**: Administrative functions
- **Data Management**: Bulk data operations
- **User Management**: Admin user operations
- **System Configuration**: System setting management

#### `convex/maintenance.ts` - Maintenance Operations
- **Database Cleanup**: Periodic cleanup operations
- **Data Optimization**: Database optimization tasks
- **Archive Management**: Old data archiving
- **Performance Tuning**: System performance optimization

---

## 🏗️ Architecture Patterns

### **Function Types**
- **Queries**: Read-only data access (reactive)
- **Mutations**: Data modification operations
- **Actions**: External API calls and complex operations
- **HTTP Actions**: HTTP endpoint handlers
- **Cron Jobs**: Scheduled background tasks

### **Security Patterns**
- **Authentication**: Clerk integration for user auth
- **Authorization**: Role-based access control
- **Data Validation**: Input validation and sanitization
- **Rate Limiting**: API rate limiting implementation

### **Integration Patterns**
- **Stripe Integration**: Comprehensive payment processing
- **Email Integration**: Resend email service
- **File Storage**: Convex file storage system
- **External APIs**: Norwegian business registry integration

This comprehensive function export provides complete visibility into the JobbLogg application architecture and business logic implementation.
