# JobbLogg - Prøveperiode og Funksjonstilgang

Dette dokumentet beskriver hvilke funksjoner som er tilgjengelige og begrenset når prøveperioden utløper i JobbLogg.

## 📋 Oversikt

Når prøveperioden utløper, g<PERSON><PERSON> over i en **begrenset modus** hvor brukere kan se eksisterende data, men ikke opprette nytt innhold eller administrere team. <PERSON><PERSON> sikrer at viktig prosjektdokumentasjon forblir tilgjengelig mens det oppfordres til oppgradering.

### 🕐 Prøveperiode-stadier

1. **Aktiv prøveperiode** - Full tilgang til alle funksjoner
2. **Utløpt prøveperiode** - Begrenset tilgang (grace period)
3. **Etter grace period** - Kun lesetilgang

---

## ✅ Tilgjengelige Funksjoner (Lesetilgang)

Disse funksjonene forblir tilgjengelige under prøveperiodens grace period:

### 📊 Dashboard og Navigasjon
| Funksjon | Beskrivelse | Tilgang | Komponenter |
|----------|-------------|---------|-------------|
| **Dashboard** | Oversikt over prosjekter og aktivitet | ✅ Full lesetilgang | `Dashboard.tsx` |
| **Navigasjon** | Hovedmeny og sidenavigasjon | ✅ Full tilgang | `AuthenticatedLayout.tsx` |
| **Prosjektoversikt** | Liste over alle prosjekter | ✅ Kan se alle prosjekter | `ProjectCard.tsx` |

### 📁 Prosjektvisning
| Funksjon | Beskrivelse | Tilgang | Komponenter |
|----------|-------------|---------|-------------|
| **Prosjektdetaljer** | Se prosjektinformasjon og metadata | ✅ Full lesetilgang | `ProjectDetail.tsx` |
| **Prosjektlogg** | Se alle logginnlegg og aktivitet | ✅ Full lesetilgang | `ProjectLog.tsx` |
| **Kundeinfo** | Se kundeinformasjon og kontaktdetaljer | ✅ Full lesetilgang | `CustomerInfo.tsx` |
| **Prosjektbilder** | Se opplastede bilder og dokumenter | ✅ Full lesetilgang | `ImageGallery.tsx` |

### 💬 Kommunikasjon
| Funksjon | Beskrivelse | Tilgang | Komponenter |
|----------|-------------|---------|-------------|
| **Prosjektsamtaler** | Se eksisterende meldinger og samtaler | ✅ Full lesetilgang | `ChatContainer.tsx` |
| **Meldingshistorikk** | Se alle tidligere meldinger | ✅ Full lesetilgang | `MessageList.tsx` |
| **Delte prosjekter** | Se prosjekter delt med kunder | ✅ Full lesetilgang | `SharedProject.tsx` |

### 👥 Team og Brukere
| Funksjon | Beskrivelse | Tilgang | Komponenter |
|----------|-------------|---------|-------------|
| **Teamoversikt** | Se teammedlemmer og roller | ✅ Full lesetilgang | `TeamManagement.tsx` |
| **Brukerinformasjon** | Se brukerdetaljer og kontaktinfo | ✅ Full lesetilgang | `TeamMemberCard.tsx` |
| **Prosjektteam** | Se hvem som er tildelt prosjekter | ✅ Full lesetilgang | `ProjectTeam.tsx` |

### ⚙️ Innstillinger
| Funksjon | Beskrivelse | Tilgang | Komponenter |
|----------|-------------|---------|-------------|
| **Bedriftsprofil** | Se bedriftsinformasjon | ✅ Lesetilgang | `CompanyProfile.tsx` |
| **Abonnementsstatus** | Se abonnementsdetaljer | ✅ Full tilgang | `SubscriptionStatus.tsx` |
| **Brukerprofil** | Se og redigere egen profil | ✅ Full tilgang | `UserProfile.tsx` |

---

## 🔒 Begrensede Funksjoner (Krever Aktivt Abonnement)

Disse funksjonene er deaktivert og viser elegante "oppgrader"-meldinger:

### 📝 Prosjektadministrasjon
| Funksjon | Beskrivelse | Begrenset Tilgang | UX-behandling | Komponenter |
|----------|-------------|-------------------|---------------|-------------|
| **Opprett Prosjekt** | Opprette nye prosjekter | 🔒 Helt blokkert | Deaktivert knapp med låsikon | `Dashboard.tsx`, `CreateProjectWizard.tsx` |
| **"Opprett ditt første prosjekt"** | EmptyState opprettelsesknapp | 🔒 Helt blokkert | Deaktivert overlay med oppgraderingsmelding | `Dashboard.tsx` (EmptyState) |
| **Rediger Prosjekt** | Endre prosjektdetaljer | 🔒 Helt blokkert | Skjulte redigeringsknapper | `ProjectDetail.tsx` |
| **Arkiver Prosjekt** | Arkivere eller gjenåpne prosjekter | 🔒 Helt blokkert | Deaktiverte handlinger | `ProjectActions.tsx` |

### 📤 Fil- og Innholdsopplasting
| Funksjon | Beskrivelse | Begrenset Tilgang | UX-behandling | Komponenter |
|----------|-------------|-------------------|---------------|-------------|
| **Last opp bilder** | Laste opp bilder til prosjekter | 🔒 Helt blokkert | Deaktivert knapp med tooltip | `ProjectDetail.tsx` |
| **Filopplasting i chat** | Laste opp filer i meldinger | 🔒 Helt blokkert | Deaktivert knapp med låsikon | `MessageInput.tsx` |
| **Kameraopptak** | Ta bilder med kamera | 🔒 Helt blokkert | Deaktivert kameraknapp | `MessageInput.tsx` |

### 👥 Teamadministrasjon
| Funksjon | Beskrivelse | Begrenset Tilgang | UX-behandling | Komponenter |
|----------|-------------|-------------------|---------------|-------------|
| **Inviter teammedlemmer** | Sende invitasjoner til nye brukere | 🔒 Helt blokkert | Deaktivert invitasjonsknapp | `TeamManagement.tsx` |
| **"Inviter første teammedlem"** | EmptyState invitasjonsknapp | 🔒 Helt blokkert | Deaktivert overlay med oppgraderingsmelding | `TeamManagement.tsx` (EmptyState) |
| **Fjern teammedlemmer** | Fjerne brukere fra teamet | 🔒 Helt blokkert | Deaktiverte handlingsknapper | `TeamMemberDetailsModal.tsx` |
| **Sperr/åpne brukere** | Blokkere eller åpne brukerkontoer | 🔒 Helt blokkert | Deaktiverte handlingsknapper | `TeamMemberDetailsModal.tsx` |
| **Endre roller** | Endre brukerroller og tilganger | 🔒 Helt blokkert | Deaktivert rolleendring | `ChangeRoleModal.tsx` |
| **Tildel til prosjekt** | Tildele teammedlemmer til prosjekter | 🔒 Helt blokkert | Deaktivert tildelingsknapp | `ProjectDetail.tsx` |
| **"Tildel første teammedlem"** | EmptyState tildelingsknapp | 🔒 Helt blokkert | Deaktivert overlay med oppgraderingsmelding | `ProjectTeamSection.tsx` (EmptyState) |

### 🔗 Deling og Samarbeid
| Funksjon | Beskrivelse | Begrenset Tilgang | UX-behandling | Komponenter |
|----------|-------------|-------------------|---------------|-------------|
| **Del prosjekt** | Dele prosjekter med kunder | 🔒 Helt blokkert | Deaktivert delingsknapp | `ShareProjectModal.tsx` |
| **Administrer deling** | Endre delingsinnstillinger | 🔒 Helt blokkert | Deaktiverte innstillinger | `ProjectSharing.tsx` |
| **Inviter underentreprenører** | Invitere eksterne samarbeidspartnere | 🔒 Helt blokkert | Deaktivert invitasjonsknapp | `SubcontractorInvite.tsx` |

### 💬 Kommunikasjon og Meldinger
| Funksjon | Beskrivelse | Begrenset Tilgang | UX-behandling | Komponenter |
|----------|-------------|-------------------|---------------|-------------|
| **Send meldinger** | Sende nye meldinger i prosjektchat | 🔒 Helt blokkert | Deaktivert meldingsinput | `MessageInput.tsx` |
| **Opprett samtaler** | Starte nye samtaler | 🔒 Helt blokkert | Deaktivert opprettelsesknapp | `ChatContainer.tsx` |
| **Meldingsreaksjoner** | Reagere på meldinger med emojis | 🔒 Helt blokkert | Deaktiverte reaksjonsknapper | `MessageReactions.tsx` |

---

## 🎨 Brukeropplevelse for Begrensede Funksjoner

### 🔒 Deaktivert Tilstand (Disabled State)
Begrensede funksjoner vises med følgende UX-behandling:

#### **Visuelle Indikatorer:**
- **Låsikon** over deaktiverte elementer
- **50% gjennomsiktighet** på utilgjengelige komponenter
- **Overlay med oppgraderingsmelding** på større områder
- **Tooltip med forklaring** ved hover på små elementer

#### **Interaktive Elementer:**
- **Deaktiverte knapper** med låsikon og hover-tooltip
- **Oppgraderingsmodal** ved klikk på større deaktiverte områder
- **Kontekstuelle meldinger** som forklarer begrensningen
- **Direkte lenker** til abonnementsadministrasjon

#### **Meldinger og Tekst:**
- **Norske meldinger** som matcher brukergrensesnittet
- **Tydelige forklaringer** på hvorfor funksjonen er begrenset
- **Oppfordrende call-to-action** for oppgradering
- **Informasjon om fordeler** ved aktivt abonnement

### 📱 Responsive Design
- **Mobile enheter**: Kompakte tooltips og touch-vennlige oppgraderingsknapper
- **Desktop**: Utvidede hover-states og detaljerte oppgraderingsmodaler
- **Konsistent styling** på tvers av alle enheter og skjermstørrelser

---

## 🛠️ Teknisk Implementering

### Komponenter for Tilgangskontroll
```typescript
// Hovedkomponenter for abonnementskontroll
SubscriptionGate.tsx       // Hovedlogikk for tilgangskontroll
DisabledFeature.tsx        // Overlay for deaktiverte funksjoner
DisabledButton.tsx         // Deaktiverte knapper med tooltips
TrialExpiredBanner.tsx     // Topbanner for utløpt prøveperiode
```

### Tilgangslogikk
```typescript
// Funksjonstilgang basert på abonnementsstatus
hasFullAccess = hasActiveSubscription || isInTrial
canCreateProjects = hasActiveSubscription || isInTrial  
canAccessProjects = hasActiveSubscription || isInTrial || isInGracePeriod
isReadOnly = isInGracePeriod || isTrialExpired
```

### Bruk av SubscriptionGate
```typescript
// Eksempel på implementering
<SubscriptionGate feature="create_project" variant="button">
  <PrimaryButton onClick={handleCreate}>
    Opprett Prosjekt
  </PrimaryButton>
</SubscriptionGate>
```

---

## 🔄 Rollespesifikke Begrensninger

### 👑 Administrator
- **Kan se**: Alle abonnementsdetaljer og oppgraderingsalternativer
- **Kan ikke**: Opprette nytt innhold eller administrere team
- **Spesielle tilganger**: Abonnementsadministrasjon og faktureringsdetaljer

### 👤 Utførende (Regular User)
- **Kan se**: Begrenset abonnementsinformasjon (kun status og utløpsdato)
- **Kan ikke**: Opprette innhold eller se oppgraderingsalternativer
- **Begrensninger**: Ingen tilgang til abonnementsadministrasjon

### 🏗️ Prosjektleder
- **Kan se**: Prosjektspesifikk informasjon og teamdetaljer
- **Kan ikke**: Administrere team eller opprette nye prosjekter
- **Spesielle tilganger**: Kan se alle prosjektdetaljer de er tildelt

---

## 📊 Funksjonsoversikt Tabell

| Kategori | Funksjon | Aktiv Prøve | Utløpt Prøve | Komponenter |
|----------|----------|-------------|--------------|-------------|
| **Prosjekter** | Se prosjekter | ✅ | ✅ | `ProjectCard`, `ProjectDetail` |
| **Prosjekter** | Opprett prosjekt | ✅ | 🔒 | `Dashboard`, `CreateProjectWizard` |
| **Prosjekter** | Rediger prosjekt | ✅ | 🔒 | `ProjectDetail` |
| **Team** | Se teammedlemmer | ✅ | ✅ | `TeamManagement` |
| **Team** | Inviter medlemmer | ✅ | 🔒 | `InviteTeamMemberModal` |
| **Team** | Administrer roller | ✅ | 🔒 | `ChangeRoleModal` |
| **Filer** | Se filer | ✅ | ✅ | `ImageGallery` |
| **Filer** | Last opp filer | ✅ | 🔒 | `MessageInput`, `ProjectDetail` |
| **Chat** | Se meldinger | ✅ | ✅ | `ChatContainer` |
| **Chat** | Send meldinger | ✅ | 🔒 | `MessageInput` |
| **Deling** | Se delte prosjekter | ✅ | ✅ | `SharedProject` |
| **Deling** | Del prosjekter | ✅ | 🔒 | `ShareProjectModal` |

---

## 🎯 Implementeringseksempler

### Deaktivert Knapp med Tooltip
```typescript
<SubscriptionGate feature="team_management" variant="button">
  <PrimaryButton onClick={() => setShowInviteModal(true)}>
    Inviter teammedlem
  </PrimaryButton>
</SubscriptionGate>
```

### Deaktivert Område med Overlay
```typescript
<SubscriptionGate feature="file_upload" variant="disable">
  <div className="file-upload-area">
    <input type="file" />
    <button>Last opp filer</button>
  </div>
</SubscriptionGate>
```

### Betinget Rendering
```typescript
const { hasFullAccess } = useSubscriptionAccess();

return (
  <div>
    {hasFullAccess ? (
      <CreateProjectButton />
    ) : (
      <DisabledFeature feature="create_project">
        <CreateProjectButton />
      </DisabledFeature>
    )}
  </div>
);
```

---

## 📞 Support og Oppgradering

### For Brukere
- **Abonnementsadministrasjon**: Tilgjengelig via brukermeny → "Abonnement"
- **Oppgraderingslenker**: Tilgjengelig i alle deaktiverte funksjoner
- **Kundesupport**: <EMAIL> eller via hjelpeseksjonen
- **Prøveperiode-forlengelse**: Kontakt kundesupport for spesielle tilfeller

### For Utviklere
- **Komponentdokumentasjon**: Se `src/components/subscription/README.md`
- **Tilgangslogikk**: Implementert i `useSubscriptionAccess` hook
- **Testing**: Bruk `VITE_FORCE_TRIAL_EXPIRED=true` for å simulere utløpt prøveperiode
- **Debugging**: Bruk `SubscriptionDebugPanel` i utviklingsmodus

### Vanlige Spørsmål
**Q: Kan brukere se sine egne data etter prøveperioden?**
A: Ja, all eksisterende data forblir synlig i lesetilstand.

**Q: Hvor lenge varer grace period?**
A: Grace period varer i 7 dager etter prøveperiodens utløp.

**Q: Kan administratorer oppgradere for hele teamet?**
A: Ja, kun administratorer kan administrere abonnement og oppgraderinger.

---

*Sist oppdatert: Januar 2025 - Versjon 2.0*
