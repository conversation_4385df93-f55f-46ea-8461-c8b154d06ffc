# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview

JobbLogg is a modern project logging application built for Norwegian contractors and craftspeople. It's a React + TypeScript application using Convex as a serverless backend with real-time database capabilities.

## Development Commands

### Quick Start
```bash
# Install dependencies
npm install

# Start development servers (both Vite and Convex)
npm run dev

# Start with port cleanup (if ports are occupied)
npm run dev:clear

# Start only frontend (Vite)
npm run dev:vite

# Start only backend (Convex)
npm run dev:convex

# Low memory development mode
npm run dev:low-memory
```

### Building & Preview
```bash
# Full build with validation
npm run build

# Build without type checking (faster)
npm run build:no-typecheck

# Preview production build locally
npm run preview

# Force preview (clear ports first)
npm run preview:force
```

### Code Quality & Testing
```bash
# Run ESLint
npm run lint

# Run JobbLogg-specific ESLint config
npm run lint:jobblogg

# TypeScript type checking
npm run type-check

# Validate import statements
npm run validate:imports

# Pre-commit validation (lint + imports + types)
npm run validate:pre-commit

# Pre-build validation (imports + types)
npm run validate:pre-build
```

### Port Management
```bash
# Check port availability
npm run port-check

# Kill processes on development ports
npm run port-clear
```

### Development Tools
```bash
# Generate new component template
npm run generate:component

# Test import validation
npm run test:imports

# Test dynamic imports (builds project)
npm run test:dynamic-imports
```

## Architecture Overview

### Tech Stack
- **Frontend**: React 19 + TypeScript with Vite 7 build system
- **Backend**: Convex (serverless real-time database)
- **Authentication**: Clerk with Norwegian localization
- **Styling**: Tailwind CSS with custom JobbLogg design system
- **Routing**: React Router v7 with lazy-loaded routes
- **Payments**: Stripe integration
- **Maps**: Google Maps API integration
- **Email**: Resend service
- **Deployment**: Netlify with automatic deployments

### Key Directories
```
src/
├── components/          # Reusable UI components and business logic
│   ├── ui/             # Design system components (Form, Button, Layout, etc.)
│   └── LazyComponents.tsx  # Lazy-loaded route components
├── pages/              # Route-specific page components
├── hooks/              # Custom React hooks
├── utils/              # Utility functions and helpers
├── types/              # TypeScript type definitions
└── styles/             # Theme and styling configurations

convex/                 # Backend functions and schema
├── _generated/         # Auto-generated Convex files
├── auth.config.ts      # Authentication configuration
└── [functions].ts      # Database queries and mutations

scripts/                # Development automation scripts
├── dev-start.js        # Smart development server manager
├── port-manager.js     # Port conflict resolution
└── component-template-generator.js  # Code generation
```

### Authentication Flow
The app uses a sophisticated authentication system with Clerk:
- Norwegian localization (`nbNO`) with custom translations
- Contractor onboarding guard system that checks user completion status
- Public routes (shared projects, legal pages) vs authenticated routes
- Team invitation system with magic links

### Routing Architecture
- **Lazy Loading**: All major routes are lazy-loaded for performance
- **Authentication Guards**: Routes are wrapped with `Authenticated`/`Unauthenticated` components
- **Onboarding Guards**: `ContractorOnboardingGuardSimple` ensures users complete setup
- **Offline Support**: Critical routes (Dashboard, ProjectDetail, ProjectLog) have fallback imports for offline development

### Component Architecture
- **Design System**: Comprehensive UI components in `src/components/ui/`
- **Lazy Components**: Performance-optimized component loading with preloading for critical routes
- **Form System**: Advanced form components with validation and Norwegian address integration
- **Layout System**: `AuthenticatedLayout` provides consistent navigation and structure

### Development Environment
- **Port Management**: Vite runs on port 5173, preview on 4173 with strict port enforcement
- **Memory Optimization**: Special configurations for low-memory development environments
- **Hot Module Replacement**: Optimized HMR with file watching exclusions
- **Convex Integration**: Real-time backend with TypeScript generation and dashboard integration

### Build System
- **Vite Configuration**: Modern build system with code splitting and manual chunks
- **TypeScript**: Strict type checking with separate configs for app and node environments
- **Bundle Optimization**: Vendor, router, authentication, and UI library chunks for optimal loading
- **Environment Handling**: Comprehensive environment variable validation and production checks

### State Management
- **Convex Real-time**: Server state managed through Convex queries and mutations
- **React Router**: Client-side routing state
- **Clerk**: Authentication and user state
- **Local Storage**: Offline capabilities and preferences

### Norwegian Localization
- Complete Norwegian language support through Clerk
- Norwegian address validation and lookup
- Norwegian county and postal code utilities
- Cultural adaptations for Norwegian business practices

## Environment Setup

### Required Environment Variables
```bash
# Copy the example file
cp .env.example .env.local

# Required variables:
VITE_CONVEX_URL=         # Convex backend URL
CONVEX_DEPLOYMENT=       # Convex deployment identifier
VITE_CLERK_PUBLISHABLE_KEY=  # Clerk authentication
VITE_GOOGLE_MAPS_API_KEY=    # Google Maps integration
VITE_STRIPE_PUBLISHABLE_KEY= # Stripe payments
```

### Development Server Ports
- **Vite Dev Server**: http://localhost:5173/
- **Vite Preview**: http://localhost:4173/
- **Convex Dashboard**: Auto-detected from deployment configuration

## Common Development Workflows

### Adding New Routes
1. Create page component in `src/pages/`
2. Add lazy import in `src/components/LazyComponents.tsx`
3. Add route definition in `src/App.tsx` with appropriate guards
4. Consider offline support for critical routes

### Working with Convex Backend
```bash
# Start Convex development server
npx convex dev

# Deploy to Convex
npx convex deploy

# Open Convex dashboard
npx convex dashboard
```

### Component Development
```bash
# Generate component template
npm run generate:component

# Follow naming convention: PascalCase for components
# Place in appropriate directory under src/components/
```

### Styling with Tailwind
- Use JobbLogg design system colors (e.g., `jobblogg-primary`, `jobblogg-text-medium`)
- Follow responsive design patterns
- Utilize custom animations and spacing system

### Performance Considerations
- Routes are lazy-loaded by default
- Critical routes have offline fallbacks
- Bundle splitting is configured for optimal loading
- Memory-conscious development mode available
