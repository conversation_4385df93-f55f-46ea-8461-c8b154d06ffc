{"webhookEndpoints": {"development": {"convex": "https://enchanted-quail-174.convex.site/stripe", "local": "http://localhost:3000/stripe"}, "staging": {"convex": "https://enchanted-quail-174.convex.site/stripe"}, "production": {"convex": "https://standing-aardvark-575.convex.site/stripe"}}, "testScenarios": {"critical": [{"name": "Trial to Paid Conversion", "events": ["checkout.session.completed", "customer.subscription.created", "invoice.paid"], "description": "Tests the complete trial-to-paid conversion flow"}, {"name": "Payment Failure Recovery", "events": ["invoice.payment_failed", "invoice.paid"], "description": "Tests payment failure and subsequent recovery"}, {"name": "Subscription Lifecycle", "events": ["customer.subscription.created", "customer.subscription.updated", "customer.subscription.deleted"], "description": "Tests complete subscription lifecycle management"}], "standard": [{"name": "Customer Management", "events": ["customer.created"], "description": "Tests customer creation and management"}, {"name": "Trial Management", "events": ["customer.subscription.trial_will_end"], "description": "Tests trial expiration notifications"}, {"name": "Payment Intents", "events": ["payment_intent.succeeded", "payment_intent.payment_failed"], "description": "Tests one-time payment processing"}]}, "requiredEvents": ["customer.created", "checkout.session.completed", "customer.subscription.created", "customer.subscription.updated", "customer.subscription.deleted", "customer.subscription.trial_will_end", "invoice.paid", "invoice.payment_failed", "payment_intent.succeeded", "payment_intent.payment_failed"], "testMetadata": {"source": "stripe-cli", "environment": "development", "test_run_id": "generated_uuid", "automated": true}, "errorScenarios": [{"name": "Invalid Signature", "description": "Test webhook with invalid signature", "method": "curl", "expectedResponse": 400}, {"name": "Missing Signature", "description": "Test webhook without signature header", "method": "curl", "expectedResponse": 400}, {"name": "Malformed JSON", "description": "Test webhook with invalid JSON payload", "method": "curl", "expectedResponse": 400}], "monitoring": {"logFiles": ["stripe-webhook-test.log", "convex-logs.log"], "metricsToTrack": ["processing_time_ms", "error_count", "success_rate", "duplicate_events", "retry_attempts"], "alertThresholds": {"error_rate": 0.05, "processing_time_ms": 5000, "duplicate_events": 3}}, "cleanup": {"testDataPatterns": ["test_*", "*_test_*", "stripe_cli_*"], "retentionDays": 1, "autoCleanup": true}}