import { mutation, query } from './_generated/server';
import { v } from 'convex/values';

// Admin function to create user without auth (for debugging)
export const createUserAdmin = mutation({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    console.log('🔧 Admin: Creating user record for:', args.clerkUserId);
    
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();
    
    if (existingUser) {
      console.log('👤 User already exists:', existingUser._id);
      return {
        success: true,
        action: 'user_already_exists',
        user: existingUser
      };
    }
    
    // Create new user record
    const userId = await ctx.db.insert("users", {
      clerkUserId: args.clerkUserId,
      contractorCompleted: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
    
    const newUser = await ctx.db.get(userId);
    console.log('✅ New user created:', newUser?._id);
    
    return {
      success: true,
      action: 'user_created',
      user: newUser
    };
  },
});

// Admin function to list all users (for debugging)
export const listAllUsers = query({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    console.log('📋 Total users in database:', users.length);
    return users.map(user => ({
      _id: user._id,
      clerkUserId: user.clerkUserId,
      contractorCompleted: user.contractorCompleted,
      createdAt: user.createdAt
    }));
  },
});

// Admin function to list all subscriptions (for debugging)
export const listAllSubscriptions = query({
  args: {},
  handler: async (ctx) => {
    const subscriptions = await ctx.db.query("subscriptions").collect();
    console.log('💳 Total subscriptions in database:', subscriptions.length);
    return subscriptions.map(sub => ({
      _id: sub._id,
      userId: sub.userId,
      status: sub.status,
      planLevel: sub.planLevel,
      stripeCustomerId: sub.stripeCustomerId,
      trialStart: sub.trialStart,
      trialEnd: sub.trialEnd,
      createdAt: sub.createdAt
    }));
  },
});

// Admin function to delete subscription (for cleanup)
export const deleteSubscription = mutation({
  args: { subscriptionId: v.id("subscriptions") },
  handler: async (ctx, args) => {
    console.log('🗑️ Admin: Deleting subscription:', args.subscriptionId);
    await ctx.db.delete(args.subscriptionId);
    return { success: true };
  },
});
