import { v } from "convex/values";
import Strip<PERSON> from "stripe";
import { internal } from "../_generated/api";
import { action, internalMutation } from "../_generated/server";

/**
 * Admin function to fix subscription periods by re-fetching from Stripe
 * This corrects any subscriptions that have stale period dates
 */
export const fixSubscriptionPeriodsFromStripe = action({
  args: {
    stripeSubscriptionId: v.optional(v.string()),
    userId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: "2024-06-20",
    });

    try {
      // Find subscriptions to fix
      let subscriptionsToFix: any[] = [];
      
      if (args.stripeSubscriptionId) {
        // Fix specific subscription
        const sub = await ctx.runMutation(internal.adminUtils.fixSubscriptionPeriods.getSubscriptionByStripeId, {
          stripeSubscriptionId: args.stripeSubscriptionId
        });
        if (sub) subscriptionsToFix = [sub];
      } else if (args.userId) {
        // Fix all subscriptions for user
        const sub = await ctx.runQuery(internal.subscriptions.getUserSubscriptionInternal, {
          userId: args.userId
        });
        if (sub) subscriptionsToFix = [sub];
      } else {
        // Fix all active subscriptions
        subscriptionsToFix = await ctx.runMutation(internal.adminUtils.fixSubscriptionPeriods.getAllActiveSubscriptions, {});
      }

      console.log(`🔧 Found ${subscriptionsToFix.length} subscriptions to fix`);

      const results = [];
      
      for (const subscription of subscriptionsToFix) {
        try {
          console.log(`🔄 Fixing subscription: ${subscription.stripeSubscriptionId}`);
          
          // Fetch fresh data from Stripe
          let stripeSubscription = await stripe.subscriptions.retrieve(subscription.stripeSubscriptionId, {
            expand: ['customer', 'items.data.price', 'latest_invoice']
          });

          // Detect if billing is still anchored to trial end
          const trialEnd = (stripeSubscription as any).trial_end;
          const currentPeriodEnd = (stripeSubscription as any).current_period_end;
          const stillAnchoredToTrial = !!trialEnd && currentPeriodEnd === trialEnd;

          if (stripeSubscription.status === 'trialing' || stillAnchoredToTrial) {
            console.log('🔧 Realigning billing cycle to NOW for subscription:', stripeSubscription.id, { stillAnchoredToTrial });
            const updateParams: any = {
              billing_cycle_anchor: 'now',
              proration_behavior: 'none',
              cancel_at_period_end: false,
            };
            if (stripeSubscription.status === 'trialing' || (trialEnd && trialEnd * 1000 > Date.now())) {
              updateParams.trial_end = 'now';
            }
            await stripe.subscriptions.update(stripeSubscription.id, updateParams);

            // Re-fetch updated subscription to get accurate period dates
            stripeSubscription = await stripe.subscriptions.retrieve(stripeSubscription.id, {
              expand: ['customer', 'items.data.price', 'latest_invoice']
            });
          }

          console.log(`📅 Stripe periods:`, {
            start: new Date((stripeSubscription as any).current_period_start * 1000).toISOString(),
            end: new Date((stripeSubscription as any).current_period_end * 1000).toISOString(),
            status: stripeSubscription.status
          });

          // Build upsert payload with fresh Stripe data
          const upsertPayload: any = {
            userId: subscription.userId,
            stripeSubscriptionId: stripeSubscription.id,
            stripeCustomerId: typeof stripeSubscription.customer === 'string' 
              ? stripeSubscription.customer 
              : stripeSubscription.customer?.id,
            status: stripeSubscription.status,
            planLevel: subscription.planLevel, // Keep existing plan level
            billingInterval: subscription.billingInterval, // Keep existing billing interval
          };

          // Add current period dates from Stripe
          if ((stripeSubscription as any).current_period_start) {
            upsertPayload.currentPeriodStart = (stripeSubscription as any).current_period_start * 1000;
          }
          if ((stripeSubscription as any).current_period_end) {
            upsertPayload.currentPeriodEnd = (stripeSubscription as any).current_period_end * 1000;
          }

          // Add trial dates if they exist
          if ((stripeSubscription as any).trial_start) {
            upsertPayload.trialStart = (stripeSubscription as any).trial_start * 1000;
          }
          if ((stripeSubscription as any).trial_end) {
            upsertPayload.trialEnd = (stripeSubscription as any).trial_end * 1000;
          }

          // Keep existing trialConvertedAt if it exists
          if (subscription.trialConvertedAt) {
            upsertPayload.trialConvertedAt = subscription.trialConvertedAt;
          }

          // Keep existing seats
          if (subscription.seats) {
            upsertPayload.seats = subscription.seats;
          }

          // Update subscription with fresh Stripe data
          await ctx.runMutation(internal.subscriptions.upsertFromStripe.upsertFromStripe, upsertPayload);

          results.push({
            subscriptionId: subscription._id,
            stripeSubscriptionId: subscription.stripeSubscriptionId,
            status: 'fixed',
            oldPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd).toISOString() : 'none',
            newPeriodEnd: upsertPayload.currentPeriodEnd ? new Date(upsertPayload.currentPeriodEnd).toISOString() : 'none',
          });

          console.log(`✅ Fixed subscription: ${subscription.stripeSubscriptionId}`);

        } catch (error) {
          console.error(`❌ Error fixing subscription ${subscription.stripeSubscriptionId}:`, error);
          results.push({
            subscriptionId: subscription._id,
            stripeSubscriptionId: subscription.stripeSubscriptionId,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      console.log(`🎉 Fixed ${results.filter(r => r.status === 'fixed').length} subscriptions`);
      return {
        success: true,
        results,
        summary: {
          total: subscriptionsToFix.length,
          fixed: results.filter(r => r.status === 'fixed').length,
          errors: results.filter(r => r.status === 'error').length,
        }
      };

    } catch (error) {
      console.error("❌ Error in fixSubscriptionPeriodsFromStripe:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  },
});

/**
 * Get subscription by Stripe ID (internal)
 */
export const getSubscriptionByStripeId = internalMutation({
  args: { stripeSubscriptionId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_subscription", (q) => 
        q.eq("stripeSubscriptionId", args.stripeSubscriptionId)
      )
      .first();
  },
});

/**
 * Get all active subscriptions (internal)
 */
export const getAllActiveSubscriptions = internalMutation({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("subscriptions")
      .filter((q) => q.or(
        q.eq(q.field("status"), "active"),
        q.eq(q.field("status"), "trialing")
      ))
      .collect();
  },
});
