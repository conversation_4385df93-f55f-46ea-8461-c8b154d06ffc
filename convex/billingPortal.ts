import { v } from "convex/values";
import { mutation, query, action, internalMutation, internalQuery, internalAction } from "./_generated/server";
import { internal } from "./_generated/api";

/**
 * Enhanced Stripe Customer Portal configuration and management
 */

// Portal configuration constants
const PORTAL_CONFIG = {
  business_profile: {
    headline: "Administrer ditt JobbLogg-abonnement",
    privacy_policy_url: `${process.env.FRONTEND_URL || 'https://jobblogg.no'}/privacy-policy`,
    terms_of_service_url: `${process.env.FRONTEND_URL || 'https://jobblogg.no'}/terms-of-service`,
  },
  features: {
    customer_update: {
      enabled: true,
      allowed_updates: ['email', 'address', 'phone', 'tax_id'] as const,
    },
    invoice_history: {
      enabled: true,
    },
    payment_method_update: {
      enabled: true,
    },
    subscription_cancel: {
      enabled: true,
      mode: 'at_period_end' as const,
      proration_behavior: 'none' as const,
      cancellation_reason: {
        enabled: true,
        options: [
          'too_expensive',
          'missing_features', 
          'switched_service',
          'customer_service',
          'low_quality',
          'unused',
          'other'
        ] as const,
      },
    },
    subscription_update: {
      enabled: true,
      default_allowed_updates: ['price', 'promotion_code'] as const,
      proration_behavior: 'create_prorations' as const,
    },
  },
  default_return_url: `${process.env.FRONTEND_URL || 'https://jobblogg.no'}/subscription?portal_return=true`,
  metadata: {
    company: 'JobbLogg',
    version: '2.0',
    locale: 'nb-NO',
  },
};

/**
 * Create or update Stripe Customer Portal configuration
 */
export const configureCustomerPortal = internalAction({
  args: {},
  handler: async (ctx, args) => {
    const stripe = new (await import('stripe')).default(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: "2025-08-27.basil",
    });

    try {
      // Check if we already have a portal configuration
      const existingConfigs = await stripe.billingPortal.configurations.list({ limit: 1 });
      
      let portalConfig;
      
      if (existingConfigs.data.length > 0) {
        // Update existing configuration
        portalConfig = await stripe.billingPortal.configurations.update(
          existingConfigs.data[0].id,
          PORTAL_CONFIG
        );
        console.log('✅ Updated existing Stripe Customer Portal configuration');
      } else {
        // Create new configuration
        portalConfig = await stripe.billingPortal.configurations.create(PORTAL_CONFIG);
        console.log('✅ Created new Stripe Customer Portal configuration');
      }

      // Store configuration ID in database for future reference
      await ctx.runMutation(internal.billingPortal.storePortalConfiguration, {
        configurationId: portalConfig.id,
        configuration: portalConfig,
      });

      return {
        success: true,
        configurationId: portalConfig.id,
        configuration: portalConfig,
      };

    } catch (error) {
      console.error('❌ Failed to configure Customer Portal:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },
});

/**
 * Store portal configuration in database
 */
export const storePortalConfiguration = internalMutation({
  args: {
    configurationId: v.string(),
    configuration: v.any(),
  },
  handler: async (ctx, args) => {
    // Check if configuration already exists
    const existing = await ctx.db
      .query("billingPortalConfigurations")
      .withIndex("by_configuration_id", (q) => q.eq("configurationId", args.configurationId))
      .first();

    if (existing) {
      // Update existing
      await ctx.db.patch(existing._id, {
        configuration: args.configuration,
        updatedAt: Date.now(),
      });
    } else {
      // Create new
      await ctx.db.insert("billingPortalConfigurations", {
        configurationId: args.configurationId,
        configuration: args.configuration,
        isActive: true,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
    }
  },
});

/**
 * Enhanced portal session creation with better error handling and Norwegian localization
 */
export const createEnhancedPortalSession = mutation({
  args: {
    userId: v.string(),
    returnUrl: v.optional(v.string()),
    locale: v.optional(v.string()),
    flowData: v.optional(v.object({
      type: v.union(
        v.literal("subscription_cancel"),
        v.literal("subscription_update"),
        v.literal("payment_method_update"),
        v.literal("invoice_history")
      ),
      after_completion: v.optional(v.object({
        type: v.literal("redirect"),
        redirect: v.object({
          return_url: v.string(),
        }),
      })),
    })),
  },
  handler: async (ctx, args) => {
    console.log('🔥 createEnhancedPortalSession called with:', args);

    // Get user's subscription using the same logic as existing portal
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check team subscription
    if (!subscription) {
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user?.companyId) {
        const companySubscription = await ctx.db
          .query("subscriptions")
          .withIndex("by_company", (q) => q.eq("companyId", user.companyId))
          .first();

        if (companySubscription && (user.role === 'administrator' || user.role === 'daglig_leder')) {
          subscription = companySubscription;
        }
      }
    }

    if (!subscription) {
      throw new Error("Ingen abonnement funnet. Vennligst start en prøveperiode først.");
    }

    if (!subscription.stripeCustomerId) {
      throw new Error("Stripe kunde-ID mangler. Kontakt support for hjelp.");
    }

    // Create portal session request record for tracking
    const portalRequestId = await ctx.db.insert("billingPortalSessions", {
      userId: args.userId,
      subscriptionId: subscription._id,
      stripeCustomerId: subscription.stripeCustomerId,
      requestedFlow: args.flowData?.type,
      returnUrl: args.returnUrl,
      locale: args.locale || 'nb',
      status: 'pending',
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Schedule portal session creation
    await ctx.scheduler.runAfter(0, internal.billingPortal.processPortalSessionCreation, {
      portalRequestId,
    });

    return {
      portalRequestId,
      status: 'initiated',
      message: 'Faktureringsportal åpnes om et øyeblikk...',
    };
  },
});

/**
 * Process portal session creation with Stripe
 */
export const processPortalSessionCreation = internalAction({
  args: {
    portalRequestId: v.id("billingPortalSessions"),
  },
  handler: async (ctx, args) => {
    const portalRequest = await ctx.runQuery(internal.billingPortal.getPortalRequest, {
      portalRequestId: args.portalRequestId,
    });

    if (!portalRequest) {
      throw new Error("Portal request not found");
    }

    try {
      // Update status to processing
      await ctx.runMutation(internal.billingPortal.updatePortalRequestStatus, {
        portalRequestId: args.portalRequestId,
        status: 'processing',
      });

      const stripe = new (await import('stripe')).default(process.env.STRIPE_SECRET_KEY!, {
        apiVersion: "2025-08-27.basil",
      });

      // Get active portal configuration
      const portalConfig = await ctx.runQuery(internal.billingPortal.getActivePortalConfiguration, {});

      // Prepare session parameters
      const sessionParams: any = {
        customer: portalRequest.stripeCustomerId,
        return_url: portalRequest.returnUrl || `${process.env.FRONTEND_URL}/subscription?portal_return=true`,
        locale: portalRequest.locale || 'nb',
      };

      // Add configuration if available
      if (portalConfig?.configurationId) {
        sessionParams.configuration = portalConfig.configurationId;
      }

      // Add flow data if specified
      if (portalRequest.requestedFlow) {
        sessionParams.flow_data = {
          type: portalRequest.requestedFlow,
          after_completion: {
            type: 'redirect',
            redirect: {
              return_url: portalRequest.returnUrl || `${process.env.FRONTEND_URL}/subscription?portal_return=true&flow=${portalRequest.requestedFlow}`,
            },
          },
        };
      }

      // Create Stripe portal session
      const session = await stripe.billingPortal.sessions.create(sessionParams);

      // Update portal request with success
      await ctx.runMutation(internal.billingPortal.updatePortalRequestStatus, {
        portalRequestId: args.portalRequestId,
        status: 'completed',
        portalUrl: session.url,
        stripeSessionId: session.id,
        completedAt: Date.now(),
      });

      console.log('✅ Enhanced portal session created successfully');

      return {
        success: true,
        url: session.url,
        sessionId: session.id,
      };

    } catch (error) {
      console.error('❌ Portal session creation failed:', error);

      // Update status to failed
      await ctx.runMutation(internal.billingPortal.updatePortalRequestStatus, {
        portalRequestId: args.portalRequestId,
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        completedAt: Date.now(),
      });

      throw error;
    }
  },
});

/**
 * Handle portal return and process any updates
 */
export const handlePortalReturn = mutation({
  args: {
    userId: v.string(),
    sessionId: v.optional(v.string()),
    flow: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log('🔥 handlePortalReturn called with:', args);

    // Find the most recent portal session for this user
    const portalSession = await ctx.db
      .query("billingPortalSessions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .first();

    if (!portalSession) {
      console.log('No portal session found for user');
      return { success: false, message: 'Ingen portal-økt funnet' };
    }

    // Update portal session with return information
    await ctx.db.patch(portalSession._id, {
      returnedAt: Date.now(),
      returnFlow: args.flow,
      returnSessionId: args.sessionId,
      updatedAt: Date.now(),
    });

    // Schedule subscription refresh to get latest data from Stripe
    await ctx.scheduler.runAfter(2000, internal.billingPortal.refreshSubscriptionData, {
      userId: args.userId,
      subscriptionId: portalSession.subscriptionId,
    });

    return {
      success: true,
      message: 'Portal-retur behandlet',
      flow: args.flow,
    };
  },
});

// Helper functions
export const getPortalRequest = internalQuery({
  args: {
    portalRequestId: v.id("billingPortalSessions"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.portalRequestId);
  },
});

export const updatePortalRequestStatus = internalMutation({
  args: {
    portalRequestId: v.id("billingPortalSessions"),
    status: v.string(),
    portalUrl: v.optional(v.string()),
    stripeSessionId: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
    completedAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.portalUrl) updates.portalUrl = args.portalUrl;
    if (args.stripeSessionId) updates.stripeSessionId = args.stripeSessionId;
    if (args.errorMessage) updates.errorMessage = args.errorMessage;
    if (args.completedAt) updates.completedAt = args.completedAt;

    await ctx.db.patch(args.portalRequestId, updates);
  },
});

export const getActivePortalConfiguration = internalQuery({
  args: {},
  handler: async (ctx, args) => {
    return await ctx.db
      .query("billingPortalConfigurations")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .first();
  },
});

export const refreshSubscriptionData = internalAction({
  args: {
    userId: v.string(),
    subscriptionId: v.id("subscriptions"),
  },
  handler: async (ctx, args) => {
    // This would typically sync with Stripe to get the latest subscription data
    // For now, we'll just log that a refresh was requested
    console.log(`🔄 Refreshing subscription data for user ${args.userId}`);
    
    // In a full implementation, this would:
    // 1. Fetch latest subscription data from Stripe
    // 2. Update the local subscription record
    // 3. Trigger any necessary notifications
  },
});

/**
 * Get portal session history for user
 */
export const getPortalSessionHistory = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("billingPortalSessions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(10);
  },
});
