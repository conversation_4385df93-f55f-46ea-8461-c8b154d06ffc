/**
 * Scheduled functions (cron jobs) for JobbLogg
 * 
 * Handles periodic tasks like payment retry processing, dunning escalation,
 * subscription downgrade management, and system maintenance.
 */

import { cronJobs } from "convex/server";
import { internal } from "./_generated/api";

const crons = cronJobs();

// ===== PAYMENT AND SUBSCRIPTION MANAGEMENT =====

/**
 * Process payment retry attempts every hour
 * Handles automatic retry of failed payments based on configured schedules
 */
crons.interval(
  "process payment retries",
  { minutes: 60 }, // Every hour
  internal.subscriptions.processPendingPaymentRetries
);

/**
 * Process dunning escalations every 6 hours
 * Escalates dunning attempts and sends communications based on schedules
 */
crons.interval(
  "process dunning escalations",
  { hours: 6 }, // Every 6 hours
  internal.subscriptions.processPendingDunningEscalations
);

/**
 * Process subscription downgrade escalations daily
 * Moves subscriptions through downgrade stages (grace → limited → suspended)
 */
crons.daily(
  "process downgrade escalations",
  { hourUTC: 9, minuteUTC: 0 }, // 9:00 AM UTC (10:00 AM CET)
  internal.subscriptionDowngrade.processPendingDowngradeEscalations
);

/**
 * Clean up expired data weekly
 * Removes old webhook events, expired tokens, and other temporary data
 */
crons.weekly(
  "cleanup expired data",
  { dayOfWeek: "sunday", hourUTC: 2, minuteUTC: 0 }, // Sunday 2:00 AM UTC
  internal.maintenance.cleanupExpiredData
);

// ===== TRIAL AND SUBSCRIPTION REMINDERS =====

/**
 * Send trial reminder emails daily
 * Checks for trials approaching expiration and sends appropriate reminders
 */
crons.daily(
  "send trial reminders",
  { hourUTC: 10, minuteUTC: 0 }, // 10:00 AM UTC (11:00 AM CET)
  internal.subscriptions.sendTrialReminders
);

/**
 * Process subscription status updates daily
 * Updates subscription statuses, handles trial expirations, and manages grace periods
 */
crons.daily(
  "update subscription statuses",
  { hourUTC: 8, minuteUTC: 0 }, // 8:00 AM UTC (9:00 AM CET)
  internal.subscriptions.updateSubscriptionStatuses
);

// ===== MONITORING AND HEALTH CHECKS =====

/**
 * Generate system health report daily
 * Monitors system performance, error rates, and key metrics
 */
crons.daily(
  "system health check",
  { hourUTC: 7, minuteUTC: 0 }, // 7:00 AM UTC (8:00 AM CET)
  internal.monitoring.generateHealthReport
);

/**
 * Monitor payment failure rates hourly
 * Tracks payment failure patterns and alerts on unusual activity
 */
crons.interval(
  "monitor payment failures",
  { hours: 1 }, // Every hour
  internal.monitoring.monitorPaymentFailures
);

export default crons;
