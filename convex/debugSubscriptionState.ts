import { action, query } from './_generated/server';
import { v } from 'convex/values';
import { internal } from './_generated/api';

/**
 * Debug action to check comprehensive subscription state
 * This helps identify where the state synchronization is failing
 */
export const debugSubscriptionState = action({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    console.log('🔍 [DEBUG] Starting comprehensive subscription state check for user:', args.userId);

    try {
      // 1. Check raw subscription record
      const rawSubscription = await ctx.runQuery(internal.subscriptions.getUserSubscriptionInternal, {
        userId: args.userId
      });

      // 2. Check subscription status (the computed status)
      const subscriptionStatus = await ctx.runQuery(internal.subscriptions.getSubscriptionStatusInternal, {
        userId: args.userId
      });

      // 3. Check user record
      const user = await ctx.runQuery(internal.subscriptions.getUserByClerkId, {
        clerkUserId: args.userId
      });

      // 4. Get current timestamp for comparison
      const currentTime = Date.now();

      const debugInfo = {
        timestamp: new Date().toISOString(),
        currentTime,
        userId: args.userId,
        
        // Raw subscription data
        rawSubscription: rawSubscription ? {
          _id: rawSubscription._id,
          status: rawSubscription.status,
          stripeSubscriptionId: rawSubscription.stripeSubscriptionId,
          stripeCustomerId: rawSubscription.stripeCustomerId,
          planLevel: rawSubscription.planLevel,
          billingInterval: rawSubscription.billingInterval,
          trialStart: rawSubscription.trialStart,
          trialEnd: rawSubscription.trialEnd,
          trialConvertedAt: rawSubscription.trialConvertedAt,
          createdAt: rawSubscription.createdAt,
          updatedAt: rawSubscription.updatedAt,
        } : null,

        // Computed subscription status
        subscriptionStatus: subscriptionStatus ? {
          hasSubscription: subscriptionStatus.hasSubscription,
          hasActiveSubscription: subscriptionStatus.hasActiveSubscription,
          isInTrial: subscriptionStatus.isInTrial,
          isTrialExpired: subscriptionStatus.isTrialExpired,
          isInGracePeriod: subscriptionStatus.isInGracePeriod,
          needsUpgrade: subscriptionStatus.needsUpgrade,
          canCreateProjects: subscriptionStatus.canCreateProjects,
          canAccessProjects: subscriptionStatus.canAccessProjects,
          hasFullAccess: subscriptionStatus.hasFullAccess,
          isReadOnly: subscriptionStatus.isReadOnly,
        } : null,

        // User data
        user: user ? {
          _id: user._id,
          role: user.role,
          subscriptionStatus: user.subscriptionStatus,
          trialEndsAt: user.trialEndsAt,
          hasActiveSubscription: user.hasActiveSubscription,
        } : null,

        // Time calculations
        timeCalculations: rawSubscription ? {
          trialEndDate: rawSubscription.trialEnd ? new Date(rawSubscription.trialEnd).toISOString() : null,
          trialEndTimestamp: rawSubscription.trialEnd,
          currentTimestamp: currentTime,
          isTrialExpiredByTime: rawSubscription.trialEnd ? currentTime >= rawSubscription.trialEnd : false,
          timeUntilExpiration: rawSubscription.trialEnd ? rawSubscription.trialEnd - currentTime : null,
          minutesUntilExpiration: rawSubscription.trialEnd ? Math.floor((rawSubscription.trialEnd - currentTime) / (60 * 1000)) : null,
        } : null,

        // Analysis
        analysis: {
          hasRawSubscription: !!rawSubscription,
          hasComputedStatus: !!subscriptionStatus,
          hasUser: !!user,
          statusMismatch: rawSubscription && subscriptionStatus ? 
            rawSubscription.status !== (subscriptionStatus.hasActiveSubscription ? 'active' : 
              subscriptionStatus.isInTrial ? 'trialing' : 'unknown') : false,
          shouldShowTrialBanner: subscriptionStatus ? 
            (subscriptionStatus.isInTrial || subscriptionStatus.isTrialExpired) && !subscriptionStatus.hasActiveSubscription : false,
        }
      };

      console.log('🔍 [DEBUG] Subscription state analysis:', debugInfo);
      return debugInfo;

    } catch (error) {
      console.error('❌ [DEBUG] Error checking subscription state:', error);
      return {
        error: error.message,
        timestamp: new Date().toISOString(),
        userId: args.userId,
      };
    }
  }
});

/**
 * Simple query to get current subscription state for debugging
 */
export const getSubscriptionDebugInfo = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    return {
      subscription: subscription ? {
        _id: subscription._id,
        status: subscription.status,
        planLevel: subscription.planLevel,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        updatedAt: subscription.updatedAt,
      } : null,
      user: user ? {
        _id: user._id,
        subscriptionStatus: user.subscriptionStatus,
        hasActiveSubscription: user.hasActiveSubscription,
        trialEndsAt: user.trialEndsAt,
      } : null,
      currentTime: Date.now(),
    };
  }
});
