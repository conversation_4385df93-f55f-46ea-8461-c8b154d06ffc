import { mutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * DIRECT FIX: Update subscription status directly via mutation
 */
export const fixSubscriptionStatus = mutation({
  args: { 
    userId: v.string(),
    reason: v.string()
  },
  handler: async (ctx, args) => {
    console.log(`🚨 DIRECT FIX: Updating subscription for user ${args.userId}`);
    console.log(`Reason: ${args.reason}`);
    
    try {
      // Get subscription
      const subscription = await ctx.db
        .query("subscriptions")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .first();
      
      if (!subscription) {
        return {
          success: false,
          error: 'No subscription found',
          userId: args.userId,
        };
      }
      
      console.log('📊 Current subscription:', {
        status: subscription.status,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
      });
      
      const now = Date.now();
      
      if (subscription.status === 'trialing') {
        // Update subscription
        await ctx.db.patch(subscription._id, {
          status: 'active',
          trialConvertedAt: now,
          updatedAt: now,
        });
        
        // Update user
        const user = await ctx.db
          .query("users")
          .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
          .first();
        
        if (user) {
          await ctx.db.patch(user._id, {
            subscriptionStatus: 'active',
            trialConvertedAt: now,
            hasCompletedTrial: true,
            updatedAt: now,
          });
        }
        
        console.log('✅ FIXED! Subscription and user updated to active');
        
        return {
          success: true,
          action: 'updated_to_active',
          subscription: {
            _id: subscription._id,
            oldStatus: 'trialing',
            newStatus: 'active',
            trialConvertedAt: now,
          },
          user: user ? {
            _id: user._id,
            subscriptionStatus: 'active',
            trialConvertedAt: now,
          } : null,
          timestamp: now,
          reason: args.reason,
        };
      } else {
        return {
          success: true,
          action: 'no_update_needed',
          message: `Subscription is already ${subscription.status}`,
          timestamp: now,
        };
      }
      
    } catch (error) {
      console.error('❌ Direct fix failed:', error);
      return {
        success: false,
        error: error.message,
        userId: args.userId,
        timestamp: Date.now(),
      };
    }
  }
});
