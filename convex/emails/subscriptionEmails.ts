import { v } from "convex/values";
import { internalMutation } from "../_generated/server";

// Send subscription-related email notifications
export const sendSubscriptionEmail = internalMutation({
  args: {
    userId: v.string(),
    type: v.union(
      v.literal("trial_reminder_day_3"),
      v.literal("trial_reminder_day_5"),
      v.literal("trial_reminder_24h"),
      v.literal("trial_expired"),
      v.literal("payment_failed"),
      v.literal("payment_failed_card_declined"),
      v.literal("payment_failed_insufficient_funds"),
      v.literal("payment_failed_expired_card"),
      v.literal("payment_failed_authentication_required"),
      v.literal("payment_retry_exhausted"),
      v.literal("subscription_suspended"),
      v.literal("subscription_activated"),
      v.literal("subscription_canceled")
    ),
    userEmail: v.string(),
    userName: v.string(),
    metadata: v.optional(v.object({
      daysLeft: v.optional(v.number()),
      planName: v.optional(v.string()),
      amount: v.optional(v.string()),
      failureReason: v.optional(v.string()),
      declineCode: v.optional(v.string()),
      attemptNumber: v.optional(v.number()),
      maxAttempts: v.optional(v.number()),
      nextRetryAt: v.optional(v.number()),
      billingPortalUrl: v.optional(v.string()),
      invoiceUrl: v.optional(v.string()),
      lastFourDigits: v.optional(v.string()),
    })),
  },
  handler: async (_ctx, args) => {
    const emailTemplate = getEmailTemplate(args.type, args.userName, args.metadata);
    
    if (!emailTemplate) {
      console.error("No email template found for type:", args.type);
      return { success: false, error: "No template found" };
    }

    try {
      // TODO: Integrate with actual email service (Resend, SendGrid, etc.)
      console.log(`📧 Sending ${args.type} email to ${args.userEmail}`);
      console.log("Subject:", emailTemplate.subject);
      console.log("Message:", emailTemplate.message);

      // For now, just log the email content
      // In production, you would send the actual email here
      
      return { success: true };
    } catch (error) {
      console.error("Failed to send subscription email:", error);
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
    }
  },
});

// Get email template based on type
function getEmailTemplate(
  type: string,
  userName: string,
  metadata?: {
    daysLeft?: number;
    planName?: string;
    amount?: string;
    failureReason?: string;
    declineCode?: string;
    attemptNumber?: number;
    maxAttempts?: number;
    nextRetryAt?: number;
    billingPortalUrl?: string;
    invoiceUrl?: string;
    lastFourDigits?: string;
  }
) {
  const baseUrl = process.env.CONVEX_SITE_URL || "https://jobblogg.no";
  const dashboardUrl = `${baseUrl}/dashboard`;
  const upgradeUrl = `${baseUrl}/upgrade`;
  const billingUrl = metadata?.billingPortalUrl || `${baseUrl}/subscription`;

  switch (type) {
    case "trial_reminder_day_3":
      return {
        subject: "⏰ 4 dager igjen av gratis prøveperioden - JobbLogg",
        message: `Hei ${userName},

Du har 4 dager igjen av din gratis prøveperiode på JobbLogg.

Så langt har du fått teste alle våre funksjoner uten kostnad. For å fortsette å bruke JobbLogg etter prøveperioden, må du velge et abonnement.

Våre planer (ekskl. mva):
• Liten bedrift (1-9 ansatte): 299 kr/mnd + mva = 374 kr/mnd
• Mellomstor bedrift (10-49 ansatte): 999 kr/mnd + mva = 1249 kr/mnd
• Stor bedrift (50-249 ansatte): 2999 kr/mnd + mva = 3749 kr/mnd

Velg abonnement: ${upgradeUrl}

Har du spørsmål? Svar på denne e-posten så hjelper vi deg.

Mvh,
JobbLogg teamet

---
Administrer abonnement: ${dashboardUrl}`
      };

    case "trial_reminder_day_5":
      return {
        subject: "⚠️ 2 dager igjen av gratis prøveperioden - JobbLogg",
        message: `Hei ${userName},

Du har kun 2 dager igjen av din gratis prøveperiode på JobbLogg.

For å unngå avbrudd i tjenesten, oppgrader til et betalt abonnement i dag.

Våre planer starter fra kun 299 kr/mnd for små bedrifter.

Oppgrader nå: ${upgradeUrl}

Trenger du hjelp med å velge riktig plan? Svar på denne e-posten så hjelper vi deg.

Mvh,
JobbLogg teamet

---
Administrer abonnement: ${dashboardUrl}`
      };

    case "trial_reminder_24h":
      return {
        subject: "🚨 24 timer igjen av gratis prøveperioden - JobbLogg",
        message: `Hei ${userName},

Din gratis prøveperiode på JobbLogg utløper i morgen!

For å fortsette å bruke alle funksjoner, må du oppgradere til et betalt abonnement i dag.

Oppgrader umiddelbart: ${upgradeUrl}

Etter prøveperioden får du 3 dager med begrenset tilgang (kun lesing) før kontoen suspenderes.

Har du spørsmål? Ring oss på 123 45 678 eller svar på denne e-posten.

Mvh,
JobbLogg teamet

---
Administrer abonnement: ${dashboardUrl}`
      };

    case "trial_expired":
      return {
        subject: "Prøveperioden er utløpt - Oppgrader JobbLogg nå",
        message: `Hei ${userName},

Din gratis prøveperiode på JobbLogg er nå utløpt.

Du har 3 dager med begrenset tilgang (kun lesing av eksisterende prosjekter) før kontoen suspenderes.

Oppgrader nå for å fortsette: ${upgradeUrl}

Alle dine prosjekter og data er trygt lagret og vil være tilgjengelig umiddelbart etter oppgradering.

Trenger du hjelp? Kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet

---
Administrer abonnement: ${dashboardUrl}`
      };

    case "payment_failed":
      return {
        subject: "Betalingsfeil - Oppdater betalingsmetode - JobbLogg",
        message: `Hei ${userName},

Vi kunne ikke belaste din betalingsmetode for JobbLogg-abonnementet.

Oppdater betalingsmetoden din for å unngå avbrudd i tjenesten:
${dashboardUrl}

Hvis problemet ikke løses innen 3 dager, vil kontoen din bli suspendert.

Trenger du hjelp? Kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet`
      };

    case "subscription_activated":
      return {
        subject: "Velkommen til JobbLogg! Abonnementet ditt er aktivt",
        message: `Hei ${userName},

Takk for at du valgte JobbLogg! Ditt ${metadata?.planName || ''} abonnement er nå aktivt.

Du har nå full tilgang til alle funksjoner:
• Ubegrenset prosjekter
• Team samarbeid
• Kundeportaler
• Alle rapporter og eksporter

Kom i gang: ${dashboardUrl}

Trenger du hjelp med å komme i gang? Se vår guide eller kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet`
      };

    case "subscription_canceled":
      return {
        subject: "Abonnement kansellert - JobbLogg",
        message: `Hei ${userName},

Ditt JobbLogg-abonnement er nå kansellert.

Du har tilgang til kontoen din til ${metadata?.planName || 'slutten av faktureringsperioden'}.

Alle dine data vil bli bevart i 90 dager etter kansellering, så du kan reaktivere abonnementet når som helst.

Reaktiver abonnement: ${dashboardUrl}

Vi savner deg allerede! Hvis det er noe vi kan gjøre bedre, hør gjerne fra deg på <EMAIL>

Mvh,
JobbLogg teamet`
      };

    case "payment_failed_card_declined":
      return {
        subject: "Kortet ditt ble avvist - Oppdater betalingsmetode - JobbLogg",
        message: `Hei ${userName},

Vi kunne ikke belaste kortet ditt for JobbLogg-abonnementet (${metadata?.amount || 'ukjent beløp'}).

🔴 Årsak: Kortet ble avvist av banken din
${metadata?.lastFourDigits ? `💳 Kort: ****${metadata.lastFourDigits}` : ''}

Dette kan skyldes:
• Utilstrekkelig saldo
• Kortet er utløpt eller blokkert
• Banken din blokkerte transaksjonen

🔧 Løsning:
1. Sjekk at kortet ditt er gyldig og har tilstrekkelig saldo
2. Oppdater betalingsmetoden din: ${billingUrl}
3. Kontakt banken din hvis problemet vedvarer

${metadata?.nextRetryAt ? `⏰ Vi prøver automatisk igjen ${new Date(metadata.nextRetryAt).toLocaleDateString('nb-NO', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' })}` : ''}

Trenger du hjelp? Kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet

---
Administrer betalingsmetoder: ${billingUrl}`
      };

    case "payment_failed_insufficient_funds":
      return {
        subject: "Utilstrekkelig saldo - Oppdater betalingsmetode - JobbLogg",
        message: `Hei ${userName},

Vi kunne ikke belaste kortet ditt for JobbLogg-abonnementet (${metadata?.amount || 'ukjent beløp'}).

🔴 Årsak: Utilstrekkelig saldo på kontoen
${metadata?.lastFourDigits ? `💳 Kort: ****${metadata.lastFourDigits}` : ''}

🔧 Løsning:
1. Sett inn penger på kontoen din
2. Eller oppdater til et annet kort: ${billingUrl}

${metadata?.nextRetryAt ? `⏰ Vi prøver automatisk igjen ${new Date(metadata.nextRetryAt).toLocaleDateString('nb-NO', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' })}` : ''}

Alle dine prosjekter og data er trygt lagret mens vi løser dette.

Trenger du hjelp? Kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet

---
Administrer betalingsmetoder: ${billingUrl}`
      };

    case "payment_failed_expired_card":
      return {
        subject: "Kortet ditt er utløpt - Oppdater betalingsmetode - JobbLogg",
        message: `Hei ${userName},

Vi kunne ikke belaste kortet ditt for JobbLogg-abonnementet (${metadata?.amount || 'ukjent beløp'}).

🔴 Årsak: Kortet ditt er utløpt
${metadata?.lastFourDigits ? `💳 Utløpt kort: ****${metadata.lastFourDigits}` : ''}

🔧 Løsning:
Oppdater til ditt nye kort umiddelbart: ${billingUrl}

${metadata?.nextRetryAt ? `⏰ Vi prøver automatisk igjen ${new Date(metadata.nextRetryAt).toLocaleDateString('nb-NO', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' })}` : ''}

💡 Tips: Aktiver automatisk kortoppdatering hos banken din for å unngå dette i fremtiden.

Trenger du hjelp? Kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet

---
Administrer betalingsmetoder: ${billingUrl}`
      };

    case "payment_failed_authentication_required":
      return {
        subject: "Bekreftelse kreves - Fullfør betaling - JobbLogg",
        message: `Hei ${userName},

Betalingen din for JobbLogg-abonnementet krever ekstra bekreftelse (3D Secure).

🔐 Årsak: Banken din krever bekreftelse for sikkerheten din
${metadata?.lastFourDigits ? `💳 Kort: ****${metadata.lastFourDigits}` : ''}
💰 Beløp: ${metadata?.amount || 'ukjent beløp'}

🔧 Løsning:
1. Gå til betalingsportalen: ${billingUrl}
2. Fullfør bekreftelsen med banken din
3. Eller oppdater til et annet kort

${metadata?.invoiceUrl ? `📄 Se faktura: ${metadata.invoiceUrl}` : ''}

Trenger du hjelp? Kontakt oss på <EMAIL>

Mvh,
JobbLogg teamet

---
Administrer betalingsmetoder: ${billingUrl}`
      };

    case "payment_retry_exhausted":
      return {
        subject: "⚠️ Viktig: Betalingsproblem må løses nå - JobbLogg",
        message: `Hei ${userName},

Vi har prøvd å belaste kortet ditt flere ganger uten hell (${metadata?.attemptNumber || 'flere'} forsøk).

🔴 Status: Alle automatiske forsøk er brukt opp
💳 Beløp: ${metadata?.amount || 'ukjent beløp'}
📅 Siste forsøk: ${metadata?.failureReason || 'Ukjent feil'}

⚠️ VIKTIG: Kontoen din vil bli suspendert hvis ikke betalingen løses innen 24 timer.

🔧 Løs dette nå:
1. Oppdater betalingsmetoden din: ${billingUrl}
2. Eller kontakt banken din for å løse problemet
3. Ring oss på 123 45 678 hvis du trenger hjelp

Alle dine prosjekter og data er trygt lagret og vil være tilgjengelig umiddelbart etter at betalingen er løst.

Mvh,
JobbLogg teamet

---
Administrer betalingsmetoder: ${billingUrl}
Kontakt support: <EMAIL>`
      };

    case "subscription_suspended":
      return {
        subject: "🚨 Konto suspendert - Reaktiver JobbLogg nå",
        message: `Hei ${userName},

Din JobbLogg-konto er suspendert på grunn av utestående betaling.

🔴 Status: Konto suspendert
💳 Utestående beløp: ${metadata?.amount || 'Se faktura'}
📅 Suspendert: ${new Date().toLocaleDateString('nb-NO')}

🔧 Reaktiver kontoen din:
1. Oppdater betalingsmetoden din: ${billingUrl}
2. Betal utestående faktura
3. Kontoen reaktiveres automatisk etter betaling

📊 Dine data:
• Alle prosjekter og data er trygt lagret
• Ingen data går tapt under suspendering
• Full tilgang gjenopprettes umiddelbart etter betaling

Trenger du hjelp? Ring oss på 123 45 678 <NAME_EMAIL>

Mvh,
JobbLogg teamet

---
Reaktiver konto: ${billingUrl}
Kontakt support: <EMAIL>`
      };

    default:
      return null;
  }
}
