import { httpRouter } from "convex/server";
import { internal } from "./_generated/api";
import { httpAction } from "./_generated/server";
import { handleStripeWebhook } from "./stripe/webhooks";

// Create HTTP router
const http = httpRouter();

// Simple test endpoint
http.route({
  path: "/test",
  method: "GET", 
  handler: httpAction(async () => {
    console.log("🧪 Simple test endpoint called!");
    return new Response("Simple test works!", {
      status: 200,
      headers: {
        "Content-Type": "text/plain",
      },
    });
  }),
});

// Health check
http.route({
  path: "/health",
  method: "GET",
  handler: httpAction(async () => {
    console.log("🏥 Health check called!");
    return new Response(JSON.stringify({
      status: "healthy",
      timestamp: new Date().toISOString(),
      service: "JobbLogg Webhook Service"
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  }),
});

// Clerk webhook handler (simplified for testing)
http.route({
  path: "/clerk/webhook",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    console.log('🔗 Clerk webhook called!');
    
    try {
      // Get headers
      const svix_id = request.headers.get("svix-id");
      const svix_timestamp = request.headers.get("svix-timestamp");
      const svix_signature = request.headers.get("svix-signature");
      
      console.log('📋 Webhook headers:', {
        svix_id: svix_id ? 'present' : 'missing',
        svix_timestamp: svix_timestamp ? 'present' : 'missing', 
        svix_signature: svix_signature ? 'present' : 'missing'
      });
      
      const body = await request.json();
      const { type, data } = body;
      
      console.log('📨 Webhook event:', { type, userId: data?.id });
      
      // Handle user creation
      if (type === 'user.created') {
        const result = await ctx.runMutation(internal.webhooks.createUserFromClerk, {
          clerkUserId: data.id,
          email: data.email_addresses?.[0]?.email_address || '',
          firstName: data.first_name || '',
          lastName: data.last_name || '',
        });
        console.log('✅ User created:', result?._id);
      }
      
      return new Response('Webhook processed successfully', {
        status: 200,
        headers: { 'Content-Type': 'text/plain' },
      });
      
    } catch (error) {
      console.error('❌ Webhook error:', error);
      return new Response('Webhook processing failed', {
        status: 500,
        headers: { 'Content-Type': 'text/plain' }
      });
    }
  }),
});

// Stripe webhook route (Convex HTTP Actions live at .convex.site)
http.route({
  path: "/stripe",
  method: "POST",
  handler: handleStripeWebhook,
});

// Keep a test endpoint for simple health checks

// Removed deprecated routes: /payments/webhook and /stripe/webhooks/testStripeWebhook

export default http;
