import { v } from "convex/values";
import { action, mutation, query } from "./_generated/server";
import { api } from "./_generated/api";

/**
 * OpenGraph metadata interface
 */
export interface OpenGraphData {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  siteName?: string;
  type?: string;
  domain: string;
  favicon?: string;
}

/**
 * Cached link preview data
 */
export interface LinkPreview extends OpenGraphData {
  _id: string;
  _creationTime: number;
  cachedAt: number;
  expiresAt: number;
  fetchError?: string;
}

/**
 * Fetch OpenGraph metadata from a URL
 * This is an action because it makes external HTTP requests
 */
export const fetchOpenGraphData = action({
  args: { url: v.string() },
  handler: async (_ctx, { url }): Promise<OpenGraphData | null> => {
    try {
      // Validate URL
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        throw new Error('Invalid protocol');
      }

      // Fetch the webpage
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'JobbLogg-LinkPreview/1.0 (+https://jobblogg.no)',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'nb-NO,nb;q=0.9,no;q=0.8,en;q=0.7',
          'Accept-Encoding': 'gzip, deflate',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        // Set timeout to prevent hanging
        signal: AbortSignal.timeout(10000), // 10 seconds
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type') || '';
      if (!contentType.includes('text/html')) {
        throw new Error('Not an HTML page');
      }

      const html = await response.text();
      
      // Parse OpenGraph and meta tags
      const ogData = parseOpenGraphData(html, url);
      
      return ogData;
    } catch (error) {
      console.error('Failed to fetch OpenGraph data:', error);
      return null;
    }
  },
});

/**
 * Parse OpenGraph data from HTML content
 */
function parseOpenGraphData(html: string, url: string): OpenGraphData {
  const urlObj = new URL(url);
  const domain = urlObj.hostname.replace(/^www\./, '');
  
  const ogData: OpenGraphData = {
    url,
    domain,
  };

  // Helper function to extract content from meta tags
  const extractMetaContent = (property: string, attribute: 'property' | 'name' = 'property'): string | undefined => {
    const regex = new RegExp(`<meta\\s+${attribute}=["']${property}["']\\s+content=["']([^"']*?)["'][^>]*>`, 'i');
    const match = html.match(regex);
    return match ? match[1].trim() : undefined;
  };

  // Extract OpenGraph data
  ogData.title = extractMetaContent('og:title') || 
                 extractMetaContent('title', 'name') ||
                 extractTitleFromHTML(html);
  
  ogData.description = extractMetaContent('og:description') || 
                       extractMetaContent('description', 'name');
  
  ogData.image = extractMetaContent('og:image');
  ogData.siteName = extractMetaContent('og:site_name');
  ogData.type = extractMetaContent('og:type');
  
  // Try to get favicon
  ogData.favicon = extractFavicon(html, url);

  // Clean up and validate image URL
  if (ogData.image) {
    ogData.image = resolveUrl(ogData.image, url);
  }

  return ogData;
}

/**
 * Extract title from HTML title tag if OpenGraph title is not available
 */
function extractTitleFromHTML(html: string): string | undefined {
  const titleMatch = html.match(/<title[^>]*>([^<]*)<\/title>/i);
  return titleMatch ? titleMatch[1].trim() : undefined;
}

/**
 * Extract favicon URL from HTML
 */
function extractFavicon(html: string, baseUrl: string): string | undefined {
  // Try different favicon patterns
  const faviconPatterns = [
    /<link[^>]*rel=["']icon["'][^>]*href=["']([^"']*?)["'][^>]*>/i,
    /<link[^>]*href=["']([^"']*?)["'][^>]*rel=["']icon["'][^>]*>/i,
    /<link[^>]*rel=["']shortcut icon["'][^>]*href=["']([^"']*?)["'][^>]*>/i,
    /<link[^>]*href=["']([^"']*?)["'][^>]*rel=["']shortcut icon["'][^>]*>/i,
  ];

  for (const pattern of faviconPatterns) {
    const match = html.match(pattern);
    if (match) {
      return resolveUrl(match[1], baseUrl);
    }
  }

  // Fallback to default favicon location
  const urlObj = new URL(baseUrl);
  return `${urlObj.protocol}//${urlObj.host}/favicon.ico`;
}

/**
 * Resolve relative URLs to absolute URLs
 */
function resolveUrl(url: string, baseUrl: string): string {
  try {
    return new URL(url, baseUrl).href;
  } catch {
    return url;
  }
}

/**
 * Store link preview data in the database with caching
 */
export const storeLinkPreview = mutation({
  args: {
    url: v.string(),
    ogData: v.optional(v.object({
      title: v.optional(v.string()),
      description: v.optional(v.string()),
      image: v.optional(v.string()),
      siteName: v.optional(v.string()),
      type: v.optional(v.string()),
      domain: v.string(),
      favicon: v.optional(v.string()),
    })),
    fetchError: v.optional(v.string()),
  },
  handler: async (ctx, { url, ogData, fetchError }) => {
    const now = Date.now();
    const expiresAt = now + (24 * 60 * 60 * 1000); // Cache for 24 hours

    return await ctx.db.insert("linkPreviews", {
      url,
      title: ogData?.title,
      description: ogData?.description,
      image: ogData?.image,
      siteName: ogData?.siteName,
      type: ogData?.type,
      domain: ogData?.domain || new URL(url).hostname.replace(/^www\./, ''),
      favicon: ogData?.favicon,
      cachedAt: now,
      expiresAt,
      fetchError,
    });
  },
});

/**
 * Get cached link preview data
 */
export const getLinkPreview = query({
  args: { url: v.string() },
  handler: async (ctx, { url }) => {
    const cached = await ctx.db
      .query("linkPreviews")
      .withIndex("by_url", (q) => q.eq("url", url))
      .first();

    // Return cached data if it exists and hasn't expired
    if (cached && cached.expiresAt > Date.now()) {
      return cached;
    }

    return null;
  },
});

/**
 * Get or fetch link preview data (combines caching with fetching)
 */
export const getOrFetchLinkPreview = action({
  args: { url: v.string() },
  handler: async (ctx, { url }): Promise<LinkPreview | null> => {
    // First check cache
    const cached = await ctx.runQuery(api.linkPreviews.getLinkPreview, { url });
    if (cached) {
      return cached;
    }

    // Fetch new data
    // TODO: Re-enable OG data fetching when type instantiation issue is resolved
    // const ogData = await ctx.runAction(api.linkPreviews.fetchOpenGraphData, { url });
    // const ogData = null; // Temporarily disable to avoid type issues

    // Store in cache (remove url field from ogData as it's not in the validator)
    // const cleanOgData = ogData ? {
    //   title: (ogData as any).title,
    //   description: (ogData as any).description,
    //   image: (ogData as any).image,
    //   siteName: (ogData as any).siteName,
    //   type: (ogData as any).type,
    //   domain: (ogData as any).domain,
    //   favicon: (ogData as any).favicon,
    // } : undefined;

    // Store the link preview data
    await ctx.runMutation(api.linkPreviews.storeLinkPreview, {
      url,
      ogData: cleanOgData,
      fetchError: ogData ? undefined : 'Failed to fetch OpenGraph data',
    });

    // Return the stored data
    return await ctx.runQuery(api.linkPreviews.getLinkPreview, { url });
  },
});


