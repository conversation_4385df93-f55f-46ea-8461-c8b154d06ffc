import { v } from "convex/values";
import { internalAction, internalQuery, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";

/**
 * Clean up expired data weekly
 * Removes old webhook events, expired tokens, and other temporary data
 */
export const cleanupExpiredData = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log('🧹 Starting weekly data cleanup...');
    
    try {
      const now = Date.now();
      const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = now - (30 * 24 * 60 * 60 * 1000);
      
      // Clean up old webhook events (older than 1 month)
      const oldWebhookEvents = await ctx.runQuery(internal.maintenance.getOldWebhookEvents, {
        cutoffTime: oneMonthAgo
      });
      
      let cleanedWebhookEvents = 0;
      for (const event of oldWebhookEvents) {
        await ctx.runMutation(internal.maintenance.deleteWebhookEvent, {
          eventId: event._id
        });
        cleanedWebhookEvents++;
      }
      
      // Clean up old email tracking records (older than 1 month)
      const oldEmailTracking = await ctx.runQuery(internal.maintenance.getOldEmailTracking, {
        cutoffTime: oneMonthAgo
      });
      
      let cleanedEmailTracking = 0;
      for (const record of oldEmailTracking) {
        await ctx.runMutation(internal.maintenance.deleteEmailTracking, {
          recordId: record._id
        });
        cleanedEmailTracking++;
      }
      
      // Clean up old notification history (older than 1 week)
      const oldNotifications = await ctx.runQuery(internal.maintenance.getOldNotifications, {
        cutoffTime: oneWeekAgo
      });
      
      let cleanedNotifications = 0;
      for (const notification of oldNotifications) {
        await ctx.runMutation(internal.maintenance.deleteNotification, {
          notificationId: notification._id
        });
        cleanedNotifications++;
      }
      
      console.log(`✅ Cleanup completed:
        - Webhook events: ${cleanedWebhookEvents}
        - Email tracking: ${cleanedEmailTracking}
        - Notifications: ${cleanedNotifications}`);
        
      return {
        success: true,
        cleaned: {
          webhookEvents: cleanedWebhookEvents,
          emailTracking: cleanedEmailTracking,
          notifications: cleanedNotifications
        }
      };
      
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
      throw error;
    }
  },
});

// Helper queries
export const getOldWebhookEvents = internalQuery({
  args: { cutoffTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("webhookEvents")
      .withIndex("by_timestamp", (q) => q.lt("timestamp", args.cutoffTime))
      .collect();
  },
});

export const getOldEmailTracking = internalQuery({
  args: { cutoffTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("emailTracking")
      .withIndex("by_timestamp", (q) => q.lt("sentAt", args.cutoffTime))
      .collect();
  },
});

export const getOldNotifications = internalQuery({
  args: { cutoffTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("notifications")
      .withIndex("by_timestamp", (q) => q.lt("createdAt", args.cutoffTime))
      .collect();
  },
});

// Helper mutations
export const deleteWebhookEvent = internalMutation({
  args: { eventId: v.id("webhookEvents") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.eventId);
  },
});

export const deleteEmailTracking = internalMutation({
  args: { recordId: v.id("emailTracking") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.recordId);
  },
});

export const deleteNotification = internalMutation({
  args: { notificationId: v.id("notifications") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.notificationId);
  },
});
