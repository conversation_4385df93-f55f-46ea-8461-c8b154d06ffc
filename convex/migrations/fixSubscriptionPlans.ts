import { v } from "convex/values";
import { internalAction, internalQuery, internalMutation, action } from "../_generated/server";
import { internal } from "../_generated/api";

/**
 * Helper function to get all subscriptions
 */
export const getAllSubscriptions = internalQuery({
  args: {},
  handler: async (ctx) => {
    return await ctx.db.query("subscriptions").collect();
  },
});

/**
 * Helper function to update subscription
 */
export const updateSubscription = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    planLevel: v.optional(v.string()),
    billingInterval: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.planLevel) {
      updates.planLevel = args.planLevel;
    }

    if (args.billingInterval) {
      updates.billingInterval = args.billingInterval;
    }

    await ctx.db.patch(args.subscriptionId, updates);
  },
});

/**
 * Helper function to get user subscription
 */
export const getUserSubscription = internalQuery({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();
  },
});

/**
 * Migration action to fix missing planLevel and billingInterval in existing subscriptions
 * This addresses users who completed onboarding before the plan selection was properly implemented
 */
export const fixMissingSubscriptionPlans = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log("🔧 Starting subscription plan migration...");

    try {
      // Get all subscriptions using internal query
      const subscriptions = await ctx.runQuery(internal.migrations.getAllSubscriptions, {});
      console.log(`📊 Found ${subscriptions.length} subscription(s) to check`);
      
      let fixedCount = 0;
      let skippedCount = 0;
      
      for (const subscription of subscriptions) {
        // Check if subscription is missing planLevel or billingInterval
        const needsFix = !subscription.planLevel || !subscription.billingInterval;
        
        if (needsFix) {
          console.log(`🔄 Fixing subscription for user ${subscription.userId}:`, {
            currentPlanLevel: subscription.planLevel || 'missing',
            currentBillingInterval: subscription.billingInterval || 'missing',
            status: subscription.status
          });
          
          // Set default values - basic plan with monthly billing
          // This is a reasonable default for trial users
          await ctx.runMutation(internal.migrations.fixSubscriptionPlans.updateSubscription, {
            subscriptionId: subscription._id,
            planLevel: subscription.planLevel || 'basic',
            billingInterval: subscription.billingInterval || 'month',
          });
          
          fixedCount++;
        } else {
          console.log(`✅ Subscription for user ${subscription.userId} already has correct plan data:`, {
            planLevel: subscription.planLevel,
            billingInterval: subscription.billingInterval
          });
          skippedCount++;
        }
      }
      
      console.log(`🎉 Migration completed:`, {
        total: subscriptions.length,
        fixed: fixedCount,
        skipped: skippedCount
      });
      
      return {
        success: true,
        total: subscriptions.length,
        fixed: fixedCount,
        skipped: skippedCount
      };
    } catch (error) {
      console.error("❌ Migration failed:", error);
      throw error;
    }
  },
});

/**
 * Check specific user's subscription data
 */
export const checkUserSubscription = internalAction({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const subscription = await ctx.runQuery(internal.migrations.fixSubscriptionPlans.getUserSubscription, {
      userId: args.userId
    });
    
    if (!subscription) {
      return { error: "No subscription found for user" };
    }
    
    return {
      userId: args.userId,
      subscriptionId: subscription._id,
      planLevel: subscription.planLevel,
      billingInterval: subscription.billingInterval,
      status: subscription.status,
      trialEnd: subscription.trialEnd,
      needsFix: !subscription.planLevel || !subscription.billingInterval
    };
  }
});

// Helper functions are defined above

export const updateSubscriptionPlan = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    planLevel: v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise")),
    billingInterval: v.union(v.literal("month"), v.literal("year")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.subscriptionId, {
      planLevel: args.planLevel,
      billingInterval: args.billingInterval,
      updatedAt: Date.now(),
    });
    
    console.log(`✅ Updated subscription ${args.subscriptionId} with plan: ${args.planLevel}, billing: ${args.billingInterval}`);
  },
});
