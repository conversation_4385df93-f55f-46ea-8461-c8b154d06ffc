import { v } from "convex/values";
import { internalAction, internalQuery, internalMutation } from "./_generated/server";
import { internal } from "./_generated/api";

/**
 * Generate system health report daily
 * Monitors system performance, error rates, and key metrics
 */
export const generateHealthReport = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log('📊 Generating daily health report...');
    
    try {
      const now = Date.now();
      const oneDayAgo = now - (24 * 60 * 60 * 1000);
      
      // Get system metrics
      const metrics = await ctx.runQuery(internal.monitoring.getSystemMetrics, {
        startTime: oneDayAgo,
        endTime: now
      });
      
      // Get error statistics
      const errorStats = await ctx.runQuery(internal.monitoring.getErrorStatistics, {
        startTime: oneDayAgo,
        endTime: now
      });
      
      // Get subscription statistics
      const subscriptionStats = await ctx.runQuery(internal.monitoring.getSubscriptionStatistics, {});
      
      // Get payment failure rates
      const paymentStats = await ctx.runQuery(internal.monitoring.getPaymentStatistics, {
        startTime: oneDayAgo,
        endTime: now
      });
      
      const healthReport = {
        timestamp: now,
        period: '24h',
        metrics,
        errorStats,
        subscriptionStats,
        paymentStats,
        status: 'healthy' // Will be determined based on thresholds
      };
      
      // Store the health report
      await ctx.runMutation(internal.monitoring.storeHealthReport, {
        report: healthReport
      });
      
      console.log('✅ Health report generated successfully');
      
      return healthReport;
      
    } catch (error) {
      console.error('❌ Error generating health report:', error);
      throw error;
    }
  },
});

/**
 * Monitor payment failure rates hourly
 * Tracks payment failure patterns and alerts on unusual activity
 */
export const monitorPaymentFailures = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log('💳 Monitoring payment failures...');
    
    try {
      const now = Date.now();
      const oneHourAgo = now - (60 * 60 * 1000);
      
      // Get recent payment failures
      const recentFailures = await ctx.runQuery(internal.monitoring.getRecentPaymentFailures, {
        startTime: oneHourAgo,
        endTime: now
      });
      
      // Calculate failure rate
      const totalPayments = await ctx.runQuery(internal.monitoring.getTotalPayments, {
        startTime: oneHourAgo,
        endTime: now
      });
      
      const failureRate = totalPayments > 0 ? (recentFailures.length / totalPayments) * 100 : 0;
      
      // Check if failure rate exceeds threshold (e.g., 10%)
      const threshold = 10;
      const isAnomalous = failureRate > threshold;
      
      const monitoringResult = {
        timestamp: now,
        period: '1h',
        totalPayments,
        totalFailures: recentFailures.length,
        failureRate,
        threshold,
        isAnomalous,
        failures: recentFailures
      };
      
      // Store monitoring result
      await ctx.runMutation(internal.monitoring.storePaymentMonitoring, {
        result: monitoringResult
      });
      
      if (isAnomalous) {
        console.log(`⚠️ High payment failure rate detected: ${failureRate.toFixed(2)}%`);
        // Could trigger alerts here
      }
      
      console.log(`✅ Payment monitoring completed. Failure rate: ${failureRate.toFixed(2)}%`);
      
      return monitoringResult;
      
    } catch (error) {
      console.error('❌ Error monitoring payment failures:', error);
      throw error;
    }
  },
});

// Helper queries
export const getSystemMetrics = internalQuery({
  args: { startTime: v.number(), endTime: v.number() },
  handler: async (ctx, args) => {
    // Get basic system metrics
    const totalUsers = await ctx.db.query("users").collect().then(users => users.length);
    const totalProjects = await ctx.db.query("projects").collect().then(projects => projects.length);
    const totalSubscriptions = await ctx.db.query("subscriptions").collect().then(subs => subs.length);
    
    return {
      totalUsers,
      totalProjects,
      totalSubscriptions,
      timestamp: Date.now()
    };
  },
});

export const getErrorStatistics = internalQuery({
  args: { startTime: v.number(), endTime: v.number() },
  handler: async (ctx, args) => {
    // Get error statistics from webhook error logs
    const errors = await ctx.db
      .query("webhookErrorLogs")
      .withIndex("by_timestamp", (q) => 
        q.gte("timestamp", args.startTime).lte("timestamp", args.endTime)
      )
      .collect();
    
    return {
      totalErrors: errors.length,
      errorsByType: errors.reduce((acc, error) => {
        acc[error.errorType || 'unknown'] = (acc[error.errorType || 'unknown'] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };
  },
});

export const getSubscriptionStatistics = internalQuery({
  args: {},
  handler: async (ctx) => {
    const subscriptions = await ctx.db.query("subscriptions").collect();
    
    const stats = {
      total: subscriptions.length,
      byStatus: subscriptions.reduce((acc, sub) => {
        acc[sub.status] = (acc[sub.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      byPlan: subscriptions.reduce((acc, sub) => {
        acc[sub.planLevel || 'unknown'] = (acc[sub.planLevel || 'unknown'] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };
    
    return stats;
  },
});

export const getPaymentStatistics = internalQuery({
  args: { startTime: v.number(), endTime: v.number() },
  handler: async (ctx, args) => {
    // Get payment retry attempts in the time period
    const retryAttempts = await ctx.db
      .query("paymentRetryAttempts")
      .withIndex("by_timestamp", (q) => 
        q.gte("createdAt", args.startTime).lte("createdAt", args.endTime)
      )
      .collect();
    
    return {
      totalRetryAttempts: retryAttempts.length,
      successfulRetries: retryAttempts.filter(r => r.status === 'succeeded').length,
      failedRetries: retryAttempts.filter(r => r.status === 'failed').length
    };
  },
});

export const getRecentPaymentFailures = internalQuery({
  args: { startTime: v.number(), endTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("paymentRetryAttempts")
      .withIndex("by_timestamp", (q) => 
        q.gte("createdAt", args.startTime).lte("createdAt", args.endTime)
      )
      .filter((q) => q.eq(q.field("status"), "failed"))
      .collect();
  },
});

export const getTotalPayments = internalQuery({
  args: { startTime: v.number(), endTime: v.number() },
  handler: async (ctx, args) => {
    const payments = await ctx.db
      .query("paymentRetryAttempts")
      .withIndex("by_timestamp", (q) => 
        q.gte("createdAt", args.startTime).lte("createdAt", args.endTime)
      )
      .collect();
    
    return payments.length;
  },
});

// Helper mutations
export const storeHealthReport = internalMutation({
  args: { report: v.any() },
  handler: async (ctx, args) => {
    // Store health report in a monitoring table (would need to add to schema)
    console.log('Health report stored:', args.report);
  },
});

export const storePaymentMonitoring = internalMutation({
  args: { result: v.any() },
  handler: async (ctx, args) => {
    // Store payment monitoring result (would need to add to schema)
    console.log('Payment monitoring result stored:', args.result);
  },
});
