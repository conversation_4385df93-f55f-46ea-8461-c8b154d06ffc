import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
// import { internal } from './_generated/api'; // TODO: Re-enable when type instantiation issues are resolved
import { nanoid } from 'nanoid';

// Generate upload URL for file storage
export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  }
});

// Debug query to see all projects in database (temporary)
export const debugGetAllProjects = query({
  args: {},
  handler: async (ctx) => {
    const allProjects = await ctx.db.query("projects").collect();
    console.log("All projects in database:", allProjects.length);
    console.log("Raw project data:", allProjects);
    return allProjects.map(p => ({
      _id: p._id,
      name: p.name,
      userId: p.userId,
      createdAt: p.createdAt,
      isArchived: p.isArchived,
      hasIsArchivedField: Object.prototype.hasOwnProperty.call(p, 'isArchived'),
      isArchivedValue: p.isArchived,
      isArchivedType: typeof p.isArchived
    }));
  }
});

// Migration function to fix existing projects without isArchived field
export const fixExistingProjects = mutation({
  args: {},
  handler: async (ctx) => {
    const allProjects = await ctx.db.query("projects").collect();
    let fixedCount = 0;

    for (const project of allProjects) {
      if (project.isArchived === undefined) {
        await ctx.db.patch(project._id, {
          isArchived: false
        });
        fixedCount++;
      }
    }

    console.log(`Fixed ${fixedCount} projects without isArchived field`);
    return { fixedCount, totalProjects: allProjects.length };
  }
});

// Debug version of getByUserWithCustomers to test exact same logic
export const debugGetByUserWithCustomers = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    console.log("DEBUG: Querying projects for userId:", args.userId);

    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
      .order("desc")
      .collect();

    console.log("DEBUG: Found projects:", projects.length);
    console.log("DEBUG: Project details:", projects.map(p => ({
      _id: p._id,
      name: p.name,
      userId: p.userId,
      isArchived: p.isArchived,
      customerId: p.customerId
    })));

    // Fetch customer data for each project
    const projectsWithCustomers = await Promise.all(
      projects.map(async (project) => {
        if (project.customerId) {
          const customer = await ctx.db.get(project.customerId);
          console.log(`DEBUG: Customer for project ${project.name}:`, {
            customerId: project.customerId,
            customerFound: !!customer,
            customerData: customer ? {
              _id: customer._id,
              name: customer.name,
              address: customer.address,
              streetAddress: customer.streetAddress,
              postalCode: customer.postalCode,
              city: customer.city
            } : null
          });
          return {
            ...project,
            customer
          };
        }
        console.log(`DEBUG: No customerId for project ${project.name}`);
        return {
          ...project,
          customer: null
        };
      })
    );

    console.log("DEBUG: Projects with customers:", projectsWithCustomers.length);
    return projectsWithCustomers;
  }
});

export const create = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    customerId: v.optional(v.id("customers")) // Optional customer reference
  },
  handler: async (ctx, args) => {
    // ✅ CRITICAL: Add authentication validation
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the userId parameter
    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // ✅ CRITICAL: Verify user exists in database before creating projects
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet i systemet. Vennligst fullfør registreringen først.");
    }

    // If customerId is provided, verify it exists and belongs to the user
    if (args.customerId) {
      const customer = await ctx.db.get(args.customerId);
      if (!customer) {
        throw new Error("Kunde ikke funnet");
      }
      if (customer.userId !== args.userId) {
        throw new Error("Du har ikke tilgang til denne kunden");
      }
    }

    // Generate sharedId for the project
    const sharedId = nanoid(10);

    // Create the project
    const projectId = await ctx.db.insert("projects", {
      name: args.name,
      description: args.description,
      userId: args.userId,
      customerId: args.customerId,
      sharedId: sharedId,
      createdAt: Date.now(),
      isArchived: false  // Ensure all new projects are marked as not archived
    });

    // Automatically create "Prosjekt startet" log entry to enable chat from day 1
    await ctx.db.insert("logEntries", {
      projectId: projectId,
      userId: args.userId,
      description: "Prosjekt startet",
      entryType: "system",
      createdAt: Date.now()
    });

    console.log(`✅ Project created successfully: ${projectId} for user: ${args.userId}`);

    // Return both projectId and sharedId for customer notifications
    return { projectId, sharedId };
  }
});

export const getByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
      .order("desc")
      .collect();
  }
});

// Get projects with customer data (AI-agent friendly) - excludes archived projects
export const getByUserWithCustomers = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    console.log('DEBUG: Querying projects for userId:', args.userId);

    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
      .order("desc")
      .collect();

    console.log('DEBUG: Found projects:', projects.length);
    console.log('DEBUG: Project details:', projects.map(p => ({ id: p._id, name: p.name, userId: p.userId })));

    // Fetch customer data for each project
    const projectsWithCustomers = await Promise.all(
      projects.map(async (project) => {
        if (project.customerId) {
          const customer = await ctx.db.get(project.customerId);
          return {
            ...project,
            customer
          };
        }
        return {
          ...project,
          customer: null
        };
      })
    );

    console.log('DEBUG: Projects with customers:', projectsWithCustomers.length);
    return projectsWithCustomers;
  }
});

// Debug query to see all projects in database
export const debugAllProjects = query({
  args: {},
  handler: async (ctx, args) => {
    const allProjects = await ctx.db.query("projects").collect();
    console.log('DEBUG: All projects in database:', allProjects.map(p => ({
      id: p._id,
      name: p.name,
      userId: p.userId,
      isArchived: p.isArchived,
      createdAt: p.createdAt
    })));
    return allProjects.map(p => ({
      id: p._id,
      name: p.name,
      userId: p.userId,
      isArchived: p.isArchived,
      createdAt: p.createdAt
    }));
  }
});



// Get archived projects for a user
export const getArchivedByUser = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", true))
      .order("desc")
      .collect();
  }
});

// Get archived projects with customer data
export const getArchivedByUserWithCustomers = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", true))
      .order("desc")
      .collect();

    // Fetch customer data for each project
    const projectsWithCustomers = await Promise.all(
      projects.map(async (project) => {
        if (project.customerId) {
          const customer = await ctx.db.get(project.customerId);
          return {
            ...project,
            customer
          };
        }
        return {
          ...project,
          customer: null
        };
      })
    );

    return projectsWithCustomers;
  }
});

// Get all projects (active and archived) for a user - for search/admin purposes
export const getAllByUser = query({
  args: {
    userId: v.string(),
    includeArchived: v.optional(v.boolean()) // Optional flag to include archived projects
  },
  handler: async (ctx, args) => {
    if (args.includeArchived) {
      // Return all projects (active and archived)
      return await ctx.db
        .query("projects")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .order("desc")
        .collect();
    } else {
      // Return only active projects (default behavior)
      return await ctx.db
        .query("projects")
        .withIndex("by_user_and_archive_status", (q) => q.eq("userId", args.userId).eq("isArchived", false))
        .order("desc")
        .collect();
    }
  }
});

export const getById = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return null;
    }

    // Fetch customer data if project has a customer
    if (project.customerId) {
      const customer = await ctx.db.get(project.customerId);
      return {
        ...project,
        customer
      };
    }

    return {
      ...project,
      customer: null
    };
  }
});

// Get project by ID with user access validation
export const getByIdWithUserAccess = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return null;
    }

    // Check if user has access to this project (either owner or team member)
    if (project.userId === args.userId) {
      // User is the owner - fetch customer data if available
      if (project.customerId) {
        const customer = await ctx.db.get(project.customerId);
        return {
          ...project,
          customer
        };
      }
      return {
        ...project,
        customer: null
      };
    }

    // TODO: Add team member access check here when team functionality is fully implemented
    // For now, only allow access to project owners

    console.log(`⚠️ User ${args.userId} attempted to access project ${args.projectId} owned by ${project.userId}`);
    return null; // User doesn't have access
  }
});

// Get projects by customer (AI-agent friendly for queries like "projects for customer X") - excludes archived
export const getByCustomer = query({
  args: {
    customerId: v.id("customers"),
    userId: v.string(),
    includeArchived: v.optional(v.boolean()) // Optional flag to include archived projects
  },
  handler: async (ctx, args) => {
    // Verify customer belongs to user
    const customer = await ctx.db.get(args.customerId);
    if (!customer || customer.userId !== args.userId) {
      throw new Error("Kunde ikke funnet eller du har ikke tilgang");
    }

    let projects;
    if (args.includeArchived) {
      // Get all projects for this customer (active and archived)
      projects = await ctx.db
        .query("projects")
        .withIndex("by_customer", (q) => q.eq("customerId", args.customerId))
        .order("desc")
        .collect();
    } else {
      // Get only active projects for this customer (default)
      projects = await ctx.db
        .query("projects")
        .withIndex("by_customer", (q) => q.eq("customerId", args.customerId))
        .filter((q) => q.neq(q.field("isArchived"), true))
        .order("desc")
        .collect();
    }

    return projects.map(project => ({
      ...project,
      customer
    }));
  }
});

// Update basic project information (name, description)
export const updateProject = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Verify authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the project
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check if user has permission to edit this project
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Du har ikke tilgang til dette prosjektet");
    }

    // Check if user has edit permissions
    const canEdit = userAccess.accessLevel === "owner" ||
                   userAccess.accessLevel === "administrator" ||
                   userAccess.accessLevel === "collaborator" ||
                   userAccess.accessLevel === "subcontractor";

    if (!canEdit) {
      throw new Error("Du har ikke tillatelse til å redigere dette prosjektet");
    }

    // Build update object with only provided fields
    const updates: any = {};
    if (args.name !== undefined) {
      updates.name = args.name.trim();
    }
    if (args.description !== undefined) {
      updates.description = args.description.trim();
    }

    // Add updated timestamp
    updates.updatedAt = Date.now();

    // Update the project
    await ctx.db.patch(args.projectId, updates);

    console.log('✅ Project basic info updated successfully');
    return { success: true };
  }
});

// Update project job data (personalNotes moved to userProjectNotes table)
export const updateProjectJobData = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
    jobData: v.object({
      jobDescription: v.string(),
      photos: v.array(v.object({
        url: v.string(),
        note: v.optional(v.string()),
        capturedAt: v.optional(v.number())
      })),
      accessNotes: v.string(),
      equipmentNeeds: v.string(),
      unresolvedQuestions: v.string(),
      // personalNotes removed - now handled by userProjectNotes table
    })
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Verify the project exists
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    // Check if user has edit permissions for job data
    // Only owners, administrators, and collaborators have edit access
    // Subcontractors have read-only access to job data
    const canEditJobData = userAccess.accessLevel === 'owner' ||
                          userAccess.accessLevel === 'administrator' ||
                          userAccess.accessLevel === 'collaborator';

    if (!canEditJobData) {
      throw new Error("Du har ikke tilgang til å redigere prosjektdata");
    }

    // Update the project with job data
    console.log('🔄 Updating project job data:', {
      projectId: args.projectId,
      userId: args.userId,
      userAccessLevel: userAccess.accessLevel,
      equipmentNeeds: args.jobData.equipmentNeeds?.substring(0, 50) + '...',
      unresolvedQuestions: args.jobData.unresolvedQuestions?.substring(0, 50) + '...',
      accessNotes: args.jobData.accessNotes?.substring(0, 50) + '...'
    });

    await ctx.db.patch(args.projectId, {
      jobData: args.jobData,
      updatedAt: Date.now() // Ensure real-time sync triggers
    });

    console.log('✅ Project job data updated successfully');
    return { success: true };
  }
});

// Store uploaded image file and return URL
export const storeJobImage = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
    storageId: v.id("_storage")
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Verify the project exists
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    // Get the URL for the stored file
    const url = await ctx.storage.getUrl(args.storageId);

    if (!url) {
      throw new Error("Kunne ikke hente bilde-URL");
    }

    return { url };
  }
});

// Archive a project (preserves all data but removes from active view)
export const archiveProject = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Verify the project exists
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check user access and permissions
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Du har ikke tilgang til dette prosjektet");
    }

    // Handle different archiving scenarios based on user access level
    if (userAccess.accessLevel === 'subcontractor') {
      // For subcontractors: Create a local archive record instead of archiving the main project
      // This allows subcontractors to hide projects from their view without affecting the main contractor

      // Check if already archived locally by this subcontractor
      const existingArchive = await ctx.db
        .query("subcontractorArchives")
        .withIndex("by_user_and_project", (q) =>
          q.eq("userId", args.userId).eq("projectId", args.projectId)
        )
        .first();

      if (existingArchive && existingArchive.isArchived) {
        throw new Error("Prosjektet er allerede arkivert i din oversikt");
      }

      // Create or update subcontractor archive record
      if (existingArchive) {
        await ctx.db.patch(existingArchive._id, {
          isArchived: true,
          archivedAt: Date.now(),
          archivedBy: args.userId
        });
      } else {
        await ctx.db.insert("subcontractorArchives", {
          userId: args.userId,
          projectId: args.projectId,
          isArchived: true,
          archivedAt: Date.now(),
          archivedBy: args.userId
        });
      }

      // Create activity log entry for subcontractor archiving
      await ctx.db.insert("logEntries", {
        projectId: args.projectId,
        userId: args.userId,
        description: "Prosjekt arkivert (underleverandør)",
        entryType: "system",
        createdAt: Date.now()
      });

      return { success: true, type: "subcontractor_archive" };
    }

    // For project owners and company administrators: Archive the main project
    if (userAccess.accessLevel !== 'owner' && userAccess.accessLevel !== 'administrator') {
      throw new Error("Du har ikke tillatelse til å arkivere dette prosjektet");
    }

    // Check if project is already archived
    if (project.isArchived) {
      throw new Error("Prosjektet er allerede arkivert");
    }

    // Archive the project (preserves all data)
    await ctx.db.patch(args.projectId, {
      isArchived: true,
      archivedAt: Date.now(),
      archivedBy: args.userId
    });

    // Create activity log entry for archiving
    await ctx.db.insert("logEntries", {
      projectId: args.projectId,
      userId: args.userId,
      description: "Prosjekt arkivert",
      entryType: "system",
      createdAt: Date.now()
    });

    return { success: true, type: "main_archive" };
  }
});

// Restore an archived project (makes it active again)
export const restoreProject = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string()
  },
  handler: async (ctx, args) => {
    // Verify the project exists and belongs to the user
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    if (project.userId !== args.userId) {
      throw new Error("Du har ikke tilgang til å gjenåpne dette prosjektet");
    }

    // Check if project is actually archived
    if (!project.isArchived) {
      throw new Error("Prosjektet er ikke arkivert");
    }

    // Restore the project (make it active again)
    await ctx.db.patch(args.projectId, {
      isArchived: false,
      archivedAt: undefined,
      archivedBy: undefined
    });

    // Create activity log entry for restoring
    await ctx.db.insert("logEntries", {
      projectId: args.projectId,
      userId: args.userId,
      description: "Prosjekt gjenåpnet",
      entryType: "system",
      createdAt: Date.now()
    });

    return { success: true };
  }
});

// Get project by shared ID (for public access)
export const getBySharedId = query({
  args: { sharedId: v.string() },
  handler: async (ctx, args) => {
    const project = await ctx.db
      .query("projects")
      .withIndex("by_shared_id", (q) => q.eq("sharedId", args.sharedId))
      .first();

    if (!project) {
      return null;
    }

    // Check if sharing is enabled
    if (!project.isPubliclyShared) {
      return null;
    }

    // Fetch customer data if project has a customer
    if (project.customerId) {
      const customer = await ctx.db.get(project.customerId);
      return {
        ...project,
        customer
      };
    }

    return {
      ...project,
      customer: null
    };
  }
});

// Enable/disable project sharing
export const updateSharingSettings = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
    isPubliclyShared: v.boolean(),
    shareSettings: v.optional(v.object({
      showContractorNotes: v.boolean(),
      accessCount: v.number(),
      lastAccessedAt: v.optional(v.number())
    }))
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Verify the project exists
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      throw new Error("Prosjekt ikke funnet");
    }

    // Check if user has access to this project using team access validation
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    // Only project owners and company administrators can share projects
    // Subcontractors are explicitly NOT allowed to share projects
    if (userAccess.accessLevel === 'subcontractor') {
      throw new Error("Underleverandører kan ikke dele prosjekter med kunder. Kun hovedkontraktøren kan dele prosjekter.");
    }

    if (userAccess.accessLevel !== 'owner' && userAccess.accessLevel !== 'administrator') {
      throw new Error("Du har ikke tillatelse til å endre delingsinnstillinger for dette prosjektet");
    }

    // Update sharing settings
    await ctx.db.patch(args.projectId, {
      isPubliclyShared: args.isPubliclyShared,
      shareSettings: args.shareSettings || {
        showContractorNotes: false,
        accessCount: 0
      }
    });

    return { success: true };
  }
});

// Track access to shared project
export const trackSharedProjectAccess = mutation({
  args: { sharedId: v.string() },
  handler: async (ctx, args) => {
    console.log(`Tracking access for sharedId: ${args.sharedId}`);

    const project = await ctx.db
      .query("projects")
      .withIndex("by_shared_id", (q) => q.eq("sharedId", args.sharedId))
      .first();

    if (!project) {
      console.error(`Project not found for sharedId: ${args.sharedId}`);
      throw new Error("Prosjekt ikke funnet");
    }

    console.log(`Found project: ${project.name}, isPubliclyShared: ${project.isPubliclyShared}`);

    // Check if sharing is enabled
    if (!project.isPubliclyShared) {
      console.error(`Project ${project.name} is not publicly shared`);
      throw new Error("Dette prosjektet er ikke delt offentlig");
    }

    // Update access statistics
    const currentAccessCount = project.shareSettings?.accessCount || 0;
    const updatedShareSettings = {
      ...project.shareSettings,
      showContractorNotes: project.shareSettings?.showContractorNotes || false,
      accessCount: currentAccessCount + 1,
      lastAccessedAt: Date.now()
    };

    console.log(`Updating access count from ${currentAccessCount} to ${currentAccessCount + 1}`);

    await ctx.db.patch(project._id, {
      shareSettings: updatedShareSettings
    });

    console.log(`Successfully tracked access for project: ${project.name}`);

    // Mark project as accessed in email tracking records
    try {
      // TODO: Re-enable email tracking when type instantiation issue is resolved
      // await ctx.runMutation(internal.emailTracking.markProjectAccessed, {
      //   projectId: project._id
      // });
      console.log("⚠️ Email tracking temporarily disabled due to type issues");
      console.log(`Updated email tracking records for project access`);
    } catch (error) {
      console.warn('Failed to update email tracking records:', error);
      // Don't fail the main tracking operation if email tracking fails
    }

    return {
      success: true,
      accessCount: updatedShareSettings.accessCount,
      lastAccessedAt: updatedShareSettings.lastAccessedAt
    };
  }
});

// Get shared project statistics for dashboard
export const getSharedProjectStats = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    const sharedProjects = projects.filter(p => p.isPubliclyShared);

    if (sharedProjects.length === 0) {
      return null;
    }

    const totalViews = sharedProjects.reduce((sum, project) => {
      return sum + (project.shareSettings?.accessCount || 0);
    }, 0);

    const lastAccessTimes = sharedProjects
      .map(p => p.shareSettings?.lastAccessedAt)
      .filter(time => time !== undefined)
      .sort((a, b) => b - a);

    const mostRecentAccess = lastAccessTimes.length > 0 ? lastAccessTimes[0] : null;

    return {
      sharedProjectCount: sharedProjects.length,
      totalViews,
      mostRecentAccess
    };
  }
});

// Get shared project statistics for a specific project
export const getProjectSharedStats = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const project = await ctx.db.get(args.projectId);

    if (!project) {
      return null;
    }

    // Only return stats if project is shared
    if (!project.isPubliclyShared) {
      return null;
    }

    return {
      isShared: true,
      accessCount: project.shareSettings?.accessCount || 0,
      lastAccessedAt: project.shareSettings?.lastAccessedAt || null,
      showContractorNotes: project.shareSettings?.showContractorNotes || false
    };
  }
});

// Helper function to get user contact information
const getUserContactInfo = async (ctx: any, clerkUserId: string) => {
  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_user_id", (q: any) => q.eq("clerkUserId", clerkUserId))
    .first();

  if (!user) return null;

  // Get contractor company for company context
  let contractorCompany = null;
  if (user.contractorCompanyId) {
    contractorCompany = await ctx.db.get(user.contractorCompanyId);
  }

  // Build contact person info
  let name = null;
  if (user.invitationFirstName && user.invitationLastName) {
    name = `${user.invitationFirstName} ${user.invitationLastName}`;
  } else if (user.invitationFirstName) {
    name = user.invitationFirstName;
  } else if (user.role === "administrator" && contractorCompany?.contactPerson) {
    name = contractorCompany.contactPerson;
  }

  return {
    clerkUserId: user.clerkUserId,
    name,
    role: user.role,
    email: user.invitationEmail || contractorCompany?.email,
    phone: user.invitationPhone || contractorCompany?.phone,
    company: contractorCompany,
    source: user.invitationFirstName ? 'invitation' : 'company'
  };
};

// Helper function to get project leader (Prosjektleder)
const getProjectLeader = async (ctx: any, projectId: string) => {
  const assignment = await ctx.db
    .query("projectAssignments")
    .withIndex("by_project", (q: any) => q.eq("projectId", projectId))
    .filter((q: any) => q.and(
      q.eq(q.field("accessLevel"), "owner"), // Prosjektleder has owner access level
      q.neq(q.field("isActive"), false)
    ))
    .first();

  return assignment;
};

// Get project contact person with intelligent resolution
export const getProjectContactPerson = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const project = await ctx.db.get(args.projectId);
    if (!project) return null;

    // 1. Check for explicit project contact person override
    if (project.projectContactPersonId && project.useProjectSpecificContact) {
      const contactInfo = await getUserContactInfo(ctx, project.projectContactPersonId);
      if (contactInfo) {
        return {
          ...contactInfo,
          source: 'explicit',
          isProjectLeader: false
        };
      }
    }

    // 2. Check for assigned Prosjektleder
    const projectLeader = await getProjectLeader(ctx, args.projectId);
    if (projectLeader) {
      const contactInfo = await getUserContactInfo(ctx, projectLeader.assignedUserId);
      if (contactInfo) {
        return {
          ...contactInfo,
          source: 'assigned',
          isProjectLeader: true
        };
      }
    }

    // 3. Fall back to project creator
    const creatorInfo = await getUserContactInfo(ctx, project.userId);
    if (creatorInfo) {
      return {
        ...creatorInfo,
        source: 'creator',
        isProjectLeader: false
      };
    }

    // 4. Final fallback to company contact person
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q: any) => q.eq("clerkUserId", project.userId))
      .first();

    if (user?.contractorCompanyId) {
      const contractorCompany = await ctx.db.get(user.contractorCompanyId);
      if (contractorCompany) {
        return {
          clerkUserId: null,
          name: contractorCompany.contactPerson,
          role: null,
          email: contractorCompany.email,
          phone: contractorCompany.phone,
          company: contractorCompany,
          source: 'company',
          isProjectLeader: false
        };
      }
    }

    return null;
  }
});
