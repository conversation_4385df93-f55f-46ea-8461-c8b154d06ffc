import { v } from 'convex/values';
import { action } from './_generated/server';

/**
 * EMERGENCY FIX: Manually update subscription status from trialing to active
 * This is a temporary fix while we investigate why webhooks aren't working
 */
export const emergencyFixSubscriptionStatus = action({
  args: { 
    userId: v.string(),
    reason: v.string() // Why we're doing this manual fix
  },
  handler: async (ctx, args) => {
    console.log(`🚨 EMERGENCY FIX: Manually updating subscription status for user ${args.userId}`);
    console.log(`Reason: ${args.reason}`);
    
    try {
      // Get current subscription using internal query
      const subscription = await ctx.runQuery("subscriptions:getSubscriptionByUserInternal", {
        userId: args.userId
      });
      
      if (!subscription) {
        return {
          success: false,
          error: 'No subscription found for user',
          userId: args.userId,
        };
      }
      
      console.log('📊 Current subscription state:', {
        _id: subscription._id,
        status: subscription.status,
        planLevel: subscription.planLevel,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
      });
      
      const now = Date.now();
      const trialEnded = subscription.trialEnd && now >= subscription.trialEnd;
      
      if (subscription.status === 'trialing') {
        console.log('🔄 Updating subscription from trialing to active');

        // Update subscription record using mutation
        await ctx.runMutation("subscriptions:updateSubscriptionStatus", {
          stripeSubscriptionId: subscription.stripeSubscriptionId,
          status: 'active',
          currentPeriodStart: now,
          currentPeriodEnd: now + (30 * 24 * 60 * 60 * 1000), // 30 days from now
          trialEnd: subscription.trialEnd,
          planLevel: subscription.planLevel,
          billingInterval: subscription.billingInterval,
        });

        console.log('✅ Subscription updated to active status');
        
        return {
          success: true,
          action: 'updated_to_active',
          subscription: {
            _id: subscription._id,
            oldStatus: 'trialing',
            newStatus: 'active',
            trialConvertedAt: now,
            trialEnded,
          },
          user: user ? {
            _id: user._id,
            hasActiveSubscription: true,
            subscriptionStatus: 'active',
          } : null,
          timestamp: now,
          reason: args.reason,
        };
      } else {
        return {
          success: true,
          action: 'no_update_needed',
          subscription: {
            _id: subscription._id,
            status: subscription.status,
            trialEnd: subscription.trialEnd,
            trialEnded,
          },
          message: `Subscription is already ${subscription.status}`,
          timestamp: now,
        };
      }
      
    } catch (error) {
      console.error('❌ Emergency fix failed:', error);
      return {
        success: false,
        error: error.message,
        userId: args.userId,
        timestamp: Date.now(),
      };
    }
  }
});

/**
 * Check all subscriptions that might need fixing
 */
export const findSubscriptionsNeedingFix = action({
  handler: async (ctx) => {
    console.log('🔍 Checking for subscriptions that might need fixing...');
    
    try {
      const now = Date.now();
      
      // Find subscriptions that are still "trialing" but trial has ended
      const subscriptions = await ctx.db.query("subscriptions").collect();
      
      const needsFix = subscriptions.filter(sub => 
        sub.status === 'trialing' && 
        sub.trialEnd && 
        now >= sub.trialEnd &&
        !sub.trialConvertedAt
      );
      
      console.log(`Found ${needsFix.length} subscriptions that might need fixing`);
      
      return {
        totalSubscriptions: subscriptions.length,
        needsFix: needsFix.length,
        subscriptions: needsFix.map(sub => ({
          _id: sub._id,
          userId: sub.userId,
          status: sub.status,
          planLevel: sub.planLevel,
          trialEnd: sub.trialEnd,
          trialEndDate: new Date(sub.trialEnd).toISOString(),
          daysOverdue: Math.floor((now - sub.trialEnd) / (24 * 60 * 60 * 1000)),
          stripeSubscriptionId: sub.stripeSubscriptionId,
        })),
        timestamp: now,
      };
      
    } catch (error) {
      console.error('❌ Error checking subscriptions:', error);
      return {
        success: false,
        error: error.message,
        timestamp: Date.now(),
      };
    }
  }
});

/**
 * Batch fix multiple subscriptions
 */
export const batchFixSubscriptions = action({
  args: { 
    userIds: v.array(v.string()),
    reason: v.string()
  },
  handler: async (ctx, args) => {
    console.log(`🚨 BATCH FIX: Updating ${args.userIds.length} subscriptions`);
    console.log(`Reason: ${args.reason}`);
    
    const results = [];
    
    for (const userId of args.userIds) {
      try {
        const result = await ctx.runAction("quickFix:emergencyFixSubscriptionStatus", {
          userId,
          reason: args.reason
        });
        results.push({ userId, ...result });
      } catch (error) {
        results.push({
          userId,
          success: false,
          error: error.message
        });
      }
    }
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`✅ Batch fix completed: ${successful} successful, ${failed} failed`);
    
    return {
      totalProcessed: args.userIds.length,
      successful,
      failed,
      results,
      timestamp: Date.now(),
    };
  }
});
