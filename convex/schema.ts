import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

// TypeScript interfaces for better type safety and documentation
export interface ShareSettings {
  showContractorNotes: boolean;        // Show contractor-specific notes to customers
  accessCount: number;                 // Track how many times project was accessed - milliseconds since Unix epoch
  lastAccessedAt?: number;             // Track last access time - milliseconds since Unix epoch
}

export interface JobPhoto {
  url: string;                         // Image URL from Convex storage
  note?: string;                       // Optional comment/note for the image
  capturedAt?: number;                 // Timestamp when photo was taken - milliseconds since Unix epoch
}

export interface JobData {
  jobDescription: string;              // "Hva skal gjøres?" - detailed job description
  photos: JobPhoto[];                  // "Bilder fra befaring" - site inspection photos
  accessNotes: string;                 // "Tilkomst og forhold" - access and site conditions
  equipmentNeeds: string;              // "Hva må medbringes?" - equipment and materials needed
  unresolvedQuestions: string;         // "Hva må avklares?" - questions that need clarification
  personalNotes: string;               // "Egne notater" - contractor's personal notes
}

export interface MessageFile {
  url: string;                         // Signed URL to file storage
  name: string;                        // Original filename
  size: number;                        // File size in bytes
  type: string;                        // MIME type
  thumbnailUrl?: string;               // Thumbnail URL for images/videos
}

export interface MessageReaction {
  emoji: string;                       // Emoji character (e.g., "👍", "❤️")
  userIds: string[];                   // Array of user IDs who reacted with this emoji
  count: number;                       // Count of reactions (for performance)
}

export type DeliveryStatus = "sending" | "sent" | "delivered" | "failed";
export type CustomerType = "privat" | "bedrift" | "contractor";
export type SenderRole = "customer" | "contractor";
export type EntryType = "user" | "system";

// Custom validators for enhanced data integrity
const positiveNumber = v.number(); // TODO: Add custom validator for positive numbers
const timestampValidator = v.number(); // milliseconds since Unix epoch

export default defineSchema({
  // User data table for contractor onboarding and team management
  users: defineTable({
    clerkUserId: v.string(),             // Clerk user ID (unique identifier from authentication)
    contractorCompleted: v.optional(v.boolean()), // Whether contractor onboarding is completed
    contractorCompanyId: v.optional(v.id("customers")), // Reference to contractor's company record

    // Multi-user team management fields
    role: v.optional(v.union(v.literal("administrator"), v.literal("prosjektleder"), v.literal("utfoerende"))), // User role within company
    invitedBy: v.optional(v.string()),   // Clerk ID of administrator who invited this user
    invitationToken: v.optional(v.string()), // Unique token for invitation process

    // Enhanced company relationship for subcontractor support
    companyType: v.optional(v.union(
      v.literal("main_contractor"),      // Main contractor company
      v.literal("subcontractor")         // Subcontractor company
    )),
    specialization: v.optional(v.string()), // For subcontractors: "Elektriker", "Rørlegger", etc.
    invitationStatus: v.optional(v.union(
      v.literal("pending"),              // Invitation sent but not accepted
      v.literal("accepted"),             // Invitation accepted and user onboarded
      v.literal("expired")               // Invitation expired or cancelled
    )),
    invitedAt: v.optional(timestampValidator), // When invitation was sent - milliseconds since Unix epoch
    acceptedAt: v.optional(timestampValidator), // When invitation was accepted - milliseconds since Unix epoch

    // Magic link invitation data (pre-filled for registration)
    invitationEmail: v.optional(v.string()), // Email from invitation
    invitationFirstName: v.optional(v.string()), // First name from invitation
    invitationLastName: v.optional(v.string()), // Last name from invitation
    invitationPhone: v.optional(v.string()), // Phone from invitation

    // Team member lifecycle management
    isActive: v.optional(v.boolean()),   // Whether user is active in the team (default: true)
    deletedAt: v.optional(timestampValidator), // When user was removed from team - milliseconds since Unix epoch
    deletedBy: v.optional(v.string()),   // Clerk ID of administrator who removed this user

    // Activity tracking for "sist logget inn" functionality
    lastLoginAt: v.optional(timestampValidator), // Last login timestamp - milliseconds since Unix epoch
    lastActivityAt: v.optional(timestampValidator), // Last activity timestamp - milliseconds since Unix epoch

    // User blocking/suspension functionality
    isBlocked: v.optional(v.boolean()),  // Whether user is blocked from team access
    blockedAt: v.optional(timestampValidator), // When user was blocked - milliseconds since Unix epoch
    blockedBy: v.optional(v.string()),   // Clerk ID of administrator who blocked this user
    blockedReason: v.optional(v.string()), // Optional reason for blocking (for audit trail)

    // Subscription status (derived from Stripe events only)
    subscriptionStatus: v.optional(v.union(
      v.literal("trialing"),
      v.literal("active"),
      v.literal("past_due"),
      v.literal("canceled"),
      v.literal("grace_period")
    )),
    trialEndsAt: v.optional(timestampValidator), // When trial ends - milliseconds since Unix epoch
    hasCompletedTrial: v.optional(v.boolean()), // Whether user has completed a trial before

    // Stripe mapping (one Stripe customer per user)
    stripeCustomerId: v.optional(v.string()),

    // Prevent multiple trials
    trialUsedAt: v.optional(timestampValidator), // When trial was first used - milliseconds since Unix epoch
    paymentMethodFingerprint: v.optional(v.string()), // Payment method fingerprint to prevent duplicate trials

    // Subscription lifecycle tracking
    trialConvertedAt: v.optional(timestampValidator), // When trial converted to paid - milliseconds since Unix epoch
    subscriptionCancelledAt: v.optional(timestampValidator), // When subscription was cancelled - milliseconds since Unix epoch
    subscriptionReactivatedAt: v.optional(timestampValidator), // When subscription was reactivated - milliseconds since Unix epoch

    createdAt: timestampValidator,       // Account creation timestamp - milliseconds since Unix epoch
    updatedAt: v.optional(timestampValidator), // Last update timestamp - milliseconds since Unix epoch
  })
    .index("by_clerk_user_id", ["clerkUserId"])
    .index("by_contractor_company", ["contractorCompanyId"])
    .index("by_onboarding_status", ["contractorCompleted"])
    .index("by_invitation_token", ["invitationToken"])
    .index("by_company_and_role", ["contractorCompanyId", "role"])
    .index("by_invited_by", ["invitedBy"])
    .index("by_invitation_status", ["invitationStatus"])
    .index("by_active_status", ["isActive"])
    .index("by_last_activity", ["lastActivityAt"])
    .index("by_blocked_status", ["isBlocked"])
    .index("by_stripe_customer_id", ["stripeCustomerId"]),

  // Customer data table for AI-agent friendly structure
  customers: defineTable({
    name: v.string(),                    // Customer name (required) - shown in UI and search
    type: v.union(v.literal("privat"), v.literal("bedrift"), v.literal("contractor")), // Customer type: enhanced for subcontractor support
    contactPerson: v.optional(v.string()), // Contact person (if type === "bedrift") - e.g. "Arne Løken"
    phone: v.optional(v.string()),       // Phone number (optional) - for quick contact
    email: v.optional(v.string()),       // Email address (optional) - used in reporting or notifications
    // Enhanced address structure
    address: v.optional(v.string()),     // Legacy single address field (for backward compatibility)
    streetAddress: v.optional(v.string()), // Street address (required for new customers) - e.g. "Storgata 15"
    postalCode: v.optional(v.string()),  // Postal code (required for new customers) - e.g. "0123"
    city: v.optional(v.string()),        // City/Town (required for new customers) - e.g. "Oslo"
    entrance: v.optional(v.string()),    // Entrance/Floor info (optional) - e.g. "Oppgang A, 2. etasje"
    orgNumber: v.optional(v.string()),   // Organization number (optional, only for bedrift) - legal ID
    notes: v.optional(v.string()),       // Notes (optional) - free text: key code, "customer is allergic to dogs", etc.
    // Contractor company identification
    contractorUserId: v.optional(v.string()), // Clerk user ID if this is a contractor's company record

    // Contractor specialization fields (for subcontractor matching)
    primarySpecialization: v.optional(v.string()), // Primary trade/specialization - e.g. "Elektriker", "Rørlegger", "Arkitekt"
    secondarySpecializations: v.optional(v.array(v.string())), // Additional specializations for multi-trade companies
    specializationSource: v.optional(v.union(
      v.literal("brregData"),            // Auto-suggested from Brønnøysundregisteret industry code
      v.literal("manual"),               // Manually selected during onboarding
      v.literal("updated")               // Updated after initial registration
    )),

    // Brønnøysundregisteret data tracking
    brregFetchedAt: v.optional(timestampValidator), // Timestamp when data was fetched from Brønnøysundregisteret - milliseconds since Unix epoch
    brregData: v.optional(v.object({     // Original Brønnøysundregisteret data (for reference and freshness)
      name: v.optional(v.string()),      // Company name from Brreg
      orgNumber: v.optional(v.string()), // Organization number from Brreg
      organizationNumber: v.optional(v.string()), // Alternative field name for organization number
      status: v.optional(v.string()),    // Company status (active, inactive, etc.)
      industryCode: v.optional(v.string()), // Industry code (NACE code) - legacy field
      industryDescription: v.optional(v.string()), // Industry description
      // Enhanced Brønnøysundregisteret fields
      organizationForm: v.optional(v.string()), // Organization form description (e.g., "Aksjeselskap")
      organizationFormCode: v.optional(v.string()), // Organization form code (e.g., "AS")
      naeringskode1: v.optional(v.string()), // Primary NACE industry code (5-digit)
      establishmentDate: v.optional(v.string()), // Company founding date (ISO format)
      numberOfEmployees: v.optional(v.number()), // Number of employees
      registryContact: v.optional(v.object({ // Contact information from registry
        phone: v.optional(v.string()),   // Phone number from registry
        email: v.optional(v.string())    // Email from registry
      })),
      managingDirector: v.optional(v.union(
        v.string(), // Legacy format: just the name as string
        v.object({  // New detailed format from Brønnøysundregisteret
          birthDate: v.optional(v.string()),
          firstName: v.optional(v.string()),
          fullName: v.optional(v.string()),
          lastName: v.optional(v.string())
        })
      )), // Managing director from Brreg
      businessAddress: v.optional(v.object({ // Business address from Brreg
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      })),
      visitingAddress: v.optional(v.object({ // Visiting address from Brreg (if different)
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      }))
    })),
    // Address override tracking
    useCustomAddress: v.optional(v.boolean()), // Whether user chose to override Brreg address
    userId: v.string(),                  // Owner of this customer record
    createdAt: timestampValidator        // Creation timestamp - milliseconds since Unix epoch
  })
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_user_and_type", ["userId", "type"])
    .index("by_org_number", ["orgNumber"])
    .index("by_contractor_user", ["contractorUserId"]),

  projects: defineTable({
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    customerId: v.optional(v.id("customers")), // Reference to customer - enables AI queries like "projects for customer X"
    sharedId: v.string(),
    createdAt: timestampValidator,         // Creation timestamp - milliseconds since Unix epoch
    updatedAt: v.optional(timestampValidator), // Last update timestamp - milliseconds since Unix epoch
    // Archive management - preserves data while removing from active view
    isArchived: v.optional(v.boolean()),       // Archive status - defaults to false for active projects
    archivedAt: v.optional(timestampValidator), // Timestamp when project was archived - milliseconds since Unix epoch
    archivedBy: v.optional(v.string()),        // User ID who archived the project
    // Project sharing configuration
    isPubliclyShared: v.optional(v.boolean()), // Enable/disable public sharing
    shareSettings: v.optional(v.object({
      showContractorNotes: v.boolean(),        // Show contractor-specific notes to customers
      accessCount: positiveNumber,             // Track how many times project was accessed
      lastAccessedAt: v.optional(timestampValidator) // Track last access time - milliseconds since Unix epoch
    })),
    // Project-specific contact person system
    projectContactPersonId: v.optional(v.string()), // Clerk ID of designated project contact person
    useProjectSpecificContact: v.optional(v.boolean()), // Override company contact with project-specific contact
    // Job information for contractor workflow documentation
    jobData: v.optional(v.object({
      jobDescription: v.string(),           // "Hva skal gjøres?" - detailed job description
      photos: v.array(v.object({           // "Bilder fra befaring" - site inspection photos
        url: v.string(),                   // Image URL from Convex storage
        note: v.optional(v.string()),      // Optional comment/note for the image
        capturedAt: v.optional(timestampValidator) // Timestamp when photo was taken - milliseconds since Unix epoch
      })),
      accessNotes: v.string(),             // "Tilkomst og forhold" - access and site conditions
      equipmentNeeds: v.string(),          // "Hva må medbringes?" - equipment and materials needed
      unresolvedQuestions: v.string(),     // "Hva må avklares?" - questions that need clarification
      // NOTE: personalNotes moved to userProjectNotes table for user-specific privacy
    }))
  })
    .index("by_user", ["userId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_customer", ["customerId"])
    .index("by_user_and_customer", ["userId", "customerId"])
    .index("by_user_and_archive_status", ["userId", "isArchived"])  // Efficient querying of active vs archived projects
    .index("by_archived_status", ["isArchived"]),                   // Global archive status queries

  logEntries: defineTable({
    projectId: v.id("projects"),
    userId: v.string(),
    description: v.string(),
    imageId: v.optional(v.id("_storage")),
    createdAt: timestampValidator,         // Creation timestamp - milliseconds since Unix epoch
    // Entry type to distinguish between user content and system activities
    entryType: v.optional(v.union(v.literal("user"), v.literal("system"))), // "user" for regular entries, "system" for archive/restore activities
    // Edit history and tracking fields
    isEdited: v.optional(v.boolean()),
    lastEditedAt: v.optional(timestampValidator), // Last edit timestamp - milliseconds since Unix epoch
    editHistory: v.optional(v.array(v.object({
      version: positiveNumber,
      editedAt: timestampValidator,       // Edit timestamp - milliseconds since Unix epoch
      description: v.string(),
      imageId: v.optional(v.id("_storage")),
      changeType: v.union(v.literal("description"), v.literal("image"), v.literal("both")), // Enum instead of free string
      changeSummary: v.string()
    })))
  })
    .index("by_project", ["projectId"])
    .index("by_user", ["userId"])
    .index("by_project_and_user", ["projectId", "userId"]),



  // Customer image likes - customers can "like" images in shared projects
  imageLikes: defineTable({
    logEntryId: v.id("logEntries"),           // Reference to the log entry with the image
    projectId: v.id("projects"),              // Reference to project (for easier querying)
    sharedId: v.string(),                     // For validation that like came from shared link

    // Customer identification (anonymous but consistent within session)
    customerSessionId: v.string(),            // Unique session identifier (nanoid) for anonymous customer
    customerName: v.optional(v.string()),     // Optional name if customer provided it
    customerEmail: v.optional(v.string()),    // Optional email if customer provided it

    // Like metadata
    createdAt: timestampValidator,            // Creation timestamp - milliseconds since Unix epoch
    ipAddress: v.optional(v.string()),        // For spam prevention
  })
    .index("by_log_entry", ["logEntryId"])
    .index("by_project", ["projectId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_customer_session", ["customerSessionId"])
    .index("by_log_entry_and_customer", ["logEntryId", "customerSessionId"])
    .index("by_project_and_customer", ["projectId", "customerSessionId"]),

  // Chat messages - threaded conversations on log entries
  messages: defineTable({
    logId: v.id("logEntries"),                // Always linked to a log entry (Prosjekt → Logg → Tråd)
    parentId: v.optional(v.id("messages")),   // null = root message (log description), value = reply to message

    // Sender information
    senderId: v.string(),                     // User ID of message sender
    senderRole: v.union(v.literal("customer"), v.literal("contractor")), // Role for display name mapping

    // Message content - at least one of text or file must be provided
    text: v.optional(v.string()),             // Message text content (required if no file)
    file: v.optional(v.object({               // File attachment metadata (required if no text)
      url: v.string(),                        // Signed URL to file storage
      name: v.string(),                       // Original filename
      size: positiveNumber,                   // File size in bytes
      type: v.string(),                       // MIME type
      thumbnailUrl: v.optional(v.string())    // Thumbnail URL for images/videos
    })),

    // Reactions and engagement
    reactions: v.optional(v.array(v.object({
      emoji: v.string(),                      // Emoji character (e.g., "👍", "❤️")
      userIds: v.array(v.string()),           // Array of user IDs who reacted with this emoji
      count: positiveNumber                   // Count of reactions (for performance)
    }))),

    // Read status tracking
    readBy: v.optional(v.record(v.string(), timestampValidator)), // Dynamic object: { [userId]: timestamp }

    // Delivery status tracking
    deliveryStatus: v.optional(v.union(
      v.literal("sending"),     // Message is being sent
      v.literal("sent"),        // Message sent to server
      v.literal("delivered"),   // Message delivered to recipient(s)
      v.literal("failed")       // Message failed to send
    )),
    deliveredTo: v.optional(v.record(v.string(), timestampValidator)), // { [userId]: timestamp } when delivered
    failureReason: v.optional(v.string()), // Error message if delivery failed

    // Message metadata
    createdAt: timestampValidator,            // Creation timestamp - milliseconds since Unix epoch
    updatedAt: v.optional(timestampValidator), // Last edit timestamp - milliseconds since Unix epoch
    isEdited: v.optional(v.boolean()),        // Whether message has been edited
    isDeleted: v.optional(v.boolean())        // Soft delete flag
  })
    // Custom validation: at least one of text or file must be provided
    // This will be enforced in the mutation handlers since Convex doesn't support cross-field validation
    .index("by_log", ["logId"])               // Get all messages for a log entry
    .index("by_parent", ["parentId"])         // Get replies to a message
    .index("by_sender", ["senderId"])         // Get messages by sender
    .index("by_created", ["createdAt"])       // Chronological ordering
    .index("by_log_and_created", ["logId", "createdAt"]) // Efficient log message ordering
    .index("by_log_and_parent", ["logId", "parentId"]),   // Thread structure queries

  // Typing indicators for real-time chat (updated for proper deployment)
  typingIndicators: defineTable({
    logId: v.id("logEntries"),                // Log entry where user is typing
    userId: v.string(),                       // User who is typing
    userRole: v.union(v.literal("customer"), v.literal("contractor")), // Role for display
    expiresAt: timestampValidator,            // Timestamp when indicator expires - milliseconds since Unix epoch
    createdAt: timestampValidator,            // When typing started - milliseconds since Unix epoch
    updatedAt: timestampValidator             // Last activity timestamp - milliseconds since Unix epoch
  })
    .index("by_log", ["logId"])               // Get all typing indicators for a log
    .index("by_log_and_user", ["logId", "userId"]) // Get specific user's typing indicator
    .index("by_expires", ["expiresAt"]),      // For cleanup of expired indicators

  // Project assignments for team collaboration (enhanced for subcontractor support)
  projectAssignments: defineTable({
    projectId: v.id("projects"),              // Reference to assigned project
    assignedUserId: v.string(),               // Clerk ID of user assigned to project
    assignedBy: v.string(),                   // Clerk ID of administrator who made assignment
    assignedAt: timestampValidator,           // When assignment was made - milliseconds since Unix epoch
    accessLevel: v.union(
      v.literal("owner"),                     // Prosjektleder - Full project ownership (main contractor only)
      v.literal("collaborator"),              // Utførende - Can edit project and add log entries (main contractor)
      v.literal("subcontractor"),             // Underleverandør - Subcontractor company member
      v.literal("viewer")                     // Observatør - Read-only access (both main contractor and subcontractors)
    ),

    // Company relationship tracking for subcontractor support
    assignedCompanyId: v.optional(v.id("customers")), // Company the assigned user belongs to
    isSubcontractor: v.optional(v.boolean()),          // Whether this is a subcontractor assignment
    subcontractorSpecialization: v.optional(v.string()), // "Elektriker", "Rørlegger", etc.

    // Invitation system fields
    invitationStatus: v.optional(v.union(
      v.literal("pending"),                   // Invitation sent, awaiting response
      v.literal("accepted"),                  // Invitation accepted by subcontractor
      v.literal("declined"),                  // Invitation declined by subcontractor
      v.literal("expired")                    // Invitation expired without response
    )),
    invitedAt: v.optional(timestampValidator),         // When invitation was sent - milliseconds since Unix epoch
    respondedAt: v.optional(timestampValidator),       // When subcontractor responded - milliseconds since Unix epoch
    expiresAt: v.optional(timestampValidator),         // When invitation expires - milliseconds since Unix epoch
    invitationMessage: v.optional(v.string()),         // Personal message from main contractor
    estimatedDuration: v.optional(v.string()),         // "2-3 uker" - estimated project duration
    urgency: v.optional(v.union(
      v.literal("low"),                       // Low priority invitation
      v.literal("medium"),                    // Medium priority invitation
      v.literal("high")                       // High priority invitation
    )),
    startDate: v.optional(timestampValidator),         // When work should begin - milliseconds since Unix epoch
    responseMessage: v.optional(v.string()),           // Subcontractor's response message

    // Project preview cache for quick invitation display
    projectPreview: v.optional(v.object({
      name: v.string(),                       // Cached project name
      description: v.string(),                // Cached project description
      address: v.string(),                    // Cached project address
      customerName: v.string(),               // Cached customer name
      mainContractorCompany: v.string(),      // Cached main contractor company name
      inviterName: v.string(),                // Cached inviter name
      inviterEmail: v.optional(v.string()),   // Cached inviter email
      inviterPhone: v.optional(v.string()),   // Cached inviter phone
      inviterRole: v.optional(v.string()),    // Cached inviter role/title
    })),

    // Existing fields
    isActive: v.optional(v.boolean()),        // Whether assignment is currently active (defaults to true)
    revokedAt: v.optional(timestampValidator), // When access was revoked - milliseconds since Unix epoch
    revokedBy: v.optional(v.string()),        // Clerk ID of administrator who revoked access
    notes: v.optional(v.string())             // Optional notes about the assignment
  })
    .index("by_project", ["projectId"])                           // Get all assignments for a project
    .index("by_assigned_user", ["assignedUserId"])                // Get all assignments for a user
    .index("by_project_and_user", ["projectId", "assignedUserId"]) // Check specific user's access to project
    .index("by_assigned_by", ["assignedBy"])                      // Track assignments made by administrator
    .index("by_active_status", ["isActive"])                      // Filter active/inactive assignments
    .index("by_access_level", ["accessLevel"])                    // Query by access level
    .index("by_invitation_status", ["invitationStatus"])          // Query by invitation status
    .index("by_user_and_invitation_status", ["assignedUserId", "invitationStatus"]) // Get user's invitations by status
    .index("by_expires", ["expiresAt"]),                          // Find expired invitations for cleanup

  // User-specific personal notes for projects (private to each user)
  userProjectNotes: defineTable({
    projectId: v.id("projects"),              // Reference to the project
    userId: v.string(),                       // Clerk ID of the user who owns these notes
    personalNotes: v.string(),                // User's private personal notes for this project
    createdAt: timestampValidator,            // When notes were first created - milliseconds since Unix epoch
    updatedAt: timestampValidator,            // When notes were last updated - milliseconds since Unix epoch
  })
    .index("by_user_and_project", ["userId", "projectId"])        // Get user's notes for a specific project
    .index("by_project", ["projectId"])                           // Get all notes for a project (admin use)
    .index("by_user", ["userId"]),                                // Get all notes by a user

  // Notifications for users (invitation responses, project updates, etc.)
  notifications: defineTable({
    userId: v.string(),                       // Clerk ID of the user who should receive the notification
    type: v.union(
      v.literal("invitation_declined"),      // When a subcontractor declines an invitation
      v.literal("invitation_accepted"),      // When a subcontractor accepts an invitation
      v.literal("project_update"),           // General project updates
      v.literal("team_invitation"),          // Team member invitations
      v.literal("system"),                   // System notifications
      v.literal("chat_message"),             // New chat messages
      v.literal("project_status_change"),    // Project status changes
      v.literal("role_changed")              // When a team member's role is changed
    ),
    title: v.string(),                        // Notification title
    message: v.string(),                      // Notification message/description
    data: v.optional(v.object({              // Additional structured data for the notification
      invitationId: v.optional(v.id("projectAssignments")),
      projectId: v.optional(v.id("projects")),
      projectName: v.optional(v.string()),
      subcontractorName: v.optional(v.string()),
      responseMessage: v.optional(v.string()),
      responseType: v.optional(v.union(v.literal("accept"), v.literal("decline"))), // Type of response for invitation notifications
      messageId: v.optional(v.id("messages")),  // For chat message notifications
      senderName: v.optional(v.string()),       // For chat message notifications
      oldRole: v.optional(v.string()),          // For role change notifications
      newRole: v.optional(v.string()),          // For role change notifications
      changedBy: v.optional(v.string()),        // Who changed the role
    })),
    isRead: v.boolean(),                      // Whether the notification has been read
    createdAt: timestampValidator,            // When the notification was created - milliseconds since Unix epoch
    readAt: v.optional(timestampValidator),   // When the notification was read - milliseconds since Unix epoch
  })
    .index("by_user", ["userId"])                                 // Get all notifications for a user
    .index("by_user_and_read", ["userId", "isRead"])             // Get unread notifications for a user
    .index("by_type", ["type"])                                   // Get notifications by type
    .index("by_created", ["createdAt"]),                          // Get notifications by creation time

  // Email tracking for customer notifications and other automated emails
  emailTracking: defineTable({
    // Email identification
    emailId: v.optional(v.string()),          // Resend email ID for tracking delivery status
    trackingId: v.optional(v.string()),       // Our internal tracking ID for pixel/click tracking
    emailType: v.union(
      v.literal("customer_notification"),     // Customer project notification
      v.literal("team_invitation"),           // Team member invitation
      v.literal("magic_link_invitation")      // Magic link invitation
    ),

    // Recipient information
    recipientEmail: v.string(),               // Email address of recipient
    recipientName: v.optional(v.string()),    // Name of recipient (if available)

    // Email content metadata
    subject: v.string(),                      // Email subject line
    templateUsed: v.string(),                 // Template identifier used

    // Delivery tracking
    status: v.union(
      v.literal("pending"),                   // Email queued for sending
      v.literal("sent"),                      // Successfully sent to email provider
      v.literal("delivered"),                 // Delivered to recipient's inbox
      v.literal("bounced"),                   // Email bounced (hard or soft)
      v.literal("failed"),                    // Failed to send
      v.literal("complained")                 // Recipient marked as spam
    ),

    // Timestamps
    sentAt: timestampValidator,               // When email was sent - milliseconds since Unix epoch
    deliveredAt: v.optional(timestampValidator), // When email was delivered - milliseconds since Unix epoch
    bouncedAt: v.optional(timestampValidator),   // When email bounced - milliseconds since Unix epoch
    lastStatusUpdate: timestampValidator,     // Last status update - milliseconds since Unix epoch

    // Error tracking
    errorMessage: v.optional(v.string()),     // Error message if sending failed
    bounceReason: v.optional(v.string()),     // Reason for bounce (if applicable)

    // Related entities
    projectId: v.optional(v.id("projects")), // Related project (for customer notifications)
    userId: v.string(),                       // User who triggered the email (contractor/admin)
    customerId: v.optional(v.id("customers")), // Related customer (for customer notifications)

    // Project access tracking (reliable customer engagement)
    projectAccessedAt: v.optional(timestampValidator), // When customer first accessed project
    projectAccessCount: v.optional(v.number()),        // Number of times customer accessed project

    // Legacy fields (deprecated - kept for backward compatibility)
    openedAt: v.optional(timestampValidator), // DEPRECATED: Unreliable email open tracking
    clickedAt: v.optional(timestampValidator), // DEPRECATED: Replaced with project access tracking
    openCount: v.optional(v.number()),        // DEPRECATED: Unreliable email open tracking
    clickCount: v.optional(v.number())        // DEPRECATED: Replaced with project access tracking
  })
    .index("by_email_id", ["emailId"])
    .index("by_tracking_id", ["trackingId"])
    .index("by_recipient", ["recipientEmail"])
    .index("by_status", ["status"])
    .index("by_type", ["emailType"])
    .index("by_project", ["projectId"])
    .index("by_user", ["userId"])
    .index("by_customer", ["customerId"])
    .index("by_sent_date", ["sentAt"])
    .index("by_status_and_type", ["status", "emailType"]),

  // Link previews for OpenGraph metadata caching
  linkPreviews: defineTable({
    url: v.string(),                          // Original URL
    title: v.optional(v.string()),            // OpenGraph title
    description: v.optional(v.string()),      // OpenGraph description
    image: v.optional(v.string()),            // OpenGraph image URL
    siteName: v.optional(v.string()),         // OpenGraph site name
    type: v.optional(v.string()),             // OpenGraph type (article, website, etc.)
    domain: v.string(),                       // Extracted domain for display
    favicon: v.optional(v.string()),          // Site favicon URL
    cachedAt: timestampValidator,             // When data was cached - milliseconds since Unix epoch
    expiresAt: timestampValidator,            // When cache expires - milliseconds since Unix epoch
    fetchError: v.optional(v.string())        // Error message if fetch failed
  })
    .index("by_url", ["url"])                 // Get cached data by URL
    .index("by_expires", ["expiresAt"])       // For cleanup of expired cache entries
    .index("by_domain", ["domain"]),          // Group by domain for analytics

  // Subcontractor-specific project archives
  // Allows subcontractors to archive projects locally without affecting main contractor's view
  subcontractorArchives: defineTable({
    userId: v.string(),                       // Clerk ID of the subcontractor user
    projectId: v.id("projects"),              // Reference to the project
    isArchived: v.boolean(),                  // Whether project is archived for this subcontractor
    archivedAt: v.optional(timestampValidator), // When project was archived - milliseconds since Unix epoch
    archivedBy: v.optional(v.string()),       // Clerk ID of user who archived it
    restoredAt: v.optional(timestampValidator), // When project was restored - milliseconds since Unix epoch
    restoredBy: v.optional(v.string())        // Clerk ID of user who restored it
  })
    .index("by_user", ["userId"])             // Get all archives for a user
    .index("by_project", ["projectId"])       // Get all archives for a project
    .index("by_user_and_project", ["userId", "projectId"]) // Get specific user-project archive
    .index("by_archived_status", ["isArchived"]), // Filter by archive status

  // Stripe subscription management
  subscriptions: defineTable({
    userId: v.optional(v.string()),           // Clerk user ID (may be resolved later from Stripe)

    // Stripe identifiers
    stripeCustomerId: v.string(),             // Stripe customer ID
    stripeSubscriptionId: v.optional(v.string()), // Stripe subscription ID (null during trial)
    stripePriceId: v.optional(v.string()),    // Stripe price ID for current plan
    stripeProductId: v.optional(v.string()),  // Stripe product ID

    // Subscription details
    status: v.union(
      v.literal("trialing"),
      v.literal("active"),
      v.literal("past_due"),
      v.literal("canceled"),
      v.literal("incomplete"),
      v.literal("incomplete_expired"),
      v.literal("unpaid"),
      v.literal("paused")
    ),

    // Plan information
    planLevel: v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise")),
    billingInterval: v.union(v.literal("month"), v.literal("year")),
    seats: v.optional(v.number()),            // Number of seats (for seat management)

    // Timing
    trialStart: v.optional(timestampValidator), // Trial start time
    trialEnd: v.optional(timestampValidator),   // Trial end time
    currentPeriodStart: timestampValidator,     // Current billing period start
    currentPeriodEnd: timestampValidator,       // Current billing period end

    // Cancellation
    cancelAt: v.optional(timestampValidator),   // Scheduled cancellation time
    cancelAtPeriodEnd: v.optional(v.boolean()), // Cancel at period end flag
    canceledAt: v.optional(timestampValidator), // Actual cancellation time

    // Payment status
    latestInvoiceStatus: v.optional(v.union(
      v.literal("draft"),
      v.literal("open"),
      v.literal("paid"),
      v.literal("uncollectible"),
      v.literal("void")
    )),
    collectionMethod: v.optional(v.union(v.literal("charge_automatically"), v.literal("send_invoice"))),

    // Plan change tracking
    previousPlanLevel: v.optional(v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise"))),
    planChangedAt: v.optional(timestampValidator), // When plan was last changed

    // Lifecycle tracking
    trialConvertedAt: v.optional(timestampValidator), // When trial converted to paid
    reactivatedAt: v.optional(timestampValidator),   // When subscription was reactivated

    // Downgrade management
    downgradeStage: v.optional(v.number()),           // Current downgrade stage (0=active, 1=grace, 2=limited, 3=suspended)
    gracePeriodEnd: v.optional(timestampValidator),   // When grace period ends
    accessSuspendedAt: v.optional(timestampValidator), // When access was suspended due to payment failure

    // Checkout tracking
    checkoutSessionId: v.optional(v.string()),      // Last checkout session ID
    checkoutCompletedAt: v.optional(timestampValidator), // When checkout was completed
    paymentStatus: v.optional(v.string()),          // Last payment status from checkout

    // Portal and webhook tracking
    portalUrl: v.optional(v.string()),         // Last generated portal URL
    lastWebhookEventId: v.optional(v.string()), // Last processed webhook event ID

    createdAt: timestampValidator,
    updatedAt: timestampValidator,
  })
    .index("by_user", ["userId"])
    .index("by_stripe_customer", ["stripeCustomerId"])
    .index("by_stripe_subscription", ["stripeSubscriptionId"])
    .index("by_status", ["status"])
    .index("by_trial_end", ["trialEnd"]),

  // Trial notifications tracking
  trialNotifications: defineTable({
    userId: v.string(),                       // Clerk user ID
    type: v.union(
      v.literal("trial_reminder_day_3"),
      v.literal("trial_reminder_day_5"),
      v.literal("trial_reminder_24h"),
      v.literal("trial_expired"),
      v.literal("payment_failed_0d"),
      v.literal("payment_failed_3d"),
      v.literal("payment_failed_5d"),
      v.literal("grace_period_reminder")
    ),
    sentAt: timestampValidator,               // When notification was sent
    emailSent: v.boolean(),                   // Whether email was successfully sent
    inAppNotificationRead: v.optional(v.boolean()), // Whether in-app notification was read
  })
    .index("by_user", ["userId"])
    .index("by_type_and_user", ["type", "userId"]),

  // Webhook event tracking for idempotency
  webhookEvents: defineTable({
    eventId: v.string(),                      // Stripe event ID
    eventType: v.string(),                    // Stripe event type
    processedAt: timestampValidator,          // When event was processed
  })
    .index("by_event_id", ["eventId"]),

  // Webhook error logging for monitoring and debugging
  webhookErrorLogs: defineTable({
    severity: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("critical")
    ),                                        // Error severity level
    category: v.string(),                     // Error category (signature_verification, etc.)
    eventId: v.optional(v.string()),          // Stripe event ID if available
    eventType: v.optional(v.string()),        // Stripe event type if available
    error: v.string(),                        // Error message
    stack: v.optional(v.string()),            // Error stack trace
    context: v.any(),                         // Additional context data
    processingTimeMs: v.optional(v.number()), // Processing time when error occurred
    retryAttempt: v.optional(v.number()),     // Retry attempt number
    maxRetries: v.optional(v.number()),       // Maximum retry attempts
    timestamp: timestampValidator,            // When error occurred
  })
    .index("by_severity", ["severity"])
    .index("by_category", ["category"])
    .index("by_timestamp", ["timestamp"])
    .index("by_event_type", ["eventType"])
    .index("by_severity_and_timestamp", ["severity", "timestamp"]),

  // Seat usage tracking for hard limit enforcement
  seatUsageHistory: defineTable({
    subscriptionId: v.id("subscriptions"),    // Reference to subscription
    userId: v.string(),                       // Clerk user ID who performed action
    action: v.union(
      v.literal("seat_added"),
      v.literal("seat_removed"),
      v.literal("invitation_blocked"),
      v.literal("plan_upgraded")
    ),
    seatCount: v.number(),                    // Seat count after action
    planLevel: v.string(),                    // Plan level at time of action
    timestamp: timestampValidator,            // When action occurred
    metadata: v.optional(v.object({
      invitedUserEmail: v.optional(v.string()),
      blockedReason: v.optional(v.string()),
      previousPlan: v.optional(v.string()),
      newPlan: v.optional(v.string()),
    })),
  })
    .index("by_subscription", ["subscriptionId"])
    .index("by_timestamp", ["timestamp"]),

  // Seat notifications (simplified for hard limit)
  seatNotifications: defineTable({
    subscriptionId: v.id("subscriptions"),    // Reference to subscription
    userId: v.string(),                       // Clerk user ID
    type: v.union(
      v.literal("approaching_limit"),         // 80% capacity
      v.literal("critical_limit"),            // 90% capacity
      v.literal("limit_reached"),             // 100% capacity - blocked
      v.literal("plan_upgraded")              // Successful upgrade
    ),
    currentSeats: v.number(),                 // Current seat count
    maxSeats: v.number(),                     // Maximum seats for plan
    planLevel: v.string(),                    // Plan level
    sentAt: timestampValidator,               // When notification was sent
    acknowledged: v.optional(v.boolean()),    // Whether user acknowledged notification
  })
    .index("by_subscription", ["subscriptionId"])
    .index("by_type", ["type"]),

  // Subscription history for plan changes and updates
  subscriptionHistory: defineTable({
    subscriptionId: v.id("subscriptions"),    // Reference to subscription
    userId: v.string(),                       // Clerk user ID who performed action
    action: v.union(
      v.literal("plan_changed_during_trial"),
      v.literal("billing_interval_changed"),
      v.literal("subscription_activated"),
      v.literal("subscription_canceled"),
      v.literal("trial_started"),
    ),
    oldPlanLevel: v.optional(v.union(
      v.literal("basic"),
      v.literal("professional"),
      v.literal("enterprise")
    )),
    newPlanLevel: v.optional(v.union(
      v.literal("basic"),
      v.literal("professional"),
      v.literal("enterprise")
    )),
    oldBillingInterval: v.optional(v.union(
      v.literal("month"),
      v.literal("year")
    )),
    newBillingInterval: v.optional(v.union(
      v.literal("month"),
      v.literal("year")
    )),
    timestamp: timestampValidator,            // When action occurred
    metadata: v.optional(v.object({
      reason: v.optional(v.string()),
      trialPreserved: v.optional(v.boolean()),
    })),
  })
    .index("by_subscription", ["subscriptionId"])
    .index("by_user", ["userId"])
    .index("by_timestamp", ["timestamp"]),

  // Subscription events for analytics and debugging
  subscriptionEvents: defineTable({
    subscriptionId: v.string(),                   // Reference to subscription (string ID, not Convex ID)
    eventType: v.union(
      v.literal("subscription_created"),
      v.literal("subscription_updated"),
      v.literal("subscription_canceled"),
      v.literal("subscription_reactivated"),
      v.literal("trial_started"),
      v.literal("trial_ending"),
      v.literal("trial_converted"),
      v.literal("payment_succeeded"),
      v.literal("payment_failed"),
      v.literal("invoice_paid"),
      v.literal("invoice_failed"),
      v.literal("plan_upgraded"),
      v.literal("plan_downgraded"),
      v.literal("billing_interval_changed"),
      v.literal("checkout_completed"),
      v.literal("webhook_processed"),
      v.literal("access_granted"),
      v.literal("access_revoked"),
      v.literal("downgrade_initiated"),
      v.literal("downgrade_escalated"),
      v.literal("downgrade_resolved"),
      v.literal("dunning_started"),
      v.literal("dunning_escalated"),
      v.literal("dunning_resolved"),
      v.literal("dunning_cancelled"),
      v.literal("payment_retry_started"),
      v.literal("payment_retry_succeeded"),
      v.literal("payment_retry_exhausted"),
      v.literal("payment_retry_escalated"),
      v.literal("subscription_synced")
    ),
    eventData: v.any(),                           // Event-specific data (flexible structure)
    timestamp: timestampValidator,                // When event occurred
    source: v.optional(v.union(
      v.literal("stripe_webhook"),
      v.literal("user_action"),
      v.literal("system_action"),
      v.literal("admin_action")
    )),
    metadata: v.optional(v.object({
      stripeEventId: v.optional(v.string()),      // Stripe event ID if from webhook
      userId: v.optional(v.string()),             // User who triggered the event
      ipAddress: v.optional(v.string()),          // IP address for user actions
      userAgent: v.optional(v.string()),          // User agent for user actions
      previousValue: v.optional(v.any()),         // Previous value for updates
      newValue: v.optional(v.any()),              // New value for updates
    })),
  })
    .index("by_subscription", ["subscriptionId"])
    .index("by_event_type", ["eventType"])
    .index("by_timestamp", ["timestamp"])
    .index("by_source", ["source"])
    .index("by_subscription_and_type", ["subscriptionId", "eventType"])
    .index("by_subscription_and_timestamp", ["subscriptionId", "timestamp"]),

  // Dunning management for payment failure handling
  dunningAttempts: defineTable({
    subscriptionId: v.id("subscriptions"),        // Reference to subscription
    userId: v.string(),                           // Clerk user ID
    stripeCustomerId: v.string(),                 // Stripe customer ID
    stripeInvoiceId: v.optional(v.string()),      // Related invoice ID
    attemptNumber: v.number(),                    // Current attempt number (1, 2, 3, etc.)
    maxAttempts: v.number(),                      // Maximum attempts configured
    status: v.union(
      v.literal("pending"),                       // Waiting for retry
      v.literal("processing"),                    // Currently processing
      v.literal("completed"),                     // Successfully resolved
      v.literal("failed"),                        // Failed after all attempts
      v.literal("cancelled")                      // Manually cancelled
    ),
    failureReason: v.optional(v.string()),        // Reason for payment failure
    nextRetryAt: v.optional(timestampValidator),  // When to retry next
    lastAttemptAt: v.optional(timestampValidator), // Last attempt timestamp
    escalationLevel: v.union(
      v.literal("initial"),                       // First failure
      v.literal("reminder"),                      // Follow-up reminders
      v.literal("urgent"),                        // Urgent notices
      v.literal("final"),                         // Final notice before suspension
      v.literal("suspended")                      // Account suspended
    ),
    communicationsSent: v.array(v.object({
      type: v.union(
        v.literal("email"),
        v.literal("in_app"),
        v.literal("sms")
      ),
      template: v.string(),                       // Template used
      sentAt: timestampValidator,                 // When sent
      status: v.union(
        v.literal("sent"),
        v.literal("delivered"),
        v.literal("failed"),
        v.literal("bounced")
      ),
    })),
    metadata: v.optional(v.object({
      originalAmount: v.optional(v.number()),     // Original invoice amount
      currency: v.optional(v.string()),           // Currency
      paymentMethod: v.optional(v.string()),      // Payment method that failed
      declineCode: v.optional(v.string()),        // Stripe decline code
      customerNotes: v.optional(v.string()),      // Internal notes
    })),
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
  })
    .index("by_subscription", ["subscriptionId"])
    .index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_escalation_level", ["escalationLevel"])
    .index("by_next_retry", ["nextRetryAt"])
    .index("by_stripe_customer", ["stripeCustomerId"])
    .index("by_subscription_and_status", ["subscriptionId", "status"]),

  // Dunning configuration and rules
  dunningConfigurations: defineTable({
    planLevel: v.union(
      v.literal("basic"),
      v.literal("professional"),
      v.literal("enterprise"),
      v.literal("default")                        // Default configuration
    ),
    maxAttempts: v.number(),                      // Maximum retry attempts
    retryIntervals: v.array(v.number()),          // Retry intervals in hours
    escalationRules: v.array(v.object({
      attemptNumber: v.number(),                  // At which attempt to escalate
      escalationLevel: v.union(
        v.literal("initial"),
        v.literal("reminder"),
        v.literal("urgent"),
        v.literal("final"),
        v.literal("suspended")
      ),
      communicationTemplates: v.array(v.string()), // Templates to send
      gracePeriodHours: v.optional(v.number()),   // Grace period before next action
    })),
    suspensionRules: v.object({
      suspendAfterAttempts: v.number(),           // Suspend after X failed attempts
      gracePeriodDays: v.number(),                // Grace period before suspension
      allowReactivation: v.boolean(),             // Allow self-reactivation
    }),
    isActive: v.boolean(),                        // Whether this config is active
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
  })
    .index("by_plan_level", ["planLevel"])
    .index("by_active", ["isActive"]),

  // Payment retry tracking and management
  paymentRetryAttempts: defineTable({
    subscriptionId: v.id("subscriptions"),        // Reference to subscription
    userId: v.string(),                           // Clerk user ID
    stripeCustomerId: v.string(),                 // Stripe customer ID
    stripeInvoiceId: v.string(),                  // Stripe invoice ID being retried
    stripePaymentIntentId: v.optional(v.string()), // Payment intent if available
    attemptNumber: v.number(),                    // Current retry attempt (1, 2, 3, etc.)
    maxRetryAttempts: v.number(),                 // Maximum retries for this failure type
    status: v.union(
      v.literal("pending"),                       // Waiting for retry
      v.literal("processing"),                    // Currently processing retry
      v.literal("succeeded"),                     // Retry succeeded
      v.literal("failed"),                        // Retry failed
      v.literal("exhausted"),                     // All retries exhausted
      v.literal("cancelled")                      // Manually cancelled
    ),
    failureType: v.union(
      v.literal("card_declined"),                 // Card declined by issuer
      v.literal("insufficient_funds"),            // Insufficient funds
      v.literal("expired_card"),                  // Card expired
      v.literal("incorrect_cvc"),                 // Incorrect CVC
      v.literal("processing_error"),              // Processing error
      v.literal("authentication_required"),      // 3D Secure required
      v.literal("generic_decline"),               // Generic decline
      v.literal("network_error"),                 // Network/connectivity issue
      v.literal("unknown")                        // Unknown failure type
    ),
    originalFailureReason: v.string(),            // Original Stripe failure message
    declineCode: v.optional(v.string()),          // Stripe decline code
    nextRetryAt: v.optional(timestampValidator),  // When to attempt next retry
    lastRetryAt: v.optional(timestampValidator),  // Last retry attempt timestamp
    retryIntervals: v.array(v.number()),          // Retry intervals in minutes
    currentIntervalIndex: v.number(),             // Current position in retry intervals
    retryResults: v.array(v.object({
      attemptNumber: v.number(),                  // Which attempt this was
      attemptedAt: timestampValidator,            // When retry was attempted
      result: v.union(
        v.literal("succeeded"),
        v.literal("failed"),
        v.literal("error")
      ),
      failureReason: v.optional(v.string()),      // Failure reason if failed
      stripeErrorCode: v.optional(v.string()),    // Stripe error code
      processingTimeMs: v.optional(v.number()),   // How long retry took
    })),
    metadata: v.optional(v.object({
      originalAmount: v.optional(v.number()),     // Original invoice amount
      currency: v.optional(v.string()),           // Currency
      paymentMethodType: v.optional(v.string()),  // Payment method type
      customerEmail: v.optional(v.string()),      // Customer email
      planLevel: v.optional(v.string()),          // Subscription plan level
    })),
    escalatedToDunning: v.optional(v.boolean()),  // Whether escalated to dunning
    escalatedAt: v.optional(timestampValidator),  // When escalated to dunning
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
  })
    .index("by_subscription", ["subscriptionId"])
    .index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_failure_type", ["failureType"])
    .index("by_next_retry", ["nextRetryAt"])
    .index("by_stripe_invoice", ["stripeInvoiceId"])
    .index("by_subscription_and_status", ["subscriptionId", "status"])
    .index("by_timestamp", ["createdAt"]),

  // Payment retry configuration rules
  paymentRetryConfigurations: defineTable({
    failureType: v.union(
      v.literal("card_declined"),
      v.literal("insufficient_funds"),
      v.literal("expired_card"),
      v.literal("incorrect_cvc"),
      v.literal("processing_error"),
      v.literal("authentication_required"),
      v.literal("generic_decline"),
      v.literal("network_error"),
      v.literal("unknown"),
      v.literal("default")                        // Default configuration
    ),
    planLevel: v.optional(v.union(
      v.literal("basic"),
      v.literal("professional"),
      v.literal("enterprise")
    )),                                           // Plan-specific config (optional)
    maxRetryAttempts: v.number(),                 // Maximum retry attempts
    retryIntervals: v.array(v.number()),          // Retry intervals in minutes
    backoffStrategy: v.union(
      v.literal("fixed"),                         // Fixed intervals
      v.literal("exponential"),                   // Exponential backoff
      v.literal("linear")                         // Linear increase
    ),
    shouldRetry: v.boolean(),                     // Whether to retry this failure type
    escalateToDunning: v.boolean(),               // Escalate to dunning after exhaustion
    notifyCustomer: v.boolean(),                  // Send customer notifications
    priority: v.number(),                         // Configuration priority (higher = more specific)
    isActive: v.boolean(),                        // Whether this config is active
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
  })
    .index("by_failure_type", ["failureType"])
    .index("by_plan_level", ["planLevel"])
    .index("by_priority", ["priority"])
    .index("by_active", ["isActive"]),

  // Payment notification preferences
  paymentNotificationPreferences: defineTable({
    userId: v.string(),                           // Clerk user ID
    emailNotifications: v.boolean(),              // Enable email notifications
    smsNotifications: v.optional(v.boolean()),    // Enable SMS notifications (future)
    notificationTypes: v.object({
      paymentFailed: v.boolean(),                 // Notify on payment failures
      retryAttempts: v.boolean(),                 // Notify on retry attempts
      retryExhausted: v.boolean(),                // Notify when retries exhausted
      subscriptionSuspended: v.boolean(),         // Notify on suspension
      paymentSucceeded: v.boolean(),              // Notify on successful payments
    }),
    frequency: v.union(
      v.literal("immediate"),                     // Send immediately
      v.literal("daily_digest"),                  // Daily summary
      v.literal("weekly_digest")                  // Weekly summary
    ),
    lastNotificationSent: v.optional(timestampValidator), // Last notification timestamp
    unsubscribeToken: v.string(),                // Token for unsubscribe links
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
  })
    .index("by_user", ["userId"])
    .index("by_unsubscribe_token", ["unsubscribeToken"]),

  // Payment notification history
  paymentNotificationHistory: defineTable({
    userId: v.string(),                           // Clerk user ID
    notificationType: v.string(),                 // Type of notification sent
    failureType: v.optional(v.string()),          // Payment failure type if applicable
    emailResult: v.optional(v.any()),             // Email service response
    sentAt: timestampValidator,                   // When notification was sent
    deliveredAt: v.optional(timestampValidator),  // When notification was delivered
    openedAt: v.optional(timestampValidator),     // When notification was opened
    clickedAt: v.optional(timestampValidator),    // When links were clicked
    bounced: v.optional(v.boolean()),             // Whether email bounced
    bounceReason: v.optional(v.string()),         // Bounce reason if applicable
  })
    .index("by_user", ["userId"])
    .index("by_sent_at", ["sentAt"])
    .index("by_notification_type", ["notificationType"]),

  // Subscription downgrade tracking
  subscriptionDowngrades: defineTable({
    subscriptionId: v.id("subscriptions"),            // Related subscription
    userId: v.string(),                               // Clerk user ID
    currentStage: v.number(),                         // Current downgrade stage (0-3)
    stageName: v.string(),                            // Stage name for readability
    reason: v.string(),                               // Reason for downgrade
    triggeredBy: v.string(),                          // What triggered the downgrade
    status: v.union(
      v.literal("active"),                            // Downgrade in progress
      v.literal("resolved"),                          // Downgrade resolved
      v.literal("cancelled")                          // Downgrade cancelled
    ),
    gracePeriodEnd: v.optional(timestampValidator),   // When grace period ends
    nextEscalationAt: v.optional(timestampValidator), // When to escalate to next stage
    escalatedAt: v.optional(timestampValidator),      // When last escalated
    resolvedAt: v.optional(timestampValidator),       // When resolved
    resolvedBy: v.optional(v.string()),               // How it was resolved
    restrictions: v.object({
      canCreateProjects: v.boolean(),
      canAccessProjects: v.boolean(),
      canUploadFiles: v.boolean(),
      canInviteTeamMembers: v.boolean(),
      canShareProjects: v.boolean(),
      canExportData: v.boolean(),
      maxProjects: v.optional(v.number()),
      maxTeamMembers: v.optional(v.number()),
    }),
    metadata: v.optional(v.any()),                    // Additional metadata
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
  })
    .index("by_subscription", ["subscriptionId"])
    .index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_next_escalation", ["nextEscalationAt"])
    .index("by_stage", ["currentStage"]),

  // Plan change requests
  planChangeRequests: defineTable({
    userId: v.string(),
    subscriptionId: v.id("subscriptions"),
    stripeSubscriptionId: v.string(),
    currentPlanId: v.string(),
    currentBillingInterval: v.union(v.literal("month"), v.literal("year")),
    newPlanId: v.string(),
    newBillingInterval: v.union(v.literal("month"), v.literal("year")),
    status: v.string(), // pending, processing, completed, failed
    errorMessage: v.optional(v.string()),
    stripeInvoiceId: v.optional(v.string()),
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
    completedAt: v.optional(timestampValidator),
  })
    .index("by_user", ["userId"])
    .index("by_subscription", ["subscriptionId"])
    .index("by_status", ["status"])
    .index("by_created_at", ["createdAt"]),

  // Subscription cancellation requests
  subscriptionCancellations: defineTable({
    userId: v.string(),
    subscriptionId: v.id("subscriptions"),
    stripeSubscriptionId: v.string(),
    reason: v.string(),
    reasonDetails: v.optional(v.string()),
    wouldRecommend: v.boolean(),
    improvementSuggestions: v.optional(v.string()),
    cancelImmediately: v.boolean(),
    status: v.string(), // pending, processing, completed, failed
    errorMessage: v.optional(v.string()),
    stripeInvoiceId: v.optional(v.string()),
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
    completedAt: v.optional(timestampValidator),
  })
    .index("by_user", ["userId"])
    .index("by_subscription", ["subscriptionId"])
    .index("by_status", ["status"])
    .index("by_created_at", ["createdAt"]),

  // Cancellation feedback for analysis
  cancellationFeedback: defineTable({
    userId: v.string(),
    reason: v.string(),
    reasonDetails: v.optional(v.string()),
    wouldRecommend: v.boolean(),
    improvementSuggestions: v.optional(v.string()),
    cancelImmediately: v.boolean(),
    createdAt: timestampValidator,
  })
    .index("by_user", ["userId"])
    .index("by_reason", ["reason"])
    .index("by_created_at", ["createdAt"]),

  // Subscription reactivation requests
  subscriptionReactivations: defineTable({
    userId: v.string(),
    originalSubscriptionId: v.id("subscriptions"),
    newPlanId: v.string(),
    newBillingInterval: v.union(v.literal("month"), v.literal("year")),
    paymentMethodId: v.optional(v.string()),
    welcomeBackDiscount: v.number(), // Percentage discount for first billing period
    status: v.string(), // pending, processing, completed, failed
    errorMessage: v.optional(v.string()),
    newSubscriptionId: v.optional(v.id("subscriptions")),
    stripeSubscriptionId: v.optional(v.string()),
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
    completedAt: v.optional(timestampValidator),
  })
    .index("by_user", ["userId"])
    .index("by_original_subscription", ["originalSubscriptionId"])
    .index("by_status", ["status"])
    .index("by_created_at", ["createdAt"]),

  // Billing portal configurations
  billingPortalConfigurations: defineTable({
    configurationId: v.string(),
    configuration: v.any(), // Stripe portal configuration object
    isActive: v.boolean(),
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
  })
    .index("by_configuration_id", ["configurationId"])
    .index("by_active", ["isActive"]),

  // Billing portal sessions
  billingPortalSessions: defineTable({
    userId: v.string(),
    subscriptionId: v.id("subscriptions"),
    stripeCustomerId: v.string(),
    requestedFlow: v.optional(v.string()), // subscription_cancel, subscription_update, etc.
    returnUrl: v.optional(v.string()),
    locale: v.string(),
    status: v.string(), // pending, processing, completed, failed
    portalUrl: v.optional(v.string()),
    stripeSessionId: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
    returnedAt: v.optional(timestampValidator),
    returnFlow: v.optional(v.string()),
    returnSessionId: v.optional(v.string()),
    createdAt: timestampValidator,
    updatedAt: timestampValidator,
    completedAt: v.optional(timestampValidator),
  })
    .index("by_user", ["userId"])
    .index("by_subscription", ["subscriptionId"])
    .index("by_status", ["status"])
    .index("by_created_at", ["createdAt"]),
});
