import { query, action } from './_generated/server';
import { v } from 'convex/values';

/**
 * Simple test to check current subscription status
 */
export const checkStatus = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    return {
      subscription: subscription ? {
        status: subscription.status,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
      } : null,
      user: user ? {
        subscriptionStatus: user.subscriptionStatus,
        trialConvertedAt: user.trialConvertedAt,
      } : null,
      shouldBeActive: subscription?.trialEnd ? Date.now() >= subscription.trialEnd : false,
    };
  }
});

/**
 * Simple fix action
 */
export const simpleFix = action({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    try {
      // Use the emergency fix
      const result = await ctx.runAction("quickFix:emergencyFixSubscriptionStatus", {
        userId: args.userId,
        reason: "Simple fix - manual activation"
      });
      
      return result;
    } catch (error) {
      return {
        success: false,
        error: error.message,
        userId: args.userId,
      };
    }
  }
});
