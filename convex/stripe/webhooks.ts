import Strip<PERSON> from "stripe";
import { internal } from "../_generated/api";
import { httpAction } from "../_generated/server";

// ===== ERROR LOGGING SYSTEM =====

/**
 * Error severity levels for webhook processing
 */
enum ErrorSeverity {
  LOW = "low",           // Minor issues, warnings
  MEDIUM = "medium",     // Recoverable errors, retries
  HIGH = "high",         // Critical errors affecting functionality
  CRITICAL = "critical"  // System-level failures, immediate attention required
}

/**
 * Error categories for webhook processing
 */
enum ErrorCategory {
  SIGNATURE_VERIFICATION = "signature_verification",
  IDEMPOTENCY_CHECK = "idempotency_check",
  EVENT_PROCESSING = "event_processing",
  DATABASE_ERROR = "database_error",
  STRIPE_API_ERROR = "stripe_api_error",
  CONFIGURATION_ERROR = "configuration_error",
  NETWORK_ERROR = "network_error",
  VALIDATION_ERROR = "validation_error",
  TIMEOUT_ERROR = "timeout_error",
  UNKNOWN_ERROR = "unknown_error"
}

/**
 * Structured error logging interface
 */
interface WebhookErrorLog {
  severity: ErrorSeverity;
  category: ErrorCategory;
  eventId?: string;
  eventType?: string;
  error: string;
  stack?: string;
  context: Record<string, any>;
  timestamp: string;
  processingTimeMs?: number;
  retryAttempt?: number;
  maxRetries?: number;
}

/**
 * Enhanced error logging function with structured data
 */
function logWebhookError(errorLog: WebhookErrorLog): void {
  const logEntry = {
    ...errorLog,
    service: "stripe-webhook",
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development"
  };

  // Use appropriate console method based on severity
  switch (errorLog.severity) {
    case ErrorSeverity.CRITICAL:
      console.error("🚨 CRITICAL WEBHOOK ERROR:", logEntry);
      break;
    case ErrorSeverity.HIGH:
      console.error("❌ HIGH SEVERITY WEBHOOK ERROR:", logEntry);
      break;
    case ErrorSeverity.MEDIUM:
      console.warn("⚠️ MEDIUM SEVERITY WEBHOOK ERROR:", logEntry);
      break;
    case ErrorSeverity.LOW:
      console.log("ℹ️ LOW SEVERITY WEBHOOK WARNING:", logEntry);
      break;
    default:
      console.error("❓ UNKNOWN SEVERITY WEBHOOK ERROR:", logEntry);
  }
}

/**
 * Enhanced success logging function with structured data
 */
function logWebhookSuccess(eventId: string, eventType: string, processingTimeMs: number, context: Record<string, any> = {}): void {
  const logEntry = {
    level: "success",
    eventId,
    eventType,
    processingTimeMs,
    timestamp: new Date().toISOString(),
    service: "stripe-webhook",
    version: "1.0.0",
    environment: process.env.NODE_ENV || "development",
    context
  };

  console.log("✅ WEBHOOK SUCCESS:", logEntry);
}

/**
 * Stripe Webhook Handler for JobbLogg
 *
 * This file handles incoming webhook events from Stripe with proper signature verification.
 *
 * Setup Instructions:
 * 1. Create a webhook endpoint in Stripe Dashboard
 * 2. Set the endpoint URL to: https://your-domain.convex.site/stripe (production/dev)
 * 3. Copy the webhook signing secret (starts with whsec_) to STRIPE_WEBHOOK_SECRET environment variable
 * 4. Configure the webhook to send the required events (see REQUIRED_WEBHOOK_EVENTS in config.ts)
 *
 * Testing with Stripe CLI:
 * 1. Install Stripe CLI: https://stripe.com/docs/stripe-cli
 * 2. Login: stripe login
 * 3. Forward events (local dev): stripe listen --forward-to localhost:3000/stripe
 * 4. Trigger test events: stripe trigger checkout.session.completed
 */

// Initialize Stripe function
function getStripe() {
  const secretKey = process.env.STRIPE_SECRET_KEY;
  if (!secretKey || secretKey === 'sk_test_your_secret_key_here') {
    throw new Error("Stripe secret key not configured");
  }
  return new Stripe(secretKey, {
    apiVersion: "2025-07-30.basil",
  });
}

// Removed testStripeWebhook handler (deprecated)

// Main webhook handler with signature verification
export const handleStripeWebhook = httpAction(async (ctx, request) => {
  console.log("🎯 Stripe webhook proxy called!");

  if (request.method !== "POST") {
    return new Response("Method Not Allowed", { status: 405 });
  }

  try {
    const signature = request.headers.get("stripe-signature") ?? "";
    const payload = await request.text(); // raw body

    const result = await ctx.runAction(internal.stripe.fulfill.fulfill, {
      signature,
      payload,
    });

    return new Response(JSON.stringify({ ok: true, result }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("❌ Proxy error forwarding to fulfill action:", error);
    // Always 200 to avoid Stripe retries, but include error in body for logs
    return new Response(JSON.stringify({ ok: true, error: String(error) }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }
});

/**
 * Process webhook event with proper routing to specific handlers
 *
 * This function routes Stripe webhook events to their appropriate handlers
 * based on the event type. Each handler is responsible for updating the
 * database and performing any necessary business logic.
 */
async function processWebhookEvent(ctx: any, event: Stripe.Event): Promise<void> {
  console.log(`🔄 Processing webhook event: ${event.type} (${event.id})`);

  // Route events to their specific handlers
  switch (event.type) {
    case "customer.created":
      await handleCustomerCreated(ctx, event);
      break;

    case "checkout.session.completed":
      await handleCheckoutSessionCompleted(ctx, event);
      break;

    case "customer.subscription.created":
      await handleSubscriptionCreated(ctx, event);
      break;

    case "customer.subscription.updated":
      await handleSubscriptionUpdated(ctx, event);
      break;

    case "customer.subscription.deleted":
      await handleSubscriptionDeleted(ctx, event);
      break;

    case "customer.subscription.trial_will_end":
      await handleTrialWillEnd(ctx, event);
      break;

    case "invoice.paid":
      await handleInvoicePaid(ctx, event);
      break;

    case "invoice.payment_failed":
      await handleInvoicePaymentFailed(ctx, event);
      break;

    case "payment_intent.succeeded":
      await handlePaymentIntentSucceeded(ctx, event);
      break;

    case "payment_intent.payment_failed":
      await handlePaymentIntentFailed(ctx, event);
      break;

    default:
      logWebhookError({
        severity: ErrorSeverity.LOW,
        category: ErrorCategory.VALIDATION_ERROR,
        eventId: event.id,
        eventType: event.type,
        error: `Unhandled webhook event type: ${event.type}`,
        context: {
          eventData: event.data,
          supportedEventTypes: [
            "customer.created",
            "checkout.session.completed",
            "customer.subscription.created",
            "customer.subscription.updated",
            "customer.subscription.deleted",
            "customer.subscription.trial_will_end",
            "invoice.paid",
            "invoice.payment_failed",
            "payment_intent.succeeded",
            "payment_intent.payment_failed"
          ],
          recommendation: "Add handler for this event type if needed"
        },
        timestamp: new Date().toISOString()
      });
      // Don't throw error for unhandled events - just log and continue
      break;
  }

  console.log(`✅ Successfully processed webhook event: ${event.type} (${event.id})`);
}

// ===== WEBHOOK EVENT HANDLERS =====
// These functions handle specific Stripe webhook events
// Each handler will be implemented in subsequent tasks

/**
 * Handle customer.created events
 * Called when a new customer is created in Stripe
 */
async function handleCustomerCreated(ctx: any, event: Stripe.Event): Promise<void> {
  const customer = event.data.object as Stripe.Customer;
  console.log(`👤 Customer created: ${customer.id}`);

  // TODO: Implement customer creation logic in next tasks
  // This might involve updating user records or sending welcome emails
  console.log("🔄 Customer creation handler not yet implemented");
}

/**
 * Handle checkout.session.completed events
 * Critical for trial-to-paid conversion and subscription activation
 */
async function handleCheckoutSessionCompleted(ctx: any, event: Stripe.Event): Promise<void> {
  const session = event.data.object as Stripe.Checkout.Session;
  console.log(`💳 Checkout session completed: ${session.id}`);

  try {
    // Validate session data
    if (!session.customer) {
      throw new Error("No customer ID in checkout session");
    }

    console.log(`🔄 Processing checkout session completion:`, {
      sessionId: session.id,
      customerId: session.customer,
      subscriptionId: session.subscription,
      mode: session.mode,
      paymentStatus: session.payment_status,
      amount: session.amount_total
    });

    // If this is a subscription checkout, force-end trial immediately for instant activation
    if (session.mode === "subscription" && session.subscription) {
      const stripe = getStripe();
      const subscriptionId = typeof session.subscription === 'string'
        ? session.subscription
        : session.subscription.id;

      // End trial now if still trialing, then use the updated subscription
      const { subscription: finalSub } = await endTrialNowIfNeeded(stripe, subscriptionId);

      const stripeCustomerId = typeof finalSub.customer === 'string' ? finalSub.customer : finalSub.customer?.id;
      if (!stripeCustomerId) {
        throw new Error("Invalid customer ID on subscription after trial end check");
      }

      // Find existing subscription record by customer
      const existingSubscription = await ctx.db
        .query("subscriptions")
        .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
        .first();

      if (existingSubscription) {
        await updateExistingSubscription(ctx, existingSubscription, finalSub);
        // Also mark checkout completed fields
        await ctx.db.patch(existingSubscription._id, {
          checkoutSessionId: session.id,
          checkoutCompletedAt: Date.now(),
          paymentStatus: session.payment_status,
          updatedAt: Date.now(),
        });
      } else {
        await createNewSubscription(ctx, finalSub, stripeCustomerId);
      }
    } else if (session.mode === "payment") {
      // Handle one-time payment checkout completion
      await handlePaymentCheckoutCompletion(ctx, session);
    }

    console.log(`✅ Checkout session completion processed successfully: ${session.id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing checkout session completion:`, {
      sessionId: session.id,
      customerId: session.customer,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error; // Re-throw to trigger webhook retry
  }
}

/**
 * Handle customer.subscription.created events
 * Called when a new subscription is created - critical for subscription activation
 */
async function handleSubscriptionCreated(ctx: any, event: Stripe.Event): Promise<void> {
  const subscription = event.data.object as Stripe.Subscription;
  console.log(`📋 Subscription created: ${subscription.id}`);

  try {
    // Validate subscription data
    if (!subscription.customer) {
      throw new Error("No customer ID in subscription");
    }

    if (!subscription.items?.data?.length) {
      throw new Error("No subscription items found");
    }

    // Force-end trial for immediate activation when appropriate
    const stripe = getStripe();
    const { subscription: finalSub } = await endTrialNowIfNeeded(stripe, subscription.id);

    const stripeCustomerId = typeof finalSub.customer === 'string'
      ? finalSub.customer
      : finalSub.customer.id;

    console.log(`🔄 Processing subscription creation:`, {
      subscriptionId: subscription.id,
      customerId: stripeCustomerId,
      status: subscription.status,
      currentPeriodStart: subscription.current_period_start,
      currentPeriodEnd: subscription.current_period_end,
      trialEnd: subscription.trial_end,
      itemsCount: subscription.items.data.length
    });

    // Find existing subscription record or create new one
    let existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (existingSubscription) {
      console.log(`📋 Updating existing subscription record: ${existingSubscription._id}`);
      await updateExistingSubscription(ctx, existingSubscription, finalSub);
    } else {
      console.log(`📋 Creating new subscription record for customer: ${stripeCustomerId}`);
      await createNewSubscription(ctx, finalSub, stripeCustomerId);
    }

    // Activate user access to paid features
    await activateUserAccess(ctx, stripeCustomerId, finalSub);

    console.log(`✅ Subscription creation processed successfully: ${subscription.id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing subscription creation:`, {
      subscriptionId: subscription.id,
      customerId: subscription.customer,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error; // Re-throw to trigger webhook retry
  }
}

/**
 * Handle customer.subscription.updated events
 * Called when subscription details change (plan, status, etc.)
 */
async function handleSubscriptionUpdated(ctx: any, event: Stripe.Event): Promise<void> {
  const subscription = event.data.object as Stripe.Subscription;
  const previousAttributes = event.data.previous_attributes as Partial<Stripe.Subscription>;

  console.log(`📋 Subscription updated: ${subscription.id}`);
  console.log(`🔍 Event livemode: ${event.livemode}, Test event: ${!event.livemode}`);

  try {
    // Validate subscription data
    if (!subscription.customer) {
      throw new Error("No customer ID in subscription update");
    }

    if (!subscription.items?.data?.length) {
      throw new Error("No subscription items found in update");
    }

    // Handle test events gracefully
    if (!event.livemode) {
      console.log("🧪 Processing test event - checking for real database records");
    }

    // Force-end trial for immediate activation as a safety net
    const stripe = getStripe();
    const { subscription: finalSub } = await endTrialNowIfNeeded(stripe, subscription.id);

    const stripeCustomerId = typeof finalSub.customer === 'string'
      ? finalSub.customer
      : finalSub.customer.id;

    console.log(`🔄 Processing subscription update:`, {
      subscriptionId: subscription.id,
      customerId: stripeCustomerId,
      status: subscription.status,
      previousStatus: previousAttributes?.status,
      currentPeriodStart: subscription.current_period_start,
      currentPeriodEnd: subscription.current_period_end,
      hasChanges: Object.keys(previousAttributes || {}).length > 0
    });

    // Find existing subscription record
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!existingSubscription) {
      console.warn(`⚠️ No existing subscription found for customer: ${stripeCustomerId}`);

      // For test events, just log and return success
      if (!event.livemode) {
        console.log("🧪 Test event with no database record - skipping processing");
        return;
      }

      // For live events, create new subscription record if it doesn't exist
      await createNewSubscription(ctx, subscription, stripeCustomerId);
      return;
    }

    // Process the subscription update
    await processSubscriptionUpdate(ctx, existingSubscription, finalSub, previousAttributes);

    // Handle user access changes if plan or status changed
    if (previousAttributes?.status || hasItemChanges(finalSub, previousAttributes)) {
      await updateUserAccess(ctx, stripeCustomerId, finalSub, previousAttributes);
    }

    console.log(`✅ Subscription update processed successfully: ${subscription.id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing subscription update:`, {
      subscriptionId: subscription.id,
      customerId: subscription.customer,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error; // Re-throw to trigger webhook retry
  }
}

/**
 * Handle customer.subscription.deleted events
 * Called when a subscription is cancelled or deleted
 */
async function handleSubscriptionDeleted(ctx: any, event: Stripe.Event): Promise<void> {
  const subscription = event.data.object as Stripe.Subscription;
  console.log(`📋 Subscription deleted: ${subscription.id}`);

  const stripeCustomerId = typeof subscription.customer === 'string'
    ? subscription.customer
    : subscription.customer?.id;

  if (!stripeCustomerId) {
    console.error(`❌ No customer ID found for subscription: ${subscription.id}`);
    return;
  }

  try {
    // Find existing subscription record
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!existingSubscription) {
      console.warn(`⚠️ No subscription record found for customer: ${stripeCustomerId}`);
      return;
    }

    console.log(`🔄 Processing subscription deletion:`, {
      subscriptionId: subscription.id,
      customerId: stripeCustomerId,
      status: subscription.status,
      canceledAt: subscription.canceled_at,
      endedAt: subscription.ended_at,
    });

    // Update subscription record with cancellation details
    const updateData = {
      status: 'canceled' as const,
      canceledAt: subscription.canceled_at ? subscription.canceled_at * 1000 : Date.now(),
      endedAt: subscription.ended_at ? subscription.ended_at * 1000 : Date.now(),
      cancelAtPeriodEnd: false, // Since it's deleted, not just scheduled for cancellation
      updatedAt: Date.now(),
    };

    await ctx.db.patch(existingSubscription._id, updateData);

    // Revoke user access to paid features
    await revokeUserAccess(ctx, stripeCustomerId, subscription);

    // Log subscription cancellation for analytics
    await logSubscriptionEvent(ctx, existingSubscription._id, 'subscription_canceled', {
      reason: 'subscription_deleted_webhook',
      canceledAt: updateData.canceledAt,
      endedAt: updateData.endedAt,
    });

    console.log(`✅ Subscription deletion processed successfully: ${subscription.id}`);
  } catch (error) {
    console.error(`❌ Error processing subscription deletion:`, error);
    throw error;
  }
}

/**
 * Handle customer.subscription.trial_will_end events
 * Called when a trial period is about to end
 */
async function handleTrialWillEnd(ctx: any, event: Stripe.Event): Promise<void> {
  const subscription = event.data.object as Stripe.Subscription;
  console.log(`⏰ Trial will end: ${subscription.id}`);

  const stripeCustomerId = typeof subscription.customer === 'string'
    ? subscription.customer
    : subscription.customer?.id;

  if (!stripeCustomerId) {
    console.error(`❌ No customer ID found for subscription: ${subscription.id}`);
    return;
  }

  try {
    // Find existing subscription record
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!existingSubscription) {
      console.warn(`⚠️ No subscription record found for customer: ${stripeCustomerId}`);
      return;
    }

    const trialEnd = subscription.trial_end ? subscription.trial_end * 1000 : null;
    const daysUntilEnd = trialEnd ? Math.ceil((trialEnd - Date.now()) / (24 * 60 * 60 * 1000)) : 0;

    console.log(`🔄 Processing trial ending notification:`, {
      subscriptionId: subscription.id,
      customerId: stripeCustomerId,
      trialEnd: trialEnd ? new Date(trialEnd).toISOString() : null,
      daysUntilEnd,
      status: subscription.status,
    });

    // Update subscription record with trial ending flag
    await ctx.db.patch(existingSubscription._id, {
      trialEndingNotificationSent: true,
      trialEndingNotificationSentAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Log trial ending event for analytics
    await logSubscriptionEvent(ctx, existingSubscription._id, 'trial_ending', {
      daysUntilEnd,
      trialEnd,
      notificationSentAt: Date.now(),
    });

    // TODO: Send Norwegian-localized email notification
    // This would be implemented with email service integration
    console.log(`📧 Trial ending notification should be sent to user (not implemented yet)`);

    console.log(`✅ Trial ending notification processed successfully: ${subscription.id}`);
  } catch (error) {
    console.error(`❌ Error processing trial ending notification:`, error);
    throw error;
  }
}

/**
 * Handle invoice.paid events
 * Called when an invoice payment succeeds
 */
async function handleInvoicePaid(ctx: any, event: Stripe.Event): Promise<void> {
  const invoice = event.data.object as Stripe.Invoice;
  console.log(`💰 Invoice paid: ${invoice.id}`);

  try {
    // Validate invoice data
    if (!invoice.customer) {
      throw new Error("No customer ID in paid invoice");
    }

    if (!invoice.subscription) {
      console.log(`ℹ️ Invoice ${invoice.id} is not associated with a subscription (one-time payment)`);
      await handleOneTimePayment(ctx, invoice);
      return;
    }

    const stripeCustomerId = typeof invoice.customer === 'string'
      ? invoice.customer
      : invoice.customer.id;

    const stripeSubscriptionId = typeof invoice.subscription === 'string'
      ? invoice.subscription
      : invoice.subscription.id;

    console.log(`🔄 Processing invoice payment:`, {
      invoiceId: invoice.id,
      customerId: stripeCustomerId,
      subscriptionId: stripeSubscriptionId,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      paymentIntent: invoice.payment_intent,
      status: invoice.status,
      paidAt: invoice.status_transitions?.paid_at
    });

    // Find existing subscription record
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!existingSubscription) {
      console.warn(`⚠️ No subscription found for customer: ${stripeCustomerId}`);
      // This might be a new subscription that will be handled by subscription.created event
      return;
    }

    // Process the successful payment
    await processSuccessfulPayment(ctx, existingSubscription, invoice);

    // Reset any grace period or past due status
    await resetGracePeriod(ctx, existingSubscription, invoice);

    // Update payment history
    await recordPaymentHistory(ctx, existingSubscription, invoice);

    console.log(`✅ Invoice payment processed successfully: ${invoice.id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing invoice payment:`, {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      subscriptionId: invoice.subscription,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error; // Re-throw to trigger webhook retry
  }
}

/**
 * Handle invoice.payment_failed events
 * Called when an invoice payment fails
 */
async function handleInvoicePaymentFailed(ctx: any, event: Stripe.Event): Promise<void> {
  const invoice = event.data.object as Stripe.Invoice;
  console.log(`❌ Invoice payment failed: ${invoice.id}`);

  try {
    // Validate invoice data
    if (!invoice.customer) {
      throw new Error("No customer ID in failed invoice");
    }

    if (!invoice.subscription) {
      console.log(`ℹ️ Invoice ${invoice.id} is not associated with a subscription (one-time payment failure)`);
      await handleOneTimePaymentFailure(ctx, invoice);
      return;
    }

    const stripeCustomerId = typeof invoice.customer === 'string'
      ? invoice.customer
      : invoice.customer.id;

    const stripeSubscriptionId = typeof invoice.subscription === 'string'
      ? invoice.subscription
      : invoice.subscription.id;

    console.log(`🔄 Processing invoice payment failure:`, {
      invoiceId: invoice.id,
      customerId: stripeCustomerId,
      subscriptionId: stripeSubscriptionId,
      amount: invoice.amount_due,
      currency: invoice.currency,
      attemptCount: invoice.attempt_count,
      nextPaymentAttempt: invoice.next_payment_attempt,
      charge: invoice.charge,
      lastFinalizationError: invoice.last_finalization_error
    });

    // Find existing subscription record
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!existingSubscription) {
      console.warn(`⚠️ No subscription found for customer: ${stripeCustomerId}`);
      return;
    }

    // Process the payment failure
    await processPaymentFailure(ctx, existingSubscription, invoice);

    // Start payment retry process first (will escalate to dunning if retries are exhausted)
    await initiatePaymentRetryProcess(ctx, existingSubscription, invoice);

    // Record payment failure in history
    await recordPaymentFailureHistory(ctx, existingSubscription, invoice);

    // Update subscription status if needed
    await updateSubscriptionStatusOnFailure(ctx, existingSubscription, invoice);

    console.log(`✅ Invoice payment failure processed successfully: ${invoice.id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing invoice payment failure:`, {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      subscriptionId: invoice.subscription,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error; // Re-throw to trigger webhook retry
  }
}

/**
 * Handle payment_intent.succeeded events
 * Called when a payment intent succeeds (for one-time payments)
 */
async function handlePaymentIntentSucceeded(ctx: any, event: Stripe.Event): Promise<void> {
  const paymentIntent = event.data.object as Stripe.PaymentIntent;
  console.log(`💳 Payment intent succeeded: ${paymentIntent.id}`);

  const stripeCustomerId = typeof paymentIntent.customer === 'string'
    ? paymentIntent.customer
    : paymentIntent.customer?.id;

  if (!stripeCustomerId) {
    console.log(`ℹ️ Payment intent ${paymentIntent.id} has no customer - might be a guest payment`);
    return;
  }

  try {
    console.log(`🔄 Processing payment intent success:`, {
      paymentIntentId: paymentIntent.id,
      customerId: stripeCustomerId,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      status: paymentIntent.status,
      paymentMethod: paymentIntent.payment_method,
    });

    // Find existing subscription record
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (existingSubscription) {
      // Update subscription with payment information
      await ctx.db.patch(existingSubscription._id, {
        lastPaymentIntentId: paymentIntent.id,
        lastPaymentAt: Date.now(),
        lastPaymentAmount: paymentIntent.amount,
        lastPaymentCurrency: paymentIntent.currency,
        paymentStatus: 'paid',
        updatedAt: Date.now(),
      });

      // Log payment success for analytics
      await logSubscriptionEvent(ctx, existingSubscription._id, 'payment_succeeded', {
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        paymentMethod: paymentIntent.payment_method,
      });
    }

    // TODO: Send payment confirmation email in Norwegian
    console.log(`📧 Payment confirmation should be sent to customer (not implemented yet)`);

    console.log(`✅ Payment intent success processed: ${paymentIntent.id}`);
  } catch (error) {
    console.error(`❌ Error processing payment intent success:`, error);
    throw error;
  }
}

/**
 * Handle payment_intent.payment_failed events
 * Called when a payment intent fails
 */
async function handlePaymentIntentFailed(ctx: any, event: Stripe.Event): Promise<void> {
  const paymentIntent = event.data.object as Stripe.PaymentIntent;
  console.log(`❌ Payment intent failed: ${paymentIntent.id}`);

  // TODO: Implement payment failure logic in next tasks
  // This handles failed payments and retry logic
  console.log("🔄 Payment intent failure handler not yet implemented");
}

// ===== CHECKOUT SESSION COMPLETION HANDLERS =====

/**
 * Handle subscription checkout completion
 * This is critical for trial-to-paid conversion
 */
async function handleSubscriptionCheckoutCompletion(ctx: any, session: Stripe.Checkout.Session): Promise<void> {
  console.log(`📋 Processing subscription checkout completion:`, {
    sessionId: session.id,
    customerId: session.customer,
    subscriptionId: session.subscription
  });

  try {
    // Get the Stripe customer ID as string
    const stripeCustomerId = typeof session.customer === 'string'
      ? session.customer
      : session.customer?.id;

    if (!stripeCustomerId) {
      throw new Error("Invalid customer ID in checkout session");
    }

    // Find the user by Stripe customer ID
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!subscription) {
      console.warn(`⚠️ No subscription found for Stripe customer: ${stripeCustomerId}`);
      // This might be a new subscription that will be handled by subscription.created event
      return;
    }

    console.log(`📋 Found subscription record: ${subscription._id}`);

    // Update subscription status to indicate checkout completion
    await ctx.db.patch(subscription._id, {
      checkoutSessionId: session.id,
      checkoutCompletedAt: Date.now(),
      paymentStatus: session.payment_status,
      lastUpdated: Date.now(),
    });

    // If this was a trial-to-paid conversion, update trial status
    if (subscription.status === "trialing" && session.payment_status === "paid") {
      console.log(`🎯 Trial-to-paid conversion detected for subscription: ${subscription._id}`);

      await ctx.db.patch(subscription._id, {
        status: "active", // Will be confirmed by subscription.updated webhook
        trialConvertedAt: Date.now(),
      });

      // Log successful trial conversion
      console.log(`✅ Trial-to-paid conversion processed: ${subscription._id}`);
    }

    console.log(`✅ Subscription checkout completion processed successfully`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing subscription checkout completion:`, {
      sessionId: session.id,
      customerId: session.customer,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Handle one-time payment checkout completion
 */
async function handlePaymentCheckoutCompletion(ctx: any, session: Stripe.Checkout.Session): Promise<void> {
  console.log(`💰 Processing one-time payment checkout completion:`, {
    sessionId: session.id,
    customerId: session.customer,
    amount: session.amount_total
  });

  try {
    // For now, just log the payment completion
    // Future implementation could handle one-time payments, credits, etc.
    console.log(`💰 One-time payment completed: ${session.amount_total} ${session.currency}`);

    // TODO: Implement one-time payment logic if needed
    // This could include:
    // - Recording payment in database
    // - Adding credits to user account
    // - Sending payment confirmation emails

    console.log(`✅ One-time payment checkout completion processed successfully`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing payment checkout completion:`, {
      sessionId: session.id,
      customerId: session.customer,
      error: errorMessage
    });
    throw error;
  }
}

// ===== SUBSCRIPTION CREATION HELPERS =====

/**
 * End trial immediately if subscription is still trialing
 * Ensures immediate activation after payment
 */
async function endTrialNowIfNeeded(stripe: Stripe, subscriptionId: string) {
  console.log("🔄 Checking if trial needs to be ended for subscription:", subscriptionId);

  // Fetch fresh subscription
  const sub = await stripe.subscriptions.retrieve(subscriptionId);
  console.log("📋 Current subscription status:", sub.status);

  // If not trialing -> no change
  if (sub.status !== 'trialing') {
    console.log("ℹ️ Subscription not trialing, no changes needed");
    return { changed: false, subscription: sub };
  }

  // Idempotency: skip if already force-ended
  const alreadyForced = (sub.metadata as any)?.trial_forced_end === '1';
  if (alreadyForced) {
    console.log("ℹ️ Trial already force-ended, skipping");
    return { changed: false, subscription: sub };
  }

  console.log("🔄 Force-ending trial now...");

  const updated = await stripe.subscriptions.update(subscriptionId, {
    trial_end: 'now',
    proration_behavior: 'none',
    metadata: { ...(sub.metadata ?? {}), trial_forced_end: '1' },
  });

  console.log("✅ Trial ended successfully, new status:", updated.status);
  return { changed: true, subscription: updated };
}

/**
 * Update existing subscription record with new Stripe subscription data
 */
async function updateExistingSubscription(ctx: any, existingSubscription: any, stripeSubscription: Stripe.Subscription): Promise<void> {
  console.log(`🔄 Updating existing subscription: ${existingSubscription._id}`);

  try {
    // Extract subscription item details
    const subscriptionItem = stripeSubscription.items.data[0];
    const priceId = subscriptionItem.price.id;
    const planType = determinePlanType(priceId);
    const billingInterval = determineBillingInterval(priceId);

    // Prepare update data
    const updateData = {
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
      currentPeriodStart: stripeSubscription.current_period_start * 1000,
      currentPeriodEnd: stripeSubscription.current_period_end * 1000,
      trialEnd: stripeSubscription.trial_end ? stripeSubscription.trial_end * 1000 : undefined,
      cancelAt: stripeSubscription.cancel_at ? stripeSubscription.cancel_at * 1000 : undefined,
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      canceledAt: stripeSubscription.canceled_at ? stripeSubscription.canceled_at * 1000 : undefined,
      stripePriceId: priceId,
      planLevel: planType,
      billingInterval: billingInterval,
      quantity: subscriptionItem.quantity,
      updatedAt: Date.now(),
    };

    // Add trial conversion timestamp if transitioning from trial
    if (existingSubscription.status === "trialing" && stripeSubscription.status === "active") {
      updateData.trialConvertedAt = Date.now();
      console.log(`🎯 Trial-to-active conversion detected for subscription: ${existingSubscription._id}`);
    }

    await ctx.db.patch(existingSubscription._id, updateData);

    console.log(`✅ Subscription record updated successfully:`, {
      subscriptionId: existingSubscription._id,
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
      planType: planType
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error updating existing subscription:`, {
      subscriptionId: existingSubscription._id,
      stripeSubscriptionId: stripeSubscription.id,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Create new subscription record from Stripe subscription data
 */
async function createNewSubscription(ctx: any, stripeSubscription: Stripe.Subscription, stripeCustomerId: string): Promise<void> {
  console.log(`🔄 Creating new subscription record for customer: ${stripeCustomerId}`);

  try {
    // Find the user associated with this Stripe customer through existing subscription
    const existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!existingSubscription) {
      throw new Error(`No existing subscription found for Stripe customer: ${stripeCustomerId}`);
    }

    const user = await ctx.db.get(existingSubscription.userId);

    if (!user) {
      throw new Error(`No user found for Stripe customer: ${stripeCustomerId}`);
    }

    // Extract subscription item details
    const subscriptionItem = stripeSubscription.items.data[0];
    const priceId = subscriptionItem.price.id;
    const planType = determinePlanType(priceId);
    const billingInterval = determineBillingInterval(priceId);

    // Create new subscription record
    const subscriptionData = {
      userId: user.clerkUserId,
      stripeCustomerId: stripeCustomerId,
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
      currentPeriodStart: stripeSubscription.current_period_start * 1000,
      currentPeriodEnd: stripeSubscription.current_period_end * 1000,
      trialEnd: stripeSubscription.trial_end ? stripeSubscription.trial_end * 1000 : undefined,
      cancelAt: stripeSubscription.cancel_at ? stripeSubscription.cancel_at * 1000 : undefined,
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      canceledAt: stripeSubscription.canceled_at ? stripeSubscription.canceled_at * 1000 : undefined,
      stripePriceId: priceId,
      planLevel: planType,
      billingInterval: billingInterval,
      quantity: subscriptionItem.quantity,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const newSubscriptionId = await ctx.db.insert("subscriptions", subscriptionData);

    console.log(`✅ New subscription record created successfully:`, {
      subscriptionId: newSubscriptionId,
      stripeSubscriptionId: stripeSubscription.id,
      userId: user._id,
      status: stripeSubscription.status,
      planType: planType
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error creating new subscription:`, {
      stripeCustomerId: stripeCustomerId,
      stripeSubscriptionId: stripeSubscription.id,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Determine plan type from Stripe price ID
 */
function determinePlanType(priceId: string): string {
  // Import the Stripe configuration to map price IDs to plan types
  const { STRIPE_CONFIG } = require("./config");

  // Check each plan type for matching price ID
  for (const [planType, prices] of Object.entries(STRIPE_CONFIG.products)) {
    if (typeof prices === 'object' && prices !== null) {
      const priceValues = Object.values(prices);
      if (priceValues.includes(priceId)) {
        return planType;
      }
    }
  }

  // Fallback: try to determine from price ID pattern
  if (priceId.includes('basic') || priceId.includes('Basic')) {
    return 'basic';
  } else if (priceId.includes('professional') || priceId.includes('Professional')) {
    return 'professional';
  } else if (priceId.includes('enterprise') || priceId.includes('Enterprise')) {
    return 'enterprise';
  }

  console.warn(`⚠️ Unknown price ID: ${priceId}, defaulting to 'basic'`);
  return 'basic';
}

/**
 * Determine billing interval from Stripe price ID
 */
function determineBillingInterval(priceId: string): "month" | "year" {
  // Import the Stripe configuration to map price IDs to billing intervals
  const { STRIPE_CONFIG } = require("./config");

  // Check each plan type for matching price ID and determine interval
  for (const [planType, prices] of Object.entries(STRIPE_CONFIG.products)) {
    if (typeof prices === 'object' && prices !== null) {
      const pricesObj = prices as Record<string, string>;
      if (pricesObj.monthly === priceId) {
        return 'month';
      }
      if (pricesObj.yearly === priceId) {
        return 'year';
      }
    }
  }

  // Fallback: try to determine from price ID pattern
  if (priceId.includes('year') || priceId.includes('annual')) {
    return 'year';
  }

  // Default to monthly
  console.warn(`⚠️ Unknown billing interval for price ID: ${priceId}, defaulting to 'month'`);
  return 'month';
}

/**
 * Activate user access to paid features based on subscription
 */
async function activateUserAccess(ctx: any, stripeCustomerId: string, stripeSubscription: Stripe.Subscription): Promise<void> {
  console.log(`🔓 Activating user access for customer: ${stripeCustomerId}`);

  try {
    // Find the user associated with this Stripe customer through subscription
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!subscription) {
      throw new Error(`No subscription found for Stripe customer: ${stripeCustomerId}`);
    }

    const user = await ctx.db.get(subscription.userId);

    if (!user) {
      throw new Error(`No user found for Stripe customer: ${stripeCustomerId}`);
    }

    // Determine the plan type and features
    const subscriptionItem = stripeSubscription.items.data[0];
    const priceId = subscriptionItem.price.id;
    const planType = determinePlanType(priceId);

    // Update user record with subscription status
    const userUpdateData = {
      subscriptionStatus: stripeSubscription.status,
      updatedAt: Date.now(),
    };

    // Add trial information if applicable
    if (stripeSubscription.trial_end) {
      userUpdateData.trialEndsAt = stripeSubscription.trial_end * 1000;
    }

    await ctx.db.patch(user._id, userUpdateData);

    console.log(`✅ User access activated successfully:`, {
      userId: user._id,
      stripeCustomerId: stripeCustomerId,
      planType: planType,
      subscriptionStatus: stripeSubscription.status,
      trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000).toISOString() : null
    });

    // TODO: Send welcome email or activation notification
    // This could be implemented in a future task
    console.log(`📧 Welcome email notification queued for user: ${user._id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error activating user access:`, {
      stripeCustomerId: stripeCustomerId,
      stripeSubscriptionId: stripeSubscription.id,
      error: errorMessage
    });
    throw error;
  }
}

// ===== INVOICE PAYMENT FAILURE HELPERS =====

/**
 * Handle one-time payment failure (not associated with a subscription)
 */
async function handleOneTimePaymentFailure(ctx: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`❌ Processing one-time payment failure: ${invoice.id}`);

  try {
    const stripeCustomerId = typeof invoice.customer === 'string'
      ? invoice.customer
      : invoice.customer?.id;

    if (!stripeCustomerId) {
      throw new Error("Invalid customer ID in one-time payment failure");
    }

    // Log the one-time payment failure
    console.log(`❌ One-time payment failed:`, {
      invoiceId: invoice.id,
      customerId: stripeCustomerId,
      amount: invoice.amount_due,
      currency: invoice.currency,
      attemptCount: invoice.attempt_count,
      lastFinalizationError: invoice.last_finalization_error?.message
    });

    // TODO: Implement one-time payment failure logic if needed
    // This could include:
    // - Sending payment failure notification emails
    // - Creating retry payment links
    // - Recording failure in database
    // - Offering alternative payment methods

    console.log(`✅ One-time payment failure processed: ${invoice.id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing one-time payment failure:`, {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Process subscription payment failure
 */
async function processPaymentFailure(ctx: any, subscription: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`🔄 Processing payment failure for subscription: ${subscription._id}`);

  try {
    const currentFailureCount = (subscription.paymentFailureCount || 0) + 1;
    const updateData = {
      paymentFailedAt: Date.now(),
      paymentFailureCount: currentFailureCount,
      lastFailedInvoiceId: invoice.id,
      lastFailedAmount: invoice.amount_due,
      lastFailedCurrency: invoice.currency,
      lastFailureReason: invoice.last_finalization_error?.message || 'Payment failed',
      paymentStatus: 'failed',
      updatedAt: Date.now(),
    };

    // Update subscription with failure information
    await ctx.db.patch(subscription._id, updateData);

    console.log(`✅ Payment failure processed:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      failureCount: currentFailureCount,
      amount: invoice.amount_due,
      currency: invoice.currency,
      reason: invoice.last_finalization_error?.message || 'Payment failed'
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing payment failure:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Initiate payment retry process with automatic escalation to dunning
 */
async function initiatePaymentRetryProcess(ctx: any, subscription: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`🔄 Initiating payment retry process for subscription: ${subscription._id}`);

  try {
    // Extract failure information from invoice
    const failureReason = invoice.last_finalization_error?.message ||
                          invoice.charge?.failure_message ||
                          'Payment failed';

    const declineCode = invoice.charge?.failure_code ||
                       invoice.last_finalization_error?.decline_code;

    const metadata = {
      originalAmount: invoice.amount_due,
      currency: invoice.currency,
      paymentMethodType: invoice.charge?.payment_method_details?.type,
      customerEmail: subscription.customerEmail,
    };

    // Start payment retry process using the new system
    await ctx.runMutation(internal.subscriptions.startPaymentRetryProcess, {
      subscriptionId: subscription._id,
      stripeInvoiceId: invoice.id,
      stripePaymentIntentId: invoice.payment_intent,
      originalFailureReason: failureReason,
      declineCode,
      metadata,
    });

    console.log(`✅ Payment retry process initiated for subscription: ${subscription._id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error initiating payment retry process:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      error: errorMessage
    });

    // Fall back to direct dunning process if retry initiation fails
    console.warn(`⚠️ Falling back to direct dunning process`);
    await initiateDunningProcess(ctx, subscription, invoice);
  }
}

/**
 * Initiate or update dunning process
 */
async function initiateDunningProcess(ctx: any, subscription: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`🔄 Initiating dunning process for subscription: ${subscription._id}`);

  try {
    // Extract failure information from invoice
    const failureReason = invoice.last_finalization_error?.message ||
                          invoice.charge?.failure_message ||
                          'Payment failed';

    const metadata = {
      originalAmount: invoice.amount_due,
      currency: invoice.currency,
      paymentMethod: invoice.default_payment_method || undefined,
      declineCode: invoice.charge?.failure_code || undefined,
    };

    // Start dunning process using the new comprehensive system
    await ctx.runMutation(internal.subscriptions.startDunningProcess, {
      subscriptionId: subscription._id,
      stripeInvoiceId: invoice.id,
      failureReason,
      metadata,
    });

    console.log(`✅ Dunning process initiated for subscription: ${subscription._id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error initiating dunning process:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      error: errorMessage
    });
    // Don't throw error here - payment failure processing should continue
    console.warn(`⚠️ Continuing payment failure processing despite dunning error`);
  }
}

/**
 * Record payment failure in subscription history
 */
async function recordPaymentFailureHistory(ctx: any, subscription: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`📝 Recording payment failure history for subscription: ${subscription._id}`);

  try {
    // Create payment failure history record
    const failureRecord = {
      subscriptionId: subscription._id,
      userId: subscription.userId,
      stripeInvoiceId: invoice.id,
      stripePaymentIntentId: invoice.payment_intent,
      amount: invoice.amount_due,
      currency: invoice.currency,
      status: 'failed',
      failedAt: Date.now(),
      attemptCount: invoice.attempt_count,
      failureReason: invoice.last_finalization_error?.message || 'Payment failed',
      nextPaymentAttempt: invoice.next_payment_attempt ? invoice.next_payment_attempt * 1000 : undefined,
      periodStart: invoice.period_start ? invoice.period_start * 1000 : undefined,
      periodEnd: invoice.period_end ? invoice.period_end * 1000 : undefined,
      description: invoice.description || `Failed payment for ${subscription.planLevel} plan`,
      createdAt: Date.now(),
    };

    // Insert payment failure history record
    const failureHistoryId = await ctx.db.insert("subscriptionHistory", failureRecord);

    console.log(`✅ Payment failure history recorded:`, {
      failureHistoryId,
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      amount: invoice.amount_due,
      currency: invoice.currency,
      attemptCount: invoice.attempt_count
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error recording payment failure history:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      error: errorMessage
    });
    // Don't throw error here - payment failure processing should continue
    console.warn(`⚠️ Payment failure processed but history recording failed`);
  }
}

/**
 * Update subscription status based on payment failure
 */
async function updateSubscriptionStatusOnFailure(ctx: any, subscription: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`🔄 Updating subscription status after payment failure: ${subscription._id}`);

  try {
    const currentDunningAttempts = subscription.dunningAttempts || 0;
    const maxDunningAttempts = 3;

    let newStatus = subscription.status;
    const updateData = {
      updatedAt: Date.now(),
    };

    // Determine new subscription status based on failure count and current status
    if (currentDunningAttempts >= maxDunningAttempts) {
      // After max attempts, move to unpaid status
      newStatus = 'unpaid';
      updateData.status = newStatus;
      updateData.unpaidAt = Date.now();
      console.log(`❌ Subscription moved to unpaid status after ${currentDunningAttempts} failed attempts`);
    } else if (subscription.status === 'active') {
      // First failure, move to past_due status
      newStatus = 'past_due';
      updateData.status = newStatus;
      updateData.pastDueAt = Date.now();
      console.log(`⚠️ Subscription moved to past_due status`);
    }

    // Only update if status changed
    if (newStatus !== subscription.status) {
      await ctx.db.patch(subscription._id, updateData);

      console.log(`✅ Subscription status updated:`, {
        subscriptionId: subscription._id,
        previousStatus: subscription.status,
        newStatus: newStatus,
        dunningAttempts: currentDunningAttempts,
        invoiceId: invoice.id
      });
    } else {
      console.log(`ℹ️ Subscription status unchanged: ${subscription.status}`);
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error updating subscription status:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      error: errorMessage
    });
    throw error;
  }
}

// ===== INVOICE PAYMENT HELPERS =====

/**
 * Handle one-time payment (not associated with a subscription)
 */
async function handleOneTimePayment(ctx: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`💰 Processing one-time payment: ${invoice.id}`);

  try {
    const stripeCustomerId = typeof invoice.customer === 'string'
      ? invoice.customer
      : invoice.customer?.id;

    if (!stripeCustomerId) {
      throw new Error("Invalid customer ID in one-time payment invoice");
    }

    // For now, just log the one-time payment
    // Future implementation could handle credits, one-time features, etc.
    console.log(`💰 One-time payment completed:`, {
      invoiceId: invoice.id,
      customerId: stripeCustomerId,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      description: invoice.description
    });

    // TODO: Implement one-time payment logic if needed
    // This could include:
    // - Adding credits to user account
    // - Unlocking one-time features
    // - Recording payment in database
    // - Sending payment confirmation emails

    console.log(`✅ One-time payment processed successfully: ${invoice.id}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing one-time payment:`, {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Process successful subscription payment
 */
async function processSuccessfulPayment(ctx: any, subscription: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`🔄 Processing successful payment for subscription: ${subscription._id}`);

  try {
    const updateData = {
      lastPaymentAt: Date.now(),
      lastInvoiceId: invoice.id,
      lastPaymentAmount: invoice.amount_paid,
      lastPaymentCurrency: invoice.currency,
      paymentStatus: 'paid',
      updatedAt: Date.now(),
    };

    // If subscription was in past_due or unpaid status, reactivate it
    if (subscription.status === 'past_due' || subscription.status === 'unpaid') {
      console.log(`🔄 Reactivating subscription from ${subscription.status} status`);
      updateData.status = 'active';
      updateData.reactivatedAt = Date.now();
    }

    // Clear any payment failure flags
    updateData.paymentFailedAt = undefined;
    updateData.paymentFailureCount = 0;

    await ctx.db.patch(subscription._id, updateData);

    // Resolve any active dunning processes
    try {
      await ctx.runMutation(internal.subscriptions.resolveDunningProcess, {
        subscriptionId: subscription._id,
        resolvedBy: 'payment_success',
      });
    } catch (dunningError) {
      console.error(`❌ Error resolving dunning process:`, dunningError);
      // Don't throw - payment processing should continue
    }

    console.log(`✅ Subscription payment processed successfully:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      wasReactivated: subscription.status === 'past_due' || subscription.status === 'unpaid'
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing successful payment:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Reset grace period and past due status after successful payment
 */
async function resetGracePeriod(ctx: any, subscription: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`🔄 Resetting grace period for subscription: ${subscription._id}`);

  try {
    const updateData = {
      gracePeriodEndsAt: undefined,
      isInGracePeriod: false,
      dunningAttempts: 0,
      lastDunningAt: undefined,
      updatedAt: Date.now(),
    };

    // If there was a grace period or dunning in progress, clear it
    if (subscription.gracePeriodEndsAt || subscription.isInGracePeriod || subscription.dunningAttempts > 0) {
      console.log(`🔄 Clearing grace period and dunning status`);
      await ctx.db.patch(subscription._id, updateData);

      console.log(`✅ Grace period reset successfully:`, {
        subscriptionId: subscription._id,
        invoiceId: invoice.id,
        hadGracePeriod: !!subscription.gracePeriodEndsAt,
        dunningAttempts: subscription.dunningAttempts || 0
      });
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error resetting grace period:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Record payment in subscription history
 */
async function recordPaymentHistory(ctx: any, subscription: any, invoice: Stripe.Invoice): Promise<void> {
  console.log(`📝 Recording payment history for subscription: ${subscription._id}`);

  try {
    // Create payment history record
    const paymentRecord = {
      subscriptionId: subscription._id,
      userId: subscription.userId,
      stripeInvoiceId: invoice.id,
      stripePaymentIntentId: invoice.payment_intent,
      amount: invoice.amount_paid,
      currency: invoice.currency,
      status: 'paid',
      paidAt: invoice.status_transitions?.paid_at ? invoice.status_transitions.paid_at * 1000 : Date.now(),
      periodStart: invoice.period_start ? invoice.period_start * 1000 : undefined,
      periodEnd: invoice.period_end ? invoice.period_end * 1000 : undefined,
      description: invoice.description || `Payment for ${subscription.planLevel} plan`,
      createdAt: Date.now(),
    };

    // Insert payment history record
    const paymentHistoryId = await ctx.db.insert("subscriptionHistory", paymentRecord);

    console.log(`✅ Payment history recorded successfully:`, {
      paymentHistoryId,
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      amount: invoice.amount_paid,
      currency: invoice.currency
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error recording payment history:`, {
      subscriptionId: subscription._id,
      invoiceId: invoice.id,
      error: errorMessage
    });
    // Don't throw error here - payment processing should continue even if history recording fails
    console.warn(`⚠️ Payment processed successfully but history recording failed`);
  }
}

// ===== SUBSCRIPTION UPDATE HELPERS =====

/**
 * Process subscription update with comprehensive change detection
 */
async function processSubscriptionUpdate(
  ctx: any,
  existingSubscription: any,
  stripeSubscription: Stripe.Subscription,
  previousAttributes: Partial<Stripe.Subscription>
): Promise<void> {
  console.log(`🔄 Processing subscription update for: ${existingSubscription._id}`);

  try {
    // Extract current subscription item details
    const subscriptionItem = stripeSubscription.items.data[0];
    const priceId = subscriptionItem.price.id;
    const planType = determinePlanType(priceId);
    const billingInterval = determineBillingInterval(priceId);

    // Detect what changed
    const changes = detectSubscriptionChanges(existingSubscription, stripeSubscription, previousAttributes);

    console.log(`📊 Detected changes:`, changes);

    // Prepare update data
    const updateData = {
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
      currentPeriodStart: stripeSubscription.current_period_start * 1000,
      currentPeriodEnd: stripeSubscription.current_period_end * 1000,
      trialEnd: stripeSubscription.trial_end ? stripeSubscription.trial_end * 1000 : undefined,
      cancelAt: stripeSubscription.cancel_at ? stripeSubscription.cancel_at * 1000 : undefined,
      cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      canceledAt: stripeSubscription.canceled_at ? stripeSubscription.canceled_at * 1000 : undefined,
      stripePriceId: priceId,
      planLevel: planType,
      billingInterval: billingInterval,
      quantity: subscriptionItem.quantity,
      updatedAt: Date.now(),
    };

    // Handle specific change types
    if (changes.planChanged) {
      console.log(`📈 Plan change detected: ${changes.previousPlan} → ${planType}`);
      updateData.planChangedAt = Date.now();
      updateData.previousPlanLevel = changes.previousPlan;
    }

    if (changes.statusChanged) {
      console.log(`🔄 Status change detected: ${changes.previousStatus} → ${stripeSubscription.status}`);

      // Handle trial-to-active conversion
      if (changes.previousStatus === "trialing" && stripeSubscription.status === "active") {
        updateData.trialConvertedAt = Date.now();
        console.log(`🎯 Trial-to-active conversion detected`);
      }

      // Handle cancellation
      if (stripeSubscription.status === "canceled") {
        updateData.canceledAt = Date.now();
        console.log(`❌ Subscription cancelled`);
      }

      // Handle reactivation
      if (changes.previousStatus === "canceled" && stripeSubscription.status === "active") {
        updateData.reactivatedAt = Date.now();
        console.log(`🔄 Subscription reactivated`);
      }
    }

    if (changes.billingIntervalChanged) {
      console.log(`📅 Billing interval changed: ${changes.previousBillingInterval} → ${billingInterval}`);
    }

    // Update the subscription record
    await ctx.db.patch(existingSubscription._id, updateData);

    console.log(`✅ Subscription record updated successfully:`, {
      subscriptionId: existingSubscription._id,
      stripeSubscriptionId: stripeSubscription.id,
      status: stripeSubscription.status,
      planLevel: planType,
      changes: Object.keys(changes).filter(key => changes[key] === true)
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error processing subscription update:`, {
      subscriptionId: existingSubscription._id,
      stripeSubscriptionId: stripeSubscription.id,
      error: errorMessage
    });
    throw error;
  }
}

/**
 * Detect what changed in a subscription update
 */
function detectSubscriptionChanges(
  existingSubscription: any,
  stripeSubscription: Stripe.Subscription,
  previousAttributes: Partial<Stripe.Subscription>
): any {
  const subscriptionItem = stripeSubscription.items.data[0];
  const currentPriceId = subscriptionItem.price.id;
  const currentPlanType = determinePlanType(currentPriceId);
  const currentBillingInterval = determineBillingInterval(currentPriceId);

  const changes = {
    statusChanged: false,
    planChanged: false,
    billingIntervalChanged: false,
    quantityChanged: false,
    trialChanged: false,
    previousStatus: existingSubscription.status,
    previousPlan: existingSubscription.planLevel,
    previousBillingInterval: existingSubscription.billingInterval,
    previousQuantity: existingSubscription.quantity,
  };

  // Check status change
  if (previousAttributes?.status && previousAttributes.status !== stripeSubscription.status) {
    changes.statusChanged = true;
    changes.previousStatus = previousAttributes.status;
  }

  // Check plan change (price ID change)
  if (existingSubscription.stripePriceId !== currentPriceId) {
    changes.planChanged = true;
    // previousPlan is already set from existing subscription
  }

  // Check billing interval change
  if (existingSubscription.billingInterval !== currentBillingInterval) {
    changes.billingIntervalChanged = true;
    // previousBillingInterval is already set from existing subscription
  }

  // Check quantity change
  if (existingSubscription.quantity !== subscriptionItem.quantity) {
    changes.quantityChanged = true;
    changes.previousQuantity = existingSubscription.quantity;
  }

  // Check trial changes
  if (previousAttributes?.trial_end !== undefined) {
    changes.trialChanged = true;
  }

  return changes;
}

/**
 * Check if subscription items have changed
 */
function hasItemChanges(stripeSubscription: Stripe.Subscription, previousAttributes: Partial<Stripe.Subscription>): boolean {
  // Check if items array changed
  if (previousAttributes?.items) {
    return true;
  }

  // Check if any item properties changed that would affect access
  const currentItem = stripeSubscription.items.data[0];

  // This is a simplified check - in a real implementation you might want to
  // compare the actual previous items data if available
  return false;
}

/**
 * Update user access based on subscription changes
 */
async function updateUserAccess(
  ctx: any,
  stripeCustomerId: string,
  stripeSubscription: Stripe.Subscription,
  previousAttributes: Partial<Stripe.Subscription>
): Promise<void> {
  console.log(`🔄 Updating user access for customer: ${stripeCustomerId}`);

  try {
    // Find the user associated with this Stripe customer through subscription
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!subscription) {
      throw new Error(`No subscription found for Stripe customer: ${stripeCustomerId}`);
    }

    const user = await ctx.db.get(subscription.userId);

    if (!user) {
      throw new Error(`No user found for subscription: ${subscription._id}`);
    }

    // Determine the current plan type and features
    const subscriptionItem = stripeSubscription.items.data[0];
    const priceId = subscriptionItem.price.id;
    const planType = determinePlanType(priceId);

    // Prepare user update data
    const userUpdateData = {
      subscriptionStatus: stripeSubscription.status,
      updatedAt: Date.now(),
    };

    // Handle trial information
    if (stripeSubscription.trial_end) {
      userUpdateData.trialEndsAt = stripeSubscription.trial_end * 1000;
    } else if (previousAttributes?.trial_end) {
      // Trial ended, remove trial end date
      userUpdateData.trialEndsAt = undefined;
    }

    // Handle status-specific updates
    if (stripeSubscription.status === "canceled") {
      console.log(`❌ Subscription cancelled - revoking user access`);
      userUpdateData.subscriptionCancelledAt = Date.now();
    } else if (previousAttributes?.status === "canceled" && stripeSubscription.status === "active") {
      console.log(`🔄 Subscription reactivated - restoring user access`);
      userUpdateData.subscriptionCancelledAt = undefined;
      userUpdateData.subscriptionReactivatedAt = Date.now();
    }

    // Handle trial-to-paid conversion
    if (previousAttributes?.status === "trialing" && stripeSubscription.status === "active") {
      console.log(`🎯 Trial-to-paid conversion - activating full access`);
      userUpdateData.trialConvertedAt = Date.now();
    }

    // Update user record
    await ctx.db.patch(user._id, userUpdateData);

    console.log(`✅ User access updated successfully:`, {
      userId: user._id,
      stripeCustomerId: stripeCustomerId,
      planType: planType,
      subscriptionStatus: stripeSubscription.status,
      previousStatus: previousAttributes?.status,
      trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000).toISOString() : null
    });

    // TODO: Send notification emails for significant changes
    // This could include plan upgrade/downgrade notifications, cancellation confirmations, etc.
    if (previousAttributes?.status !== stripeSubscription.status) {
      console.log(`📧 Status change notification queued for user: ${user._id}`);
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error updating user access:`, {
      stripeCustomerId: stripeCustomerId,
      stripeSubscriptionId: stripeSubscription.id,
      error: errorMessage
    });
    throw error;
  }
}

// ===== ADDITIONAL UTILITY FUNCTIONS =====

/**
 * Revoke user access to paid features when subscription is cancelled
 */
async function revokeUserAccess(ctx: any, stripeCustomerId: string, subscription: Stripe.Subscription): Promise<void> {
  console.log(`🔄 Revoking user access for customer: ${stripeCustomerId}`);

  try {
    // Find user by Stripe customer ID
    const user = await ctx.db
      .query("users")
      .withIndex("by_stripe_customer", (q: any) => q.eq("stripeCustomerId", stripeCustomerId))
      .first();

    if (!user) {
      console.warn(`⚠️ No user found for Stripe customer: ${stripeCustomerId}`);
      return;
    }

    // Update user access flags
    await ctx.db.patch(user._id, {
      hasActiveSubscription: false,
      subscriptionStatus: 'canceled',
      accessRevokedAt: Date.now(),
      updatedAt: Date.now(),
    });

    console.log(`✅ User access revoked for: ${user.clerkUserId}`);
  } catch (error) {
    console.error(`❌ Error revoking user access:`, error);
    throw error;
  }
}

/**
 * Log subscription events for analytics and debugging
 */
async function logSubscriptionEvent(
  ctx: any,
  subscriptionId: string,
  eventType: string,
  eventData: any,
  source: 'stripe_webhook' | 'user_action' | 'system_action' | 'admin_action' = 'stripe_webhook',
  metadata?: any
): Promise<void> {
  try {
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId,
      eventType,
      eventData,
      timestamp: Date.now(),
      source,
      metadata,
    });

    console.log(`📊 Logged subscription event: ${eventType} for ${subscriptionId}`);
  } catch (error) {
    console.error(`❌ Error logging subscription event:`, error);
    // Don't throw - logging failures shouldn't break webhook processing
  }
}

/**
 * Enhanced webhook processing with comprehensive error handling and retry logic
 */
async function processWebhookWithRetry(
  ctx: any,
  event: Stripe.Event,
  maxRetries: number = 3
): Promise<void> {
  let attempt = 0;
  let lastError: Error | null = null;

  while (attempt < maxRetries) {
    try {
      await processWebhookEvent(ctx, event);

      // Success - log and return
      if (attempt > 0) {
        console.log(`✅ Webhook processing succeeded on attempt ${attempt + 1}/${maxRetries}`);
      }
      return;

    } catch (error) {
      attempt++;
      lastError = error instanceof Error ? error : new Error(String(error));

      console.error(`❌ Webhook processing attempt ${attempt}/${maxRetries} failed:`, {
        eventId: event.id,
        eventType: event.type,
        attempt,
        error: lastError.message,
      });

      // If this was the last attempt, throw the error
      if (attempt >= maxRetries) {
        console.error(`❌ All webhook processing attempts failed for event ${event.id}`);
        throw lastError;
      }

      // Wait before retry (exponential backoff)
      const delayMs = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
      console.log(`⏳ Retrying webhook processing in ${delayMs}ms...`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }
}

/**
 * Validate webhook event data for security and completeness
 */
function validateWebhookEvent(event: Stripe.Event): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Basic event validation
  if (!event.id) {
    errors.push('Missing event ID');
  }

  if (!event.type) {
    errors.push('Missing event type');
  }

  if (!event.data || !event.data.object) {
    errors.push('Missing event data object');
  }

  // Event-specific validation
  switch (event.type) {
    case 'checkout.session.completed':
      const session = event.data.object as Stripe.Checkout.Session;
      if (!session.customer) {
        errors.push('Checkout session missing customer ID');
      }
      if (session.mode === 'subscription' && !session.subscription) {
        errors.push('Subscription checkout session missing subscription ID');
      }
      break;

    case 'customer.subscription.created':
    case 'customer.subscription.updated':
    case 'customer.subscription.deleted':
      const subscription = event.data.object as Stripe.Subscription;
      if (!subscription.customer) {
        errors.push('Subscription missing customer ID');
      }
      if (!subscription.items?.data?.length) {
        errors.push('Subscription missing items');
      }
      break;

    case 'invoice.paid':
    case 'invoice.payment_failed':
      const invoice = event.data.object as Stripe.Invoice;
      if (!invoice.customer) {
        errors.push('Invoice missing customer ID');
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
