import { v } from "convex/values";
import { internal } from "./_generated/api";
import { internalAction, internalMutation, internalQuery, mutation, query } from "./_generated/server";

/**
 * Initiate subscription cancellation
 */
export const initiateSubscriptionCancellation = mutation({
  args: {
    userId: v.string(),
    feedback: v.object({
      reason: v.string(),
      details: v.optional(v.string()),
      wouldRecommend: v.boolean(),
      improvementSuggestions: v.optional(v.string()),
    }),
    cancelImmediately: v.boolean(),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("Abonnement ikke funnet");
    }

    if (!subscription.stripeSubscriptionId) {
      throw new Error("Stripe abonnement-ID mangler");
    }

    if (subscription.status === "canceled") {
      throw new Error("Abonnementet er allerede avbrutt");
    }

    // Create cancellation request record
    const cancellationId = await ctx.db.insert("subscriptionCancellations", {
      userId: args.userId,
      subscriptionId: subscription._id,
      stripeSubscriptionId: subscription.stripeSubscriptionId,
      reason: args.feedback.reason,
      reasonDetails: args.feedback.details,
      wouldRecommend: args.feedback.wouldRecommend,
      improvementSuggestions: args.feedback.improvementSuggestions,
      cancelImmediately: args.cancelImmediately,
      status: "pending",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Schedule the actual cancellation processing
    await ctx.scheduler.runAfter(0, internal.subscriptionCancellation.processCancellation, {
      cancellationRequestId: cancellationId,
    });

    return {
      cancellationRequestId: cancellationId,
      status: "initiated",
      message: args.cancelImmediately 
        ? "Avbryting er startet og vil tre i kraft umiddelbart"
        : "Avbryting er startet og vil tre i kraft ved slutten av faktureringsperioden",
    };
  },
});

/**
 * Process subscription cancellation with Stripe
 */
export const processCancellation = internalAction({
  args: {
    cancellationRequestId: v.id("subscriptionCancellations"),
  },
  handler: async (ctx, args) => {
    const cancellationRequest = await ctx.runQuery(internal.subscriptionCancellation.getCancellationRequest, {
      cancellationRequestId: args.cancellationRequestId,
    });

    if (!cancellationRequest) {
      throw new Error("Cancellation request not found");
    }

    if (cancellationRequest.status !== "pending") {
      throw new Error("Cancellation request is not pending");
    }

    try {
      // Update status to processing
      await ctx.runMutation(internal.subscriptionCancellation.updateCancellationStatus, {
        cancellationRequestId: args.cancellationRequestId,
        status: "processing",
      });

      // Cancel Stripe subscription
      const stripeResult = await ctx.runAction(internal.subscriptionCancellation.cancelStripeSubscription, {
        stripeSubscriptionId: cancellationRequest.stripeSubscriptionId,
        cancelImmediately: cancellationRequest.cancelImmediately,
      });

      if (!stripeResult.success) {
        throw new Error(`Stripe cancellation failed: ${stripeResult.error}`);
      }

      // Update local subscription record
      await ctx.runMutation(internal.subscriptionCancellation.updateSubscriptionStatus, {
        subscriptionId: cancellationRequest.subscriptionId,
        cancelImmediately: cancellationRequest.cancelImmediately,
        stripeData: stripeResult.subscription,
      });

      // Update cancellation request status
      const updateArgs: any = {
        cancellationRequestId: args.cancellationRequestId,
        status: "completed",
        completedAt: Date.now(),
      };

      // Only include stripeInvoiceId if it's not null
      if (stripeResult.invoiceId) {
        updateArgs.stripeInvoiceId = stripeResult.invoiceId;
      }

      await ctx.runMutation(internal.subscriptionCancellation.updateCancellationStatus, updateArgs);

      // Send confirmation email
      await ctx.runAction(internal.subscriptionCancellation.sendCancellationConfirmationEmail, {
        userId: cancellationRequest.userId,
        cancellationRequestId: args.cancellationRequestId,
      });

      // Log cancellation feedback for analysis
      await ctx.runMutation(internal.subscriptionCancellation.logCancellationFeedback, {
        cancellationRequestId: args.cancellationRequestId,
      });

      console.log(`✅ Subscription cancellation completed for user ${cancellationRequest.userId}`);

      return {
        success: true,
        cancellationRequestId: args.cancellationRequestId,
        canceledImmediately: cancellationRequest.cancelImmediately,
      };

    } catch (error) {
      console.error("❌ Subscription cancellation failed:", error);

      // Update status to failed
      await ctx.runMutation(internal.subscriptionCancellation.updateCancellationStatus, {
        cancellationRequestId: args.cancellationRequestId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        completedAt: Date.now(),
      });

      throw error;
    }
  },
});

/**
 * Cancel Stripe subscription
 */
export const cancelStripeSubscription = internalAction({
  args: {
    stripeSubscriptionId: v.string(),
    cancelImmediately: v.boolean(),
  },
  handler: async (ctx, args) => {
    const stripe = new (await import('stripe')).default(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: "2025-08-27.basil",
    });

    try {
      let canceledSubscription;
      let invoiceId = null;

      if (args.cancelImmediately) {
        // Cancel immediately and create credit for unused time
        canceledSubscription = await stripe.subscriptions.cancel(args.stripeSubscriptionId, {
          prorate: true, // Create credit for unused time
        });

        // Get the credit invoice if created
        const invoices = await stripe.invoices.list({
          subscription: args.stripeSubscriptionId,
          limit: 1,
        });
        
        if (invoices.data[0] && invoices.data[0].amount_due < 0) {
          invoiceId = invoices.data[0].id;
        }
      } else {
        // Cancel at period end
        canceledSubscription = await stripe.subscriptions.update(args.stripeSubscriptionId, {
          cancel_at_period_end: true,
        });
      }

      return {
        success: true,
        subscription: canceledSubscription,
        invoiceId,
      };

    } catch (error) {
      console.error("Stripe subscription cancellation failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown Stripe error",
      };
    }
  },
});

/**
 * Send cancellation confirmation email
 */
export const sendCancellationConfirmationEmail = internalAction({
  args: {
    userId: v.string(),
    cancellationRequestId: v.id("subscriptionCancellations"),
  },
  handler: async (ctx, args) => {
    const cancellationRequest = await ctx.runQuery(internal.subscriptionCancellation.getCancellationRequest, {
      cancellationRequestId: args.cancellationRequestId,
    });

    if (!cancellationRequest) {
      throw new Error("Cancellation request not found");
    }

    // Get user details for email
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();
      
    if (!user) {
      throw new Error("User not found");
    }

    const subscription = await ctx.db.get(cancellationRequest.subscriptionId);
    if (!subscription) {
      throw new Error("Subscription not found");
    }

    const effectiveDate = cancellationRequest.cancelImmediately 
      ? new Date()
      : new Date(subscription.currentPeriodEnd || Date.now());

    const emailContent = {
      subject: `Bekreftelse på avbryting av abonnement - JobbLogg`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563EB;">Abonnement avbrutt</h2>
          
          <p>Hei ${user.name || user.email},</p>
          
          <p>Vi bekrefter at ditt JobbLogg-abonnement har blitt avbrutt som forespurt.</p>
          
          <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Avbrytingsdetaljer</h3>
            <p><strong>Plan:</strong> ${subscription.planLevel}</p>
            <p><strong>Avbrutt:</strong> ${new Date().toLocaleDateString('nb-NO')}</p>
            <p><strong>Tilgang til:</strong> ${effectiveDate.toLocaleDateString('nb-NO')}</p>
            ${cancellationRequest.cancelImmediately 
              ? '<p><strong>Type:</strong> Umiddelbar avbryting med refusjon</p>'
              : '<p><strong>Type:</strong> Avbryting ved slutten av faktureringsperioden</p>'
            }
          </div>
          
          <h3>Hva skjer nå?</h3>
          <ul>
            ${cancellationRequest.cancelImmediately 
              ? '<li>Du har mistet tilgang til JobbLogg med umiddelbar virkning</li><li>Du vil motta en kreditt for ubrukt tid på neste faktura</li>'
              : '<li>Du beholder full tilgang til JobbLogg til ' + effectiveDate.toLocaleDateString('nb-NO') + '</li><li>Du vil ikke bli belastet igjen</li>'
            }
            <li>Alle prosjektdata blir bevart i 90 dager</li>
            <li>Du kan reaktivere abonnementet når som helst</li>
          </ul>
          
          <div style="background-color: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h4 style="margin-top: 0; color: #92400e;">Vil du komme tilbake?</h4>
            <p style="margin-bottom: 0; color: #92400e;">
              Vi jobber kontinuerlig med å forbedre JobbLogg basert på tilbakemeldinger fra brukere som deg. 
              Hvis du endrer mening, er du alltid velkommen tilbake!
            </p>
          </div>
          
          <p>Takk for at du har brukt JobbLogg. Vi håper å se deg igjen i fremtiden!</p>
          
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px;">
            JobbLogg - Prosjektdokumentasjon for håndverkere<br>
            <a href="mailto:<EMAIL>"><EMAIL></a>
          </p>
        </div>
      `,
    };

    await ctx.runAction(internal.emails.sendEmail, {
      to: user.email,
      subject: emailContent.subject,
      html: emailContent.html,
      recipientName: user.name || user.email,
    });

    console.log(`✅ Cancellation confirmation email sent to ${user.email}`);
  },
});

// Helper functions
export const getCancellationRequest = internalQuery({
  args: {
    cancellationRequestId: v.id("subscriptionCancellations"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.cancellationRequestId);
  },
});

export const updateCancellationStatus = internalMutation({
  args: {
    cancellationRequestId: v.id("subscriptionCancellations"),
    status: v.string(),
    completedAt: v.optional(v.number()),
    errorMessage: v.optional(v.string()),
    stripeInvoiceId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.completedAt) updates.completedAt = args.completedAt;
    if (args.errorMessage) updates.errorMessage = args.errorMessage;
    // Only set stripeInvoiceId if it's provided and not null/undefined
    if (args.stripeInvoiceId) updates.stripeInvoiceId = args.stripeInvoiceId;

    await ctx.db.patch(args.cancellationRequestId, updates);
  },
});

export const updateSubscriptionStatus = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    cancelImmediately: v.boolean(),
    stripeData: v.any(),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      updatedAt: Date.now(),
    };

    if (args.cancelImmediately) {
      updates.status = "canceled";
      updates.canceledAt = Date.now();
    } else {
      updates.cancelAtPeriodEnd = true;
      updates.status = "active"; // Still active until period end
    }

    // Update with Stripe data
    if (args.stripeData.current_period_end) {
      updates.currentPeriodEnd = args.stripeData.current_period_end * 1000;
    }

    await ctx.db.patch(args.subscriptionId, updates);

    // Log the cancellation event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId: args.subscriptionId,
      eventType: args.cancelImmediately ? 'canceled_immediately' : 'scheduled_for_cancellation',
      eventData: {
        cancelImmediately: args.cancelImmediately,
        stripeSubscriptionId: args.stripeData.id,
        canceledAt: args.cancelImmediately ? Date.now() : null,
        cancelAtPeriodEnd: !args.cancelImmediately,
      },
      timestamp: Date.now(),
      source: 'user_action',
    });
  },
});

export const logCancellationFeedback = internalMutation({
  args: {
    cancellationRequestId: v.id("subscriptionCancellations"),
  },
  handler: async (ctx, args) => {
    const cancellation = await ctx.db.get(args.cancellationRequestId);
    if (!cancellation) return;

    // Log feedback for analysis (could be used for retention improvements)
    await ctx.db.insert("cancellationFeedback", {
      userId: cancellation.userId,
      reason: cancellation.reason,
      reasonDetails: cancellation.reasonDetails,
      wouldRecommend: cancellation.wouldRecommend,
      improvementSuggestions: cancellation.improvementSuggestions,
      cancelImmediately: cancellation.cancelImmediately,
      createdAt: Date.now(),
    });
  },
});

/**
 * Get user's cancellation history
 */
export const getCancellationHistory = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptionCancellations")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(10);
  },
});

/**
 * Check if user can cancel subscription
 */
export const canCancelSubscription = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      return { canCancel: false, reason: "Ingen aktiv abonnement funnet" };
    }

    if (subscription.status === "canceled") {
      return { canCancel: false, reason: "Abonnementet er allerede avbrutt" };
    }

    if (subscription.cancelAtPeriodEnd) {
      return { canCancel: false, reason: "Abonnementet er allerede planlagt for avbryting" };
    }

    // Check for pending cancellation requests
    const pendingCancellation = await ctx.db
      .query("subscriptionCancellations")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.or(q.eq(q.field("status"), "pending"), q.eq(q.field("status"), "processing")))
      .first();

    if (pendingCancellation) {
      return { canCancel: false, reason: "Du har allerede en pågående avbrytingsforespørsel" };
    }

    return { canCancel: true };
  },
});
