/**
 * Subscription Downgrade System
 * 
 * Handles graceful degradation of subscription access when payments fail beyond retry limits.
 * Provides grace periods and progressive feature restrictions to maintain user experience
 * while encouraging payment resolution.
 */

import { v } from "convex/values";
import { mutation, action, internalMutation, internalAction, internalQuery } from "./_generated/server";
import { internal } from "./_generated/api";

// ===== DOWNGRADE CONFIGURATION =====

/**
 * Downgrade stages with progressive restrictions
 */
export const DOWNGRADE_STAGES = {
  // Stage 0: Normal operation (active subscription)
  ACTIVE: {
    stage: 0,
    name: 'active',
    description: 'Full access to all features',
    gracePeriodDays: 0,
    restrictions: {
      canCreateProjects: true,
      canAccessProjects: true,
      canUploadFiles: true,
      canInviteTeamMembers: true,
      canShareProjects: true,
      canExportData: true,
      maxProjects: undefined,
      maxTeamMembers: undefined,
    }
  },

  // Stage 1: Grace period (3 days) - Full access with warnings
  GRACE_PERIOD: {
    stage: 1,
    name: 'grace_period',
    description: 'Full access with payment reminders',
    gracePeriodDays: 3,
    restrictions: {
      canCreateProjects: true,
      canAccessProjects: true,
      canUploadFiles: true,
      canInviteTeamMembers: true,
      canShareProjects: true,
      canExportData: true,
      maxProjects: undefined,
      maxTeamMembers: undefined,
    }
  },

  // Stage 2: Limited access (7 days) - Read-only with basic functionality
  LIMITED_ACCESS: {
    stage: 2,
    name: 'limited_access',
    description: 'Read-only access with limited functionality',
    gracePeriodDays: 7,
    restrictions: {
      canCreateProjects: false,
      canAccessProjects: true,
      canUploadFiles: false,
      canInviteTeamMembers: false,
      canShareProjects: false,
      canExportData: false,
      maxProjects: undefined,
      maxTeamMembers: undefined,
    }
  },

  // Stage 3: Suspended (indefinite) - Minimal access for data recovery
  SUSPENDED: {
    stage: 3,
    name: 'suspended',
    description: 'Account suspended - data recovery only',
    gracePeriodDays: undefined, // Indefinite until payment resolved
    restrictions: {
      canCreateProjects: false,
      canAccessProjects: true, // View-only for data recovery
      canUploadFiles: false,
      canInviteTeamMembers: false,
      canShareProjects: false,
      canExportData: true, // Allow data export for recovery
      maxProjects: undefined,
      maxTeamMembers: undefined,
    }
  }
} as const;

// ===== DOWNGRADE MANAGEMENT =====

/**
 * Initiate subscription downgrade process
 */
export const initiateSubscriptionDowngrade = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    reason: v.string(),
    triggeredBy: v.optional(v.string()), // 'payment_failure', 'dunning_exhausted', etc.
    metadata: v.optional(v.object({
      originalFailureReason: v.optional(v.string()),
      retryAttempts: v.optional(v.number()),
      dunningAttempts: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    console.log(`🔻 Initiating subscription downgrade: ${args.subscriptionId}`);

    try {
      // Get subscription details
      const subscription = await ctx.db.get(args.subscriptionId);
      if (!subscription) {
        throw new Error("Subscription not found");
      }

      // Check if downgrade is already in progress
      const existingDowngrade = await ctx.db
        .query("subscriptionDowngrades")
        .withIndex("by_subscription", (q) => q.eq("subscriptionId", args.subscriptionId))
        .filter((q) => q.neq(q.field("status"), "resolved"))
        .first();

      if (existingDowngrade) {
        console.log(`⚠️ Downgrade already in progress: ${existingDowngrade._id}`);
        return existingDowngrade;
      }

      // Determine initial downgrade stage
      const initialStage = DOWNGRADE_STAGES.GRACE_PERIOD;
      const gracePeriodEnd = Date.now() + (initialStage.gracePeriodDays * 24 * 60 * 60 * 1000);

      // Create downgrade record
      const downgradeId = await ctx.db.insert("subscriptionDowngrades", {
        subscriptionId: args.subscriptionId,
        userId: subscription.userId,
        currentStage: initialStage.stage,
        stageName: initialStage.name,
        reason: args.reason,
        triggeredBy: args.triggeredBy || 'system',
        status: 'active',
        gracePeriodEnd,
        nextEscalationAt: gracePeriodEnd,
        restrictions: initialStage.restrictions,
        metadata: args.metadata || {},
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // Update subscription status
      await ctx.db.patch(args.subscriptionId, {
        status: "past_due",
        downgradeStage: initialStage.stage,
        gracePeriodEnd,
        updatedAt: Date.now(),
      });

      // Log downgrade event
      await ctx.db.insert("subscriptionEvents", {
        subscriptionId: args.subscriptionId,
        eventType: 'downgrade_initiated',
        eventData: {
          downgradeId,
          stage: initialStage.stage,
          stageName: initialStage.name,
          reason: args.reason,
          gracePeriodEnd,
        },
        timestamp: Date.now(),
        source: 'system_action',
      });

      const downgrade = await ctx.db.get(downgradeId);
      console.log(`✅ Subscription downgrade initiated: ${downgradeId}`);
      return downgrade;

    } catch (error) {
      console.error(`❌ Error initiating subscription downgrade:`, error);
      throw error;
    }
  },
});

/**
 * Process downgrade escalation (move to next stage)
 */
export const processDowngradeEscalation = internalMutation({
  args: {
    downgradeId: v.id("subscriptionDowngrades"),
  },
  handler: async (ctx, args) => {
    console.log(`⬇️ Processing downgrade escalation: ${args.downgradeId}`);

    try {
      const downgrade = await ctx.db.get(args.downgradeId);
      if (!downgrade || downgrade.status !== 'active') {
        console.log(`⏭️ Downgrade not active or not found: ${args.downgradeId}`);
        return null;
      }

      // Check if escalation is due
      const now = Date.now();
      if (!downgrade.nextEscalationAt || now < downgrade.nextEscalationAt) {
        console.log(`⏰ Escalation not yet due: ${downgrade.nextEscalationAt ? new Date(downgrade.nextEscalationAt) : 'No escalation scheduled'}`);
        return downgrade;
      }

      // Determine next stage
      const currentStage = downgrade.currentStage;
      let nextStage;

      switch (currentStage) {
        case DOWNGRADE_STAGES.GRACE_PERIOD.stage:
          nextStage = DOWNGRADE_STAGES.LIMITED_ACCESS;
          break;
        case DOWNGRADE_STAGES.LIMITED_ACCESS.stage:
          nextStage = DOWNGRADE_STAGES.SUSPENDED;
          break;
        default:
          console.log(`⚠️ Already at final stage: ${currentStage}`);
          return downgrade;
      }

      // Calculate next escalation time (undefined for suspended stage)
      const nextEscalationAt = nextStage.gracePeriodDays
        ? now + (nextStage.gracePeriodDays * 24 * 60 * 60 * 1000)
        : undefined;

      // Update downgrade record
      await ctx.db.patch(args.downgradeId, {
        currentStage: nextStage.stage,
        stageName: nextStage.name,
        nextEscalationAt,
        restrictions: nextStage.restrictions,
        escalatedAt: now,
        updatedAt: now,
      });

      // Update subscription
      await ctx.db.patch(downgrade.subscriptionId, {
        downgradeStage: nextStage.stage,
        accessSuspendedAt: nextStage.stage === DOWNGRADE_STAGES.SUSPENDED.stage ? now : undefined,
        updatedAt: now,
      });

      // Log escalation event
      await ctx.db.insert("subscriptionEvents", {
        subscriptionId: downgrade.subscriptionId,
        eventType: 'downgrade_escalated',
        eventData: {
          downgradeId: args.downgradeId,
          fromStage: currentStage,
          toStage: nextStage.stage,
          stageName: nextStage.name,
          nextEscalationAt,
        },
        timestamp: now,
        source: 'system_action',
      });

      const updatedDowngrade = await ctx.db.get(args.downgradeId);
      console.log(`✅ Downgrade escalated to stage ${nextStage.stage}: ${nextStage.name}`);
      return updatedDowngrade;

    } catch (error) {
      console.error(`❌ Error processing downgrade escalation:`, error);
      throw error;
    }
  },
});

/**
 * Resolve subscription downgrade (restore full access)
 */
export const resolveSubscriptionDowngrade = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    resolvedBy: v.optional(v.string()), // 'payment_success', 'manual_override', etc.
    metadata: v.optional(v.object({
      paymentIntentId: v.optional(v.string()),
      resolvedAmount: v.optional(v.number()),
    })),
  },
  handler: async (ctx, args) => {
    console.log(`⬆️ Resolving subscription downgrade: ${args.subscriptionId}`);

    try {
      // Find active downgrade
      const downgrade = await ctx.db
        .query("subscriptionDowngrades")
        .withIndex("by_subscription", (q) => q.eq("subscriptionId", args.subscriptionId))
        .filter((q) => q.eq(q.field("status"), "active"))
        .first();

      if (!downgrade) {
        console.log(`⚠️ No active downgrade found for subscription: ${args.subscriptionId}`);
        return null;
      }

      // Update downgrade status
      await ctx.db.patch(downgrade._id, {
        status: 'resolved',
        resolvedBy: args.resolvedBy || 'system',
        resolvedAt: Date.now(),
        metadata: {
          ...downgrade.metadata,
          ...args.metadata,
        },
        updatedAt: Date.now(),
      });

      // Restore subscription to active status
      await ctx.db.patch(args.subscriptionId, {
        status: "active",
        downgradeStage: DOWNGRADE_STAGES.ACTIVE.stage,
        gracePeriodEnd: undefined,
        accessSuspendedAt: undefined,
        reactivatedAt: Date.now(),
        updatedAt: Date.now(),
      });

      // Log resolution event
      await ctx.db.insert("subscriptionEvents", {
        subscriptionId: args.subscriptionId,
        eventType: 'downgrade_resolved',
        eventData: {
          downgradeId: downgrade._id,
          resolvedBy: args.resolvedBy || 'system',
          previousStage: downgrade.currentStage,
          restoredToActive: true,
        },
        timestamp: Date.now(),
        source: 'system_action',
      });

      console.log(`✅ Subscription downgrade resolved: ${downgrade._id}`);
      return await ctx.db.get(downgrade._id);

    } catch (error) {
      console.error(`❌ Error resolving subscription downgrade:`, error);
      throw error;
    }
  },
});

// ===== QUERY FUNCTIONS =====

/**
 * Get current downgrade status for a subscription
 */
export const getSubscriptionDowngradeStatus = internalQuery({
  args: { subscriptionId: v.id("subscriptions") },
  handler: async (ctx, args) => {
    const downgrade = await ctx.db
      .query("subscriptionDowngrades")
      .withIndex("by_subscription", (q) => q.eq("subscriptionId", args.subscriptionId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (!downgrade) {
      return null;
    }

    // Get stage configuration
    const stageConfig = Object.values(DOWNGRADE_STAGES).find(
      stage => stage.stage === downgrade.currentStage
    );

    return {
      ...downgrade,
      stageConfig,
      isInGracePeriod: downgrade.currentStage === DOWNGRADE_STAGES.GRACE_PERIOD.stage,
      isLimitedAccess: downgrade.currentStage === DOWNGRADE_STAGES.LIMITED_ACCESS.stage,
      isSuspended: downgrade.currentStage === DOWNGRADE_STAGES.SUSPENDED.stage,
      daysUntilEscalation: downgrade.nextEscalationAt
        ? Math.ceil((downgrade.nextEscalationAt - Date.now()) / (24 * 60 * 60 * 1000))
        : null,
    };
  },
});

// ===== BATCH PROCESSING =====

/**
 * Process pending downgrade escalations (scheduled function)
 */
export const processPendingDowngradeEscalations = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log('🔄 Processing pending downgrade escalations...');

    try {
      const now = Date.now();

      // Find downgrades that need escalation
      const pendingEscalations = await ctx.runQuery(internal.subscriptionDowngrade.getPendingEscalations, {
        currentTime: now,
      });

      console.log(`📋 Found ${pendingEscalations.length} downgrades pending escalation`);

      let processedCount = 0;
      let errorCount = 0;

      // Process each escalation
      for (const downgrade of pendingEscalations) {
        try {
          await ctx.runMutation(internal.subscriptionDowngrade.processDowngradeEscalation, {
            downgradeId: downgrade._id,
          });
          processedCount++;
          console.log(`✅ Escalated downgrade: ${downgrade._id}`);
        } catch (error) {
          errorCount++;
          console.error(`❌ Error escalating downgrade ${downgrade._id}:`, error);
        }
      }

      console.log(`✅ Downgrade escalation processing complete: ${processedCount} processed, ${errorCount} errors`);

      return {
        totalFound: pendingEscalations.length,
        processed: processedCount,
        errors: errorCount,
      };

    } catch (error) {
      console.error('❌ Error in downgrade escalation processing:', error);
      throw error;
    }
  },
});

/**
 * Get downgrades pending escalation (internal query)
 */
export const getPendingEscalations = internalQuery({
  args: { currentTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptionDowngrades")
      .withIndex("by_next_escalation", (q) => q.lte("nextEscalationAt", args.currentTime))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();
  },
});

/**
 * Get downgrade statistics for monitoring
 */
export const getDowngradeStatistics = internalQuery({
  args: {},
  handler: async (ctx) => {
    const allDowngrades = await ctx.db.query("subscriptionDowngrades").collect();

    const stats = {
      total: allDowngrades.length,
      active: 0,
      resolved: 0,
      cancelled: 0,
      byStage: {
        gracePeriod: 0,
        limitedAccess: 0,
        suspended: 0,
      },
      pendingEscalations: 0,
    };

    const now = Date.now();

    for (const downgrade of allDowngrades) {
      // Count by status
      stats[downgrade.status as keyof typeof stats]++;

      // Count by stage (active only)
      if (downgrade.status === 'active') {
        if (downgrade.currentStage === DOWNGRADE_STAGES.GRACE_PERIOD.stage) {
          stats.byStage.gracePeriod++;
        } else if (downgrade.currentStage === DOWNGRADE_STAGES.LIMITED_ACCESS.stage) {
          stats.byStage.limitedAccess++;
        } else if (downgrade.currentStage === DOWNGRADE_STAGES.SUSPENDED.stage) {
          stats.byStage.suspended++;
        }

        // Count pending escalations
        if (downgrade.nextEscalationAt && downgrade.nextEscalationAt <= now) {
          stats.pendingEscalations++;
        }
      }
    }

    return stats;
  },
});
