import { v } from "convex/values";
import { internalMutation } from "../_generated/server";

/**
 * Normalize plan level from various sources to internal values
 */
function normalizePlanLevel(input?: string): "basic" | "professional" | "enterprise" {
  const map: Record<string, "basic"|"professional"|"enterprise"> = {
    basic: "basic",
    professional: "professional",
    enterprise: "enterprise",
    liten_bedrift: "basic",
    mellomstor_bedrift: "professional",
    stor_bedrift: "enterprise",
  };
  return map[(input || "").toLowerCase()] ?? "basic";
}

/**
 * Idempotent upsert subscription from Stripe webhook data
 * Handles existing trial rows and converts them to active subscriptions
 */
export const upsertFromStripe = internalMutation({
  args: {
    userId: v.optional(v.string()),
    stripeSubscriptionId: v.string(),
    stripeCustomerId: v.string(),
    status: v.string(),
    planLevel: v.string(),
    billingInterval: v.string(),
    currentPeriodStart: v.optional(v.number()),
    currentPeriodEnd: v.optional(v.number()),
    trialStart: v.optional(v.number()),
    trialEnd: v.optional(v.number()),
    trialConvertedAt: v.optional(v.number()),
    seats: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    console.log("🔄 Upserting subscription from Stripe:", {
      userId: args.userId,
      stripeSubscriptionId: args.stripeSubscriptionId,
      stripeCustomerId: args.stripeCustomerId,
      status: args.status
    });


    // Enforce user-customer mapping: never write orphaned records
    if (!args.userId) {
      console.log(`❌ UPSERT_REJECTED: missing userId for stripeSubscriptionId=${args.stripeSubscriptionId} stripeCustomerId=${args.stripeCustomerId}`);
      return { action: "skipped_missing_user" } as const;
    }

      // Ensure user record carries the authoritative Stripe customer mapping
      try {
        const user = await ctx.db
          .query("users")
          .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId!))
          .first();
        if (user && user.stripeCustomerId !== args.stripeCustomerId) {
          await ctx.db.patch(user._id, { stripeCustomerId: args.stripeCustomerId, updatedAt: Date.now() });
        }
      } catch (e) {
        console.log("\u26a0\ufe0f Failed to sync stripeCustomerId to users table:", e);
      }


    // Robust lookup chain: by subscriptionId -> by customerId -> by userId (trial)
    let existingSubscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.stripeSubscriptionId))
      .first();

    if (!existingSubscription) {
      existingSubscription = await ctx.db
        .query("subscriptions")
        .withIndex("by_stripe_customer", (q) => q.eq("stripeCustomerId", args.stripeCustomerId))
        .first();
    }

    if (!existingSubscription && args.userId) {
      existingSubscription = await ctx.db
        .query("subscriptions")
        .withIndex("by_user", (q) => q.eq("userId", args.userId))
        .filter((q) => q.eq(q.field("status"), "trialing"))
        .first();
    }

    // Determine if this is no longer a trial
    const isNoLongerTrial = args.status !== "trialing";
    const trialConvertedAt = args.trialConvertedAt
      ? args.trialConvertedAt
      : (isNoLongerTrial && existingSubscription?.status === "trialing"
        ? Date.now()
        : existingSubscription?.trialConvertedAt);

    // Resolve userId if possible, but don't fail if absent
    let resolvedUserId = args.userId || existingSubscription?.userId;
    // If still missing, try to find any subscription for this customer to inherit userId
    if (!resolvedUserId) {
      const byCust = await ctx.db
        .query("subscriptions")
        .withIndex("by_stripe_customer", (q) => q.eq("stripeCustomerId", args.stripeCustomerId))
        .first();
      if (byCust?.userId) resolvedUserId = byCust.userId;
    }

    // Normalize plan level
    const normalizedPlanLevel = normalizePlanLevel(args.planLevel);

    // Build patch data; only include trialEnd if defined to avoid validator issues
    const baseData = {
      stripeSubscriptionId: args.stripeSubscriptionId,
      stripeCustomerId: args.stripeCustomerId,
      status: args.status as "trialing" | "active" | "past_due" | "canceled" | "incomplete" | "incomplete_expired" | "unpaid" | "paused",
      planLevel: normalizedPlanLevel,
      billingInterval: args.billingInterval as "month" | "year",
      trialConvertedAt,
      updatedAt: Date.now(),
    } as any;

    if (resolvedUserId !== undefined) {
      baseData.userId = resolvedUserId;
    }

    if (args.currentPeriodStart !== undefined) baseData.currentPeriodStart = args.currentPeriodStart;
    if (args.currentPeriodEnd !== undefined) baseData.currentPeriodEnd = args.currentPeriodEnd;
    if (args.trialStart !== undefined) baseData.trialStart = args.trialStart;
    if (args.trialEnd !== undefined) baseData.trialEnd = args.trialEnd;
    if (args.seats !== undefined) baseData.seats = args.seats;

    // Preserve trialStart/trialEnd for historical reference, even after activation.
    // We only update them if provided; otherwise we keep existing values.

    const subscriptionData = baseData;

    if (existingSubscription) {
      // Update existing subscription
      console.log("📝 Updating existing subscription:", existingSubscription._id);

      await ctx.db.patch(existingSubscription._id, subscriptionData);

      console.log("✅ Subscription updated successfully");
      return { action: "updated", subscriptionId: existingSubscription._id };

    } else {
      // Create new subscription
      console.log("📝 Creating new subscription");

      const newSubscriptionData = {
        ...subscriptionData,
        createdAt: Date.now(),
        seats: subscriptionData.seats ?? 1,
      };

      const subscriptionId = await ctx.db.insert("subscriptions", newSubscriptionData);

      console.log("✅ Subscription created successfully");
      return { action: "created", subscriptionId };
    }
  },
});
