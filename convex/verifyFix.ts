import { query } from './_generated/server';
import { v } from 'convex/values';

/**
 * Quick verification that the emergency fix worked
 */
export const verifySubscriptionFix = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // Get subscription
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // Get user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    const now = Date.now();

    return {
      timestamp: new Date().toISOString(),
      userId: args.userId,
      
      subscription: subscription ? {
        _id: subscription._id,
        status: subscription.status,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
        updatedAt: subscription.updatedAt,
        
        // Analysis
        isActive: subscription.status === 'active',
        hasConversionTimestamp: !!subscription.trialConvertedAt,
        trialEndedByTime: subscription.trialEnd ? now >= subscription.trialEnd : false,
      } : null,
      
      user: user ? {
        _id: user._id,
        subscriptionStatus: user.subscriptionStatus,
        trialConvertedAt: user.trialConvertedAt,
        hasCompletedTrial: user.hasCompletedTrial,
        updatedAt: user.updatedAt,
        
        // Analysis
        userStatusActive: user.subscriptionStatus === 'active',
        userHasConversionTimestamp: !!user.trialConvertedAt,
      } : null,
      
      // Overall analysis
      analysis: {
        subscriptionFixed: subscription?.status === 'active',
        userFixed: user?.subscriptionStatus === 'active',
        bothFixed: subscription?.status === 'active' && user?.subscriptionStatus === 'active',
        trialBannerShouldHide: subscription?.status === 'active' && user?.subscriptionStatus === 'active',
        
        // What should happen next
        nextSteps: subscription?.status === 'active' && user?.subscriptionStatus === 'active' 
          ? ['✅ Fix successful!', '✅ Trial banner should disappear', '✅ User has active subscription']
          : ['❌ Fix incomplete', '⚠️ Trial banner will still show', '🔧 Run Emergency Fix again']
      }
    };
  }
});
