import { v } from 'convex/values';
import { action, query } from './_generated/server';

/**
 * Get recent webhook events to debug subscription update issues
 */
export const getRecentWebhookEvents = query({
  args: { 
    limit: v.optional(v.number()),
    eventType: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    
    let query = ctx.db.query("webhookEvents").order("desc");
    
    if (args.eventType) {
      query = query.filter((q) => q.eq(q.field("type"), args.eventType));
    }
    
    const events = await query.take(limit);
    
    return events.map(event => ({
      _id: event._id,
      type: event.type,
      processed: event.processed,
      createdAt: event.createdAt,
      eventId: event.eventId,
      data: event.data ? {
        id: event.data.id,
        object: event.data.object,
        status: (event.data as any).status,
        customer: (event.data as any).customer,
        subscription: (event.data as any).subscription,
      } : null,
      error: event.error,
    }));
  }
});

/**
 * Get subscription update events specifically
 */
export const getSubscriptionUpdateEvents = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const limit = args.limit || 10;
    
    const events = await ctx.db
      .query("webhookEvents")
      .filter((q) => q.eq(q.field("type"), "customer.subscription.updated"))
      .order("desc")
      .take(limit);
    
    return events.map(event => ({
      _id: event._id,
      type: event.type,
      processed: event.processed,
      createdAt: event.createdAt,
      eventId: event.eventId,
      subscriptionId: (event.data as any)?.id,
      customerId: (event.data as any)?.customer,
      status: (event.data as any)?.status,
      previousStatus: (event.data as any)?.previous_attributes?.status,
      error: event.error,
      processingTime: event.processingTime,
    }));
  }
});

/**
 * Check if specific subscription has received webhook updates
 */
export const checkSubscriptionWebhooks = query({
  args: { stripeSubscriptionId: v.string() },
  handler: async (ctx, args) => {
    // Find webhook events for this subscription
    const events = await ctx.db
      .query("webhookEvents")
      .filter((q) => q.eq((q.field("data") as any).id, args.stripeSubscriptionId))
      .order("desc")
      .collect();
    
    // Find the subscription record
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.stripeSubscriptionId))
      .first();
    
    return {
      stripeSubscriptionId: args.stripeSubscriptionId,
      subscription: subscription ? {
        _id: subscription._id,
        status: subscription.status,
        planLevel: subscription.planLevel,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
        updatedAt: subscription.updatedAt,
        createdAt: subscription.createdAt,
      } : null,
      webhookEvents: events.map(event => ({
        _id: event._id,
        type: event.type,
        processed: event.processed,
        createdAt: event.createdAt,
        eventId: event.eventId,
        status: (event.data as any)?.status,
        previousStatus: (event.data as any)?.previous_attributes?.status,
        error: event.error,
      })),
      analysis: {
        totalEvents: events.length,
        processedEvents: events.filter(e => e.processed).length,
        failedEvents: events.filter(e => e.error).length,
        subscriptionUpdates: events.filter(e => e.type === 'customer.subscription.updated').length,
        hasTrialToActiveUpdate: events.some(e => 
          e.type === 'customer.subscription.updated' && 
          (e.data as any)?.previous_attributes?.status === 'trialing' &&
          (e.data as any)?.status === 'active'
        ),
      }
    };
  }
});

/**
 * Action to manually trigger subscription status sync from Stripe
 */
export const forceSyncSubscriptionStatus = action({
  args: { 
    userId: v.string(),
    stripeSubscriptionId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    console.log('🔄 Force syncing subscription status for user:', args.userId);
    
    try {
      // Get current subscription from database using internal query
      const subscription = await ctx.runQuery("subscriptions:getSubscriptionByUserInternal", {
        userId: args.userId
      });
      
      if (!subscription) {
        return {
          success: false,
          error: 'No subscription found for user',
          userId: args.userId,
        };
      }
      
      console.log('📊 Current subscription in database:', {
        _id: subscription._id,
        status: subscription.status,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
      });
      
      // TODO: Here we would fetch from Stripe API and compare
      // For now, let's manually update if we know it should be active
      
      // If trial has ended and no trialConvertedAt, it might need manual update
      const now = Date.now();
      const trialEnded = subscription.trialEnd && now >= subscription.trialEnd;
      
      if (subscription.status === 'trialing' && trialEnded) {
        console.log('🎯 Trial has ended, manually updating to active status');

        // Use the emergency fix action instead
        const result = await ctx.runAction("quickFix:emergencyFixSubscriptionStatus", {
          userId: args.userId,
          reason: "Force sync - trial ended"
        });

        return result;
        
        return {
          success: true,
          action: 'updated_to_active',
          subscription: {
            _id: subscription._id,
            oldStatus: subscription.status,
            newStatus: 'active',
            trialConvertedAt: now,
          },
          timestamp: now,
        };
      }
      
      return {
        success: true,
        action: 'no_update_needed',
        subscription: {
          _id: subscription._id,
          status: subscription.status,
          trialEnd: subscription.trialEnd,
          trialEnded,
        },
        timestamp: now,
      };
      
    } catch (error) {
      console.error('❌ Error force syncing subscription:', error);
      return {
        success: false,
        error: error.message,
        userId: args.userId,
        timestamp: Date.now(),
      };
    }
  }
});
