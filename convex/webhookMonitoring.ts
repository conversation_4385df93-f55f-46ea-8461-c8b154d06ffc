import { query, action } from './_generated/server';
import { v } from 'convex/values';

/**
 * Monitor webhook health and delivery status
 */
export const getWebhookHealth = query({
  handler: async (ctx) => {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const oneDayAgo = now - (24 * 60 * 60 * 1000);

    // Get recent webhook events
    const recentEvents = await ctx.db
      .query("webhookEvents")
      .order("desc")
      .take(50);

    // Filter by time periods
    const lastHour = recentEvents.filter(e => e.createdAt >= oneHourAgo);
    const lastDay = recentEvents.filter(e => e.createdAt >= oneDayAgo);

    // Count by event type
    const eventTypes = {};
    recentEvents.forEach(event => {
      eventTypes[event.type] = (eventTypes[event.type] || 0) + 1;
    });

    // Count processing status
    const processed = recentEvents.filter(e => e.processed).length;
    const failed = recentEvents.filter(e => e.error).length;

    return {
      timestamp: new Date().toISOString(),
      summary: {
        totalEvents: recentEvents.length,
        lastHour: lastHour.length,
        lastDay: lastDay.length,
        processed,
        failed,
        successRate: recentEvents.length > 0 ? ((processed / recentEvents.length) * 100).toFixed(1) : '0',
      },
      eventTypes,
      criticalEvents: {
        subscriptionUpdates: eventTypes['customer.subscription.updated'] || 0,
        checkoutCompleted: eventTypes['checkout.session.completed'] || 0,
        invoicePaid: eventTypes['invoice.paid'] || 0,
      },
      recentFailures: recentEvents
        .filter(e => e.error)
        .slice(0, 5)
        .map(e => ({
          type: e.type,
          error: e.error,
          createdAt: new Date(e.createdAt).toISOString(),
        })),
      isHealthy: lastHour.length > 0 || recentEvents.length === 0, // Healthy if recent activity or no events yet
      recommendations: generateHealthRecommendations(recentEvents, eventTypes),
    };
  }
});

/**
 * Test webhook connectivity
 */
export const testWebhookConnectivity = action({
  handler: async (ctx) => {
    console.log('🧪 Testing webhook connectivity...');
    
    try {
      // Create a test webhook event record
      const testEventId = `test-${Date.now()}`;
      
      await ctx.runMutation("webhooks:markEventProcessed", {
        eventId: testEventId,
        eventType: "test.connectivity",
      });
      
      // Verify it was created
      const testEvent = await ctx.runQuery("webhooks:checkEventProcessed", {
        eventId: testEventId
      });
      
      return {
        success: true,
        message: "Webhook system is operational",
        testEventId,
        testEventExists: !!testEvent,
        timestamp: new Date().toISOString(),
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: "Webhook system has issues",
        timestamp: new Date().toISOString(),
      };
    }
  }
});

/**
 * Generate health recommendations based on webhook activity
 */
function generateHealthRecommendations(events: any[], eventTypes: any): string[] {
  const recommendations = [];
  
  if (events.length === 0) {
    recommendations.push("⚠️ No webhook events received - check Stripe Dashboard webhook configuration");
    recommendations.push("📋 Verify webhook endpoint URL: https://enchanted-quail-174.convex.site/stripe/webhooks/handleStripeWebhook");
    recommendations.push("🔑 Verify webhook signing secret is configured correctly");
  }
  
  if (!eventTypes['customer.subscription.updated']) {
    recommendations.push("❌ No subscription.updated events - trial-to-paid conversions will fail");
    recommendations.push("📝 Add 'customer.subscription.updated' event in Stripe Dashboard");
  }
  
  if (!eventTypes['checkout.session.completed']) {
    recommendations.push("❌ No checkout.session.completed events - new subscriptions may not be tracked");
    recommendations.push("📝 Add 'checkout.session.completed' event in Stripe Dashboard");
  }
  
  const failedEvents = events.filter(e => e.error);
  if (failedEvents.length > 0) {
    recommendations.push(`⚠️ ${failedEvents.length} webhook events failed processing`);
    recommendations.push("🔍 Check webhook processing logs for errors");
  }
  
  if (recommendations.length === 0) {
    recommendations.push("✅ Webhook system appears healthy");
  }
  
  return recommendations;
}

/**
 * Get webhook configuration status
 */
export const getWebhookConfig = query({
  handler: async (ctx) => {
    // Check if we have the required environment variables
    const hasWebhookSecret = !!process.env.STRIPE_WEBHOOK_SECRET && 
                             process.env.STRIPE_WEBHOOK_SECRET !== 'whsec_your_webhook_secret_here';
    
    const hasStripeKey = !!process.env.STRIPE_SECRET_KEY && 
                        process.env.STRIPE_SECRET_KEY !== 'sk_test_your_secret_key_here';
    
    return {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      webhookEndpoint: 'https://enchanted-quail-174.convex.site/stripe',
      testEndpoint: 'https://enchanted-quail-174.convex.site/test',
      configuration: {
        hasWebhookSecret,
        hasStripeKey,
        webhookSecretLength: process.env.STRIPE_WEBHOOK_SECRET?.length || 0,
        stripeKeyLength: process.env.STRIPE_SECRET_KEY?.length || 0,
      },
      requiredEvents: [
        'customer.subscription.created',
        'customer.subscription.updated',
        'customer.subscription.deleted',
        'checkout.session.completed',
        'invoice.paid',
        'invoice.payment_failed',
      ],
      isConfigured: hasWebhookSecret && hasStripeKey,
      nextSteps: hasWebhookSecret && hasStripeKey 
        ? ['✅ Configuration looks good', '🧪 Test with a new subscription']
        : ['❌ Missing environment variables', '🔧 Configure STRIPE_WEBHOOK_SECRET and STRIPE_SECRET_KEY'],
    };
  }
});
