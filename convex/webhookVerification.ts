import { query, action } from './_generated/server';
import { v } from 'convex/values';
import { internal } from './_generated/api';

/**
 * Query to check recent webhook events and subscription updates
 */
export const getRecentWebhookEvents = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    const limit = args.limit || 10;
    
    // Get recent webhook events
    const webhookEvents = await ctx.db
      .query("webhookEvents")
      .order("desc")
      .take(limit);

    // Get recent subscription updates
    const subscriptions = await ctx.db
      .query("subscriptions")
      .order("desc")
      .take(limit);

    return {
      webhookEvents: webhookEvents.map(event => ({
        _id: event._id,
        type: event.type,
        processed: event.processed,
        createdAt: event.createdAt,
        data: event.data ? {
          id: event.data.id,
          object: event.data.object,
          status: (event.data as any).status,
        } : null,
      })),
      subscriptions: subscriptions.map(sub => ({
        _id: sub._id,
        userId: sub.userId,
        status: sub.status,
        planLevel: sub.planLevel,
        trialEnd: sub.trialEnd,
        trialConvertedAt: sub.trialConvertedAt,
        stripeSubscriptionId: sub.stripeSubscriptionId,
        updatedAt: sub.updatedAt,
        createdAt: sub.createdAt,
      })),
      timestamp: Date.now(),
    };
  }
});

/**
 * Action to manually trigger subscription status refresh from Stripe
 */
export const refreshSubscriptionFromStripe = action({
  args: { 
    userId: v.string(),
    stripeSubscriptionId: v.optional(v.string())
  },
  handler: async (ctx, args) => {
    console.log('🔄 Manual subscription refresh requested for user:', args.userId);

    try {
      // Get current subscription
      const subscription = await ctx.runQuery(internal.subscriptions.getUserSubscriptionInternal, {
        userId: args.userId
      });

      if (!subscription) {
        return {
          success: false,
          error: 'No subscription found for user',
          userId: args.userId,
        };
      }

      console.log('📊 Current subscription state:', {
        _id: subscription._id,
        status: subscription.status,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
        updatedAt: subscription.updatedAt,
      });

      // TODO: Add Stripe API call to fetch latest subscription status
      // For now, just return current state
      return {
        success: true,
        subscription: {
          _id: subscription._id,
          status: subscription.status,
          planLevel: subscription.planLevel,
          trialEnd: subscription.trialEnd,
          trialConvertedAt: subscription.trialConvertedAt,
          stripeSubscriptionId: subscription.stripeSubscriptionId,
          updatedAt: subscription.updatedAt,
        },
        timestamp: Date.now(),
      };

    } catch (error) {
      console.error('❌ Error refreshing subscription:', error);
      return {
        success: false,
        error: error.message,
        userId: args.userId,
        timestamp: Date.now(),
      };
    }
  }
});

/**
 * Query to check subscription status for a specific user with detailed analysis
 */
export const analyzeSubscriptionStatus = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // Get subscription
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // Get user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    // Get recent webhook events for this user's subscription
    const webhookEvents = subscription ? await ctx.db
      .query("webhookEvents")
      .filter((q) => q.eq(q.field("data.id"), subscription.stripeSubscriptionId))
      .order("desc")
      .take(5) : [];

    const currentTime = Date.now();

    return {
      userId: args.userId,
      currentTime,
      subscription: subscription ? {
        _id: subscription._id,
        status: subscription.status,
        planLevel: subscription.planLevel,
        trialStart: subscription.trialStart,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        stripeCustomerId: subscription.stripeCustomerId,
        createdAt: subscription.createdAt,
        updatedAt: subscription.updatedAt,
      } : null,
      user: user ? {
        _id: user._id,
        role: user.role,
        subscriptionStatus: user.subscriptionStatus,
        hasActiveSubscription: user.hasActiveSubscription,
        trialEndsAt: user.trialEndsAt,
      } : null,
      webhookEvents: webhookEvents.map(event => ({
        _id: event._id,
        type: event.type,
        processed: event.processed,
        createdAt: event.createdAt,
        data: {
          id: event.data?.id,
          status: (event.data as any)?.status,
          trial_end: (event.data as any)?.trial_end,
        }
      })),
      analysis: {
        hasSubscription: !!subscription,
        subscriptionStatus: subscription?.status,
        isTrialExpiredByTime: subscription?.trialEnd ? currentTime >= subscription.trialEnd : false,
        shouldBeActive: subscription?.status === 'active' || (subscription?.trialConvertedAt && subscription?.status !== 'trialing'),
        timeUntilTrialEnd: subscription?.trialEnd ? subscription.trialEnd - currentTime : null,
        recentWebhookCount: webhookEvents.length,
        lastWebhookType: webhookEvents[0]?.type,
        lastWebhookProcessed: webhookEvents[0]?.processed,
      }
    };
  }
});
