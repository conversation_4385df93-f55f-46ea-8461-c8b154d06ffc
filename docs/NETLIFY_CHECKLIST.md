# JobbLogg Netlify Deployment Checklist

## Pre-Deployment Checklist

### ✅ Repository Setup
- [ ] Alle endringer committet til `main` branch
- [ ] `netlify.toml` konfigurert
- [ ] `package.json` har `build:netlify` script
- [ ] Vite config oppdatert for produksjon
- [ ] `.gitignore` oppdatert (Docker-filer ekskludert)

### ✅ Environment Variables (Netlify Dashboard)
- [ ] `VITE_CONVEX_URL=https://standing-aardvark-575.convex.cloud`
- [ ] `VITE_CLERK_PUBLISHABLE_KEY=pk_live_Y2xlcmsuam9iYmxvZ2cubm8k`
- [ ] `VITE_GOOGLE_MAPS_API_KEY=AIzaSyBceCUk15jgn4U3BeyuCrdpGwIlXhFkuAs`
- [ ] `VITE_STRIPE_PUBLISHABLE_KEY=[DIN_PRODUCTION_KEY]`
- [ ] `VITE_ALLOW_INDEXING=true`

### ✅ External Services
- [ ] Convex production deployment aktiv
- [ ] Clerk production keys konfigurert
- [ ] Stripe production keys klare
- [ ] Google Maps API fungerer

## Deployment Steps

### 1. Netlify Site Creation
- [ ] Gå til [Netlify Dashboard](https://app.netlify.com)
- [ ] "New site from Git" → GitHub → `djrobbieh/JobbLogg`
- [ ] Build settings: `npm run build` / `dist`

### 2. Environment Variables Setup
- [ ] Site settings → Environment variables
- [ ] Legg til alle VITE_ variabler
- [ ] Test at build fungerer

### 3. Domain Configuration
- [ ] Site settings → Domain management
- [ ] Legg til `jobblogg.no`
- [ ] Oppdater DNS hos domain provider
- [ ] Vent på SSL sertifikat (automatisk)

### 4. Testing
- [ ] Test på [site-name].netlify.app
- [ ] Verifiser Clerk login
- [ ] Test Convex database tilkobling
- [ ] Test Stripe integration
- [ ] Test alle hovedfunksjoner

### 5. Go Live
- [ ] Oppdater DNS til å peke på Netlify
- [ ] Test på jobblogg.no
- [ ] Overvåk for feil i første timer

## Post-Deployment

### Cleanup
- [ ] Fjern Docker-filer fra repo
- [ ] Fjern deployment scripts
- [ ] Oppdater README med nye instruksjoner
- [ ] Arkiver Hetzner server

### Monitoring
- [ ] Sett opp Netlify notifications
- [ ] Overvåk build logs første dager
- [ ] Test alle kritiske flows

## Rollback Plan
Hvis noe går galt:
1. Netlify Dashboard → Deploys
2. Finn siste fungerende deployment
3. Klikk "Publish deploy"

## Support
- Netlify Docs: https://docs.netlify.com
- Netlify Support: https://answers.netlify.com
