# JobbLogg - React + TypeScript + Vite

JobbLogg er en moderne prosjektloggingsapplikasjon bygget for norske entreprenører og håndverkere.

## 🚀 Quick Start

### Prerequisites
- Node.js 20+
- npm eller yarn
- Git

### Local Development
1. Clone repository:
   ```bash
   git clone https://github.com/djrobbieh/JobbLogg.git
   cd JobbLogg
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your development keys
   ```

4. Start development server:
   ```bash
   npm run dev
   ```

### Production Deployment (Netlify)

JobbLogg er nå deployet på Netlify for enkel og skalerbar hosting.

1. **Automatisk deployment**: Push til `main` branch trigger automatisk deployment
2. **Manual deployment**: Gå til Netlify Dashboard → Deploys → "Trigger deploy"
3. **Environment variables**: Konfigurert i Netlify Dashboard under Site settings

Se `NETLIFY_DEPLOYMENT_GUIDE.md` for detaljert setup-guide.

## 📁 Project Structure

### Core Application
```
src/
├── components/     # Reusable UI components
├── pages/         # Route components
├── hooks/         # Custom React hooks
├── services/      # External service integrations
├── utils/         # Utility functions
└── types/         # TypeScript type definitions
```

### Configuration Files
- `netlify.toml` - Netlify deployment configuration
- `vite.config.ts` - Vite build configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `tsconfig.json` - TypeScript configuration

### Environment Files
- `.env.example` - Template with all variables
- `.env.local` - Local development (gitignored)
- `.env.netlify` - Netlify production template

## 🌐 Access URLs

### Development
- Local: http://localhost:5173

### Production
- Live site: https://jobblogg.netlify.app
- Custom domain: https://jobblogg.no (when configured)

## 🔧 Tech Stack

### Frontend
- **Framework**: React 19 + TypeScript
- **Build Tool**: Vite 7
- **Styling**: Tailwind CSS v4 + daisyUI
- **Routing**: React Router v7

### Backend & Services
- **Backend**: Convex (serverless)
- **Database**: Convex (managed)
- **Authentication**: Clerk
- **Payments**: Stripe
- **Email**: Resend
- **Maps**: Google Maps API

### Deployment
- **Hosting**: Netlify
- **CDN**: Netlify Global CDN
- **SSL**: Automatic via Netlify
- **Deployment**: Git-based (auto-deploy on push to main)

## 📚 Documentation

- `NETLIFY_DEPLOYMENT_GUIDE.md` - Detaljert guide for Netlify setup
- `NETLIFY_CHECKLIST.md` - Deployment checklist
- `JOBBLOGG_ARCHITECTURE_DOCUMENTATION.md` - Arkitektur oversikt

## 🚀 Scripts

```bash
# Development
npm run dev              # Start development server
npm run dev:clear        # Clear ports and start dev server

# Building
npm run build            # Full build with validation
npm run build:netlify    # Netlify-optimized build
npm run preview          # Preview production build

# Quality Assurance
npm run lint             # Run ESLint
npm run type-check       # TypeScript type checking
npm run validate:imports # Validate import statements

# Utilities
npm run port-check       # Check port availability
npm run port-clear       # Kill processes on development ports
```

## 🔄 Migration Status

✅ **Migrated from Hetzner/Docker to Netlify**
- Simplified deployment process
- Automatic SSL and CDN
- Git-based deployments
- No server maintenance required

## 📞 Support

For spørsmål eller problemer:
1. Sjekk dokumentasjonen i `/docs` mappen
2. Se deployment guides for Netlify setup
3. Kontakt utviklingsteamet
