# Stripe Webhook Testing Guide for JobbLogg

This guide provides comprehensive instructions for testing Stripe webhooks in the JobbLogg development environment.

## 🚀 Quick Start

### Prerequisites

1. **Stripe CLI installed**
   ```bash
   # macOS
   brew install stripe/stripe-cli/stripe
   
   # Linux/Windows - see https://stripe.com/docs/stripe-cli#install
   ```

2. **Stripe CLI authenticated**
   ```bash
   stripe login
   ```

3. **Environment variables configured**
   ```bash
   # In .env.local
   STRIPE_SECRET_KEY=sk_test_your_secret_key_here
   STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
   ```

### Interactive Testing Script

```bash
# Run the interactive testing script
./scripts/stripe-webhook-testing.sh
```

## 📋 Testing Scenarios

### 1. Basic Connectivity Test

Test if the webhook endpoint is reachable:

```bash
# Test Convex development endpoint
./scripts/stripe-webhook-testing.sh test-endpoint

# Test specific endpoint
./scripts/stripe-webhook-testing.sh test-endpoint "https://your-domain.convex.site/stripe"
```

### 2. Webhook Event Forwarding

Forward Stripe events to your local development environment:

```bash
# Start forwarding to Convex development
./scripts/stripe-webhook-testing.sh forward

# Start forwarding to localhost (if running locally)
./scripts/stripe-webhook-testing.sh forward "http://localhost:3000/stripe"
```

### 3. Individual Event Testing

Test specific webhook events:

```bash
# Test checkout completion
stripe trigger checkout.session.completed

# Test subscription creation
stripe trigger customer.subscription.created

# Test payment failure
stripe trigger invoice.payment_failed
```

### 4. Comprehensive Event Testing

Test all required webhook events:

```bash
./scripts/stripe-webhook-testing.sh test-events
```

## 🎯 Critical Test Scenarios

### Trial-to-Paid Conversion Flow

1. **Start webhook forwarding**
   ```bash
   stripe listen --forward-to https://enchanted-quail-174.convex.site/stripe
   ```

2. **Trigger checkout completion**
   ```bash
   stripe trigger checkout.session.completed --add subscription=sub_test123 --add customer=cus_test123
   ```

3. **Verify subscription activation**
   ```bash
   stripe trigger customer.subscription.created --add customer=cus_test123
   ```

### Payment Failure Handling

1. **Trigger payment failure**
   ```bash
   stripe trigger invoice.payment_failed --add subscription=sub_test123
   ```

2. **Trigger retry success**
   ```bash
   stripe trigger invoice.paid --add subscription=sub_test123
   ```

### Subscription Lifecycle

1. **Create subscription**
   ```bash
   stripe trigger customer.subscription.created
   ```

2. **Update subscription (plan change)**
   ```bash
   stripe trigger customer.subscription.updated
   ```

3. **Cancel subscription**
   ```bash
   stripe trigger customer.subscription.deleted
   ```

## 🔍 Monitoring and Debugging

### Real-time Webhook Logs

Monitor webhook processing in real-time:

```bash
# View Stripe CLI logs
tail -f stripe-webhook-test.log

# View Convex logs (in separate terminal)
npx convex logs --tail
```

### Error Log Analysis

Check webhook error logs in the database:

```bash
# Get error statistics
npx convex run webhooks:getWebhookErrorStats '{}'

# Check for duplicate events
npx convex run webhooks:checkForDuplicateEvents '{}'

# Validate webhook events table
npx convex run webhooks:validateWebhookEventsTable '{}'
```

### Test Error Logging System

```bash
# Test all error logging scenarios
npx convex run webhooks:testWebhookErrorLogging '{"testScenario": "all_severities"}'

# Clean up test data
npx convex run webhooks:cleanupTestErrorLogs '{}'
```

## 🧪 Advanced Testing

### Idempotency Testing

Test duplicate event handling:

```bash
# Test idempotency with same event ID
stripe trigger checkout.session.completed --add metadata[test_id]=idempotency_test_1
stripe trigger checkout.session.completed --add metadata[test_id]=idempotency_test_1
```

### Error Scenario Testing

Test various error conditions:

```bash
# Test with invalid signature (should fail)
curl -X POST https://enchanted-quail-174.convex.site/stripe \
  -H "Content-Type: application/json" \
  -d '{"test": "invalid_signature"}'

# Test with missing signature header
curl -X POST https://enchanted-quail-174.convex.site/stripe \
  -H "Content-Type: application/json" \
  -d '{"test": "no_signature"}'
```

### Performance Testing

Test webhook processing performance:

```bash
# Trigger multiple events rapidly
for i in {1..10}; do
  stripe trigger customer.created --add metadata[batch_test]=$i &
done
wait
```

## 📊 Webhook Event Reference

### Required Events for JobbLogg

| Event Type | Purpose | Critical |
|------------|---------|----------|
| `customer.created` | New customer setup | Medium |
| `checkout.session.completed` | Trial-to-paid conversion | **High** |
| `customer.subscription.created` | Subscription activation | **High** |
| `customer.subscription.updated` | Plan changes | **High** |
| `customer.subscription.deleted` | Cancellation handling | **High** |
| `customer.subscription.trial_will_end` | Trial expiration notice | Medium |
| `invoice.paid` | Payment confirmation | **High** |
| `invoice.payment_failed` | Payment failure handling | **High** |
| `payment_intent.succeeded` | One-time payment success | Medium |
| `payment_intent.payment_failed` | One-time payment failure | Medium |

### Test Event Examples

```bash
# Critical events for subscription flow
stripe trigger checkout.session.completed
stripe trigger customer.subscription.created
stripe trigger invoice.paid
stripe trigger invoice.payment_failed

# Lifecycle events
stripe trigger customer.subscription.updated
stripe trigger customer.subscription.deleted
stripe trigger customer.subscription.trial_will_end

# Customer events
stripe trigger customer.created

# Payment intent events (for one-time payments)
stripe trigger payment_intent.succeeded
stripe trigger payment_intent.payment_failed
```

## 🔧 Troubleshooting

### Common Issues

1. **Webhook endpoint not reachable**
   - Check Convex deployment status
   - Verify endpoint URL is correct
   - Test with curl command

2. **Signature verification failing**
   - Ensure STRIPE_WEBHOOK_SECRET is set correctly
   - Check that webhook secret matches Stripe dashboard
   - Verify request body is not modified

3. **Events not being processed**
   - Check Convex logs for errors
   - Verify webhook handlers are implemented
   - Check database connectivity

4. **Duplicate events**
   - Verify idempotency checking is working
   - Check webhook events table for duplicates
   - Monitor error logs for race conditions

### Debug Commands

```bash
# Check Stripe CLI configuration
stripe config --list

# Test webhook endpoint manually
curl -X POST https://enchanted-quail-174.convex.site/stripe/webhooks/handleStripeWebhook \
  -H "Content-Type: application/json" \
  -d '{"test": "manual"}'

# View recent webhook events in Stripe Dashboard
stripe events list --limit 10

# Check webhook endpoint status in Stripe Dashboard
stripe listen --print-secret
```

## 📝 Best Practices

1. **Always test in development environment first**
2. **Monitor webhook processing logs during testing**
3. **Test both success and failure scenarios**
4. **Verify idempotency handling with duplicate events**
5. **Test error logging and monitoring capabilities**
6. **Clean up test data after testing sessions**
7. **Document any issues found during testing**

## 🔗 Related Documentation

- [Stripe CLI Documentation](https://stripe.com/docs/stripe-cli)
- [Stripe Webhooks Guide](https://stripe.com/docs/webhooks)
- [JobbLogg Webhook Implementation](../convex/stripe/webhooks.ts)
- [Error Logging System](../convex/webhooks.ts)
