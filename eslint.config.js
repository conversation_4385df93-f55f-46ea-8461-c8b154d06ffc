import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'
import { globalIgnores } from 'eslint/config'

export default tseslint.config([
  globalIgnores(['dist', 'convex/_generated/**']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      js.configs.recommended,
      tseslint.configs.recommended,
      reactHooks.configs['recommended-latest'],
      reactRefresh.configs.vite,
    ],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    rules: {
      /**
       * ✅ PHASE 4 COMPLETED: ESLint Rules Re-enabled
       *
       * All critical TypeScript and React rules have been restored:
       * - TypeScript any types: Warning level (encourage proper typing)
       * - Unused variables: Warning with underscore prefix ignore pattern
       * - React hooks dependencies: Warning level for better performance
       * - Regex escapes: Still disabled (low priority)
       * - Component exports: Still disabled (mixed exports during transition)
       */

      // CRITICAL RULES - MUST REMAIN ENFORCED
      'react-hooks/rules-of-hooks': 'error',
      'no-var': 'error',
      'no-prototype-builtins': 'error',
      'prefer-const': 'error',

      // RE-ENABLED: TypeScript any types should be avoided
      '@typescript-eslint/no-explicit-any': 'warn',
      'no-useless-escape': 'off',
      'react-hooks/exhaustive-deps': 'warn',
      '@typescript-eslint/no-unused-vars': ['warn', {
        'argsIgnorePattern': '^_',
        'varsIgnorePattern': '^_',
        'ignoreRestSiblings': true
      }],
      'react-refresh/only-export-components': 'off', // Temporary - mixed exports during transition
    },
  },
])
