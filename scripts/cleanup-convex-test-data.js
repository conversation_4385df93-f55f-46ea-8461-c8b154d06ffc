#!/usr/bin/env node

/**
 * Convex Test Data Cleanup Script
 * 
 * This script helps clean up test data from Convex database that references
 * deleted Stripe customers, including:
 * - Subscription records with deleted Stripe customer IDs
 * - User records with deleted Stripe customer IDs
 * - Related billing and payment data
 * 
 * Usage:
 * node scripts/cleanup-convex-test-data.js [--dry-run]
 */

const { ConvexHttpClient } = require("convex/browser");
const fs = require('fs');
const path = require('path');

// Load environment variables manually
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});

const CONVEX_URL = envVars.VITE_CONVEX_URL;
const STRIPE_SECRET_KEY = envVars.STRIPE_SECRET_KEY;

if (!CONVEX_URL) {
  console.error('❌ VITE_CONVEX_URL not found in environment variables');
  process.exit(1);
}

if (!STRIPE_SECRET_KEY) {
  console.error('❌ STRIPE_SECRET_KEY not found in environment variables');
  process.exit(1);
}

if (!STRIPE_SECRET_KEY.startsWith('sk_test_')) {
  console.error('❌ This script only works with test mode keys (sk_test_...)');
  process.exit(1);
}

const stripe = require('stripe')(STRIPE_SECRET_KEY);
const client = new ConvexHttpClient(CONVEX_URL);

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');

console.log('🧹 Convex Test Data Cleanup Script');
console.log(`📊 Mode: ${isDryRun ? 'DRY RUN (no actual deletions)' : 'LIVE CLEANUP'}`);
console.log('');

/**
 * Check if a Stripe customer exists
 */
async function stripeCustomerExists(customerId) {
  try {
    await stripe.customers.retrieve(customerId);
    return true;
  } catch (error) {
    if (error.code === 'resource_missing') {
      return false;
    }
    throw error;
  }
}

/**
 * Clean up subscription records with deleted Stripe customers
 */
async function cleanupSubscriptions() {
  console.log('📋 Cleaning up Subscription records...');
  
  try {
    // Get all subscription records
    const subscriptions = await client.query("subscriptions:list", {});
    console.log(`   Found ${subscriptions.length} subscription records`);
    
    if (subscriptions.length === 0) {
      console.log('   ✅ No subscription records to clean up');
      return;
    }
    
    let deletedCount = 0;
    let validCount = 0;
    
    for (const subscription of subscriptions) {
      const customerId = subscription.stripeCustomerId;
      
      if (!customerId) {
        console.log(`   ⚠️  Subscription ${subscription._id} has no stripeCustomerId`);
        continue;
      }
      
      // Check if Stripe customer still exists
      const customerExists = await stripeCustomerExists(customerId);
      
      if (!customerExists) {
        console.log(`   🗑️  Found orphaned subscription: ${subscription._id} (customer: ${customerId})`);
        
        if (isDryRun) {
          console.log(`   [DRY RUN] Would delete subscription: ${subscription._id}`);
          deletedCount++;
        } else {
          try {
            await client.mutation("subscriptions:deleteSubscription", { 
              subscriptionId: subscription._id 
            });
            console.log(`   ✅ Deleted subscription: ${subscription._id}`);
            deletedCount++;
          } catch (error) {
            console.log(`   ❌ Failed to delete subscription ${subscription._id}: ${error.message}`);
          }
        }
      } else {
        validCount++;
      }
    }
    
    console.log(`   📊 Valid subscriptions: ${validCount}`);
    console.log(`   📊 ${isDryRun ? 'Would delete' : 'Deleted'} subscriptions: ${deletedCount}`);
    
  } catch (error) {
    console.error(`   ❌ Error cleaning subscriptions: ${error.message}`);
  }
}

/**
 * Clean up user records with deleted Stripe customers
 */
async function cleanupUsers() {
  console.log('👥 Cleaning up User records...');
  
  try {
    // Get all user records that have stripeCustomerId
    const users = await client.query("users:listUsersWithStripeCustomers", {});
    console.log(`   Found ${users.length} user records with Stripe customer IDs`);
    
    if (users.length === 0) {
      console.log('   ✅ No user records with Stripe customers to clean up');
      return;
    }
    
    let updatedCount = 0;
    let validCount = 0;
    
    for (const user of users) {
      const customerId = user.stripeCustomerId;
      
      if (!customerId) {
        continue;
      }
      
      // Check if Stripe customer still exists
      const customerExists = await stripeCustomerExists(customerId);
      
      if (!customerExists) {
        console.log(`   🗑️  Found user with deleted customer: ${user._id} (customer: ${customerId})`);
        
        if (isDryRun) {
          console.log(`   [DRY RUN] Would clear stripeCustomerId for user: ${user._id}`);
          updatedCount++;
        } else {
          try {
            await client.mutation("users:clearStripeCustomerId", { 
              userId: user._id 
            });
            console.log(`   ✅ Cleared stripeCustomerId for user: ${user._id}`);
            updatedCount++;
          } catch (error) {
            console.log(`   ❌ Failed to update user ${user._id}: ${error.message}`);
          }
        }
      } else {
        validCount++;
      }
    }
    
    console.log(`   📊 Valid user records: ${validCount}`);
    console.log(`   📊 ${isDryRun ? 'Would update' : 'Updated'} user records: ${updatedCount}`);
    
  } catch (error) {
    console.error(`   ❌ Error cleaning users: ${error.message}`);
  }
}

/**
 * Main cleanup function
 */
async function main() {
  try {
    console.log('🚀 Starting Convex cleanup...\n');
    
    await cleanupSubscriptions();
    console.log('');
    
    await cleanupUsers();
    console.log('');
    
    console.log('✅ Convex cleanup completed!');
    
    if (isDryRun) {
      console.log('\n💡 This was a dry run. To actually clean the data, run without --dry-run flag');
    } else {
      console.log('\n⚠️  Test data has been cleaned up. This action cannot be undone.');
    }
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, cleanupSubscriptions, cleanupUsers };
