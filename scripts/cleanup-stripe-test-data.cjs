#!/usr/bin/env node

/**
 * Stripe Test Data Cleanup Script
 * 
 * This script helps clean up test data from Stripe test mode including:
 * - Payment Intents
 * - Customers (and their associated subscriptions)
 * - Charges
 * - Invoices
 * - Payment Methods
 * 
 * Usage:
 * node scripts/cleanup-stripe-test-data.js [--dry-run] [--type=all|customers|payment_intents|charges|invoices]
 */

const stripe = require('stripe');
const fs = require('fs');
const path = require('path');

// Load environment variables manually
const envPath = path.join(__dirname, '..', '.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};
envContent.split('\n').forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});

const STRIPE_SECRET_KEY = envVars.STRIPE_SECRET_KEY;

if (!STRIPE_SECRET_KEY) {
  console.error('❌ STRIPE_SECRET_KEY not found in environment variables');
  process.exit(1);
}

if (!STRIPE_SECRET_KEY.startsWith('sk_test_')) {
  console.error('❌ This script only works with test mode keys (sk_test_...)');
  process.exit(1);
}

const stripeClient = stripe(STRIPE_SECRET_KEY);

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');
const typeArg = args.find(arg => arg.startsWith('--type='));
const cleanupType = typeArg ? typeArg.split('=')[1] : 'all';

console.log('🧹 Stripe Test Data Cleanup Script');
console.log(`📊 Mode: ${isDryRun ? 'DRY RUN (no actual deletions)' : 'LIVE CLEANUP'}`);
console.log(`🎯 Type: ${cleanupType}`);
console.log('');

/**
 * Delete payment intents
 */
async function cleanupPaymentIntents() {
  console.log('💳 Cleaning up Payment Intents...');
  
  try {
    const paymentIntents = await stripeClient.paymentIntents.list({ limit: 100 });
    console.log(`   Found ${paymentIntents.data.length} payment intents`);
    
    if (isDryRun) {
      console.log('   [DRY RUN] Would delete payment intents:');
      paymentIntents.data.forEach(pi => {
        console.log(`   - ${pi.id} (${pi.status}, ${pi.amount} ${pi.currency})`);
      });
      return;
    }
    
    // Note: Payment intents cannot be deleted via API, but we can cancel them
    let cancelledCount = 0;
    for (const pi of paymentIntents.data) {
      if (pi.status === 'requires_payment_method' || pi.status === 'requires_confirmation') {
        try {
          await stripeClient.paymentIntents.cancel(pi.id);
          cancelledCount++;
          console.log(`   ✅ Cancelled: ${pi.id}`);
        } catch (error) {
          console.log(`   ⚠️  Could not cancel ${pi.id}: ${error.message}`);
        }
      } else {
        console.log(`   ℹ️  Cannot cancel ${pi.id} (status: ${pi.status})`);
      }
    }
    
    console.log(`   📊 Cancelled ${cancelledCount} payment intents`);
    console.log('   ℹ️  Note: Completed payment intents cannot be deleted, only cancelled if pending');
    
  } catch (error) {
    console.error(`   ❌ Error cleaning payment intents: ${error.message}`);
  }
}

/**
 * Delete customers (and their subscriptions)
 */
async function cleanupCustomers() {
  console.log('👥 Cleaning up Customers...');
  
  try {
    const customers = await stripeClient.customers.list({ limit: 100 });
    console.log(`   Found ${customers.data.length} customers`);
    
    if (isDryRun) {
      console.log('   [DRY RUN] Would delete customers:');
      customers.data.forEach(customer => {
        console.log(`   - ${customer.id} (${customer.email || 'no email'}, ${customer.name || 'no name'})`);
      });
      return;
    }
    
    let deletedCount = 0;
    for (const customer of customers.data) {
      try {
        // First, cancel any active subscriptions
        const subscriptions = await stripeClient.subscriptions.list({ 
          customer: customer.id,
          status: 'active'
        });
        
        for (const subscription of subscriptions.data) {
          await stripeClient.subscriptions.cancel(subscription.id);
          console.log(`   🔄 Cancelled subscription: ${subscription.id}`);
        }
        
        // Then delete the customer
        await stripeClient.customers.del(customer.id);
        deletedCount++;
        console.log(`   ✅ Deleted: ${customer.id} (${customer.email || 'no email'})`);
      } catch (error) {
        console.log(`   ⚠️  Could not delete ${customer.id}: ${error.message}`);
      }
    }
    
    console.log(`   📊 Deleted ${deletedCount} customers`);
    
  } catch (error) {
    console.error(`   ❌ Error cleaning customers: ${error.message}`);
  }
}

/**
 * Delete charges (note: charges cannot be deleted, only refunded)
 */
async function cleanupCharges() {
  console.log('💰 Cleaning up Charges...');
  
  try {
    const charges = await stripeClient.charges.list({ limit: 100 });
    console.log(`   Found ${charges.data.length} charges`);
    
    if (isDryRun) {
      console.log('   [DRY RUN] Would attempt to refund charges:');
      charges.data.forEach(charge => {
        console.log(`   - ${charge.id} (${charge.status}, ${charge.amount} ${charge.currency})`);
      });
      return;
    }
    
    console.log('   ℹ️  Note: Charges cannot be deleted, only refunded. Skipping charge cleanup.');
    console.log('   ℹ️  Test charges will be automatically cleaned up by Stripe over time.');
    
  } catch (error) {
    console.error(`   ❌ Error listing charges: ${error.message}`);
  }
}

/**
 * Delete invoices
 */
async function cleanupInvoices() {
  console.log('📄 Cleaning up Invoices...');
  
  try {
    const invoices = await stripeClient.invoices.list({ limit: 100 });
    console.log(`   Found ${invoices.data.length} invoices`);
    
    if (isDryRun) {
      console.log('   [DRY RUN] Would delete invoices:');
      invoices.data.forEach(invoice => {
        console.log(`   - ${invoice.id} (${invoice.status}, ${invoice.total} ${invoice.currency})`);
      });
      return;
    }
    
    let deletedCount = 0;
    for (const invoice of invoices.data) {
      try {
        if (invoice.status === 'draft') {
          await stripeClient.invoices.del(invoice.id);
          deletedCount++;
          console.log(`   ✅ Deleted: ${invoice.id}`);
        } else {
          console.log(`   ℹ️  Cannot delete ${invoice.id} (status: ${invoice.status})`);
        }
      } catch (error) {
        console.log(`   ⚠️  Could not delete ${invoice.id}: ${error.message}`);
      }
    }
    
    console.log(`   📊 Deleted ${deletedCount} draft invoices`);
    console.log('   ℹ️  Note: Only draft invoices can be deleted');
    
  } catch (error) {
    console.error(`   ❌ Error cleaning invoices: ${error.message}`);
  }
}

/**
 * Main cleanup function
 */
async function main() {
  try {
    console.log('🚀 Starting cleanup...\n');
    
    if (cleanupType === 'all' || cleanupType === 'payment_intents') {
      await cleanupPaymentIntents();
      console.log('');
    }
    
    if (cleanupType === 'all' || cleanupType === 'customers') {
      await cleanupCustomers();
      console.log('');
    }
    
    if (cleanupType === 'all' || cleanupType === 'charges') {
      await cleanupCharges();
      console.log('');
    }
    
    if (cleanupType === 'all' || cleanupType === 'invoices') {
      await cleanupInvoices();
      console.log('');
    }
    
    console.log('✅ Cleanup completed!');
    
    if (isDryRun) {
      console.log('\n💡 This was a dry run. To actually delete the data, run without --dry-run flag');
    } else {
      console.log('\n⚠️  Test data has been cleaned up. This action cannot be undone.');
    }
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, cleanupPaymentIntents, cleanupCustomers, cleanupCharges, cleanupInvoices };
