#!/usr/bin/env tsx

/**
 * Setup script for Stripe Customer Portal configuration
 * 
 * This script configures the Stripe Customer Portal with JobbLogg-specific
 * settings, Norwegian localization, and proper return URLs.
 * 
 * Usage:
 *   npm run setup-billing-portal
 *   or
 *   npx tsx scripts/setup-billing-portal.ts
 */

import { ConvexHttpClient } from "convex/browser";
import { api } from "../convex/_generated/api";

// Configuration
const CONVEX_URL = process.env.CONVEX_URL || process.env.VITE_CONVEX_URL;
const FRONTEND_URL = process.env.FRONTEND_URL || process.env.VITE_FRONTEND_URL || 'http://localhost:5173';

if (!CONVEX_URL) {
  console.error('❌ CONVEX_URL environment variable is required');
  process.exit(1);
}

const client = new ConvexHttpClient(CONVEX_URL);

async function setupBillingPortal() {
  console.log('🚀 Setting up Stripe Customer Portal configuration...');
  console.log(`📍 Frontend URL: ${FRONTEND_URL}`);
  console.log(`🔗 Convex URL: ${CONVEX_URL}`);

  try {
    // Configure the Customer Portal
    const result = await client.action(api.billingPortal.configureCustomerPortal, {});

    if (result.success) {
      console.log('✅ Stripe Customer Portal configured successfully!');
      console.log(`📋 Configuration ID: ${result.configurationId}`);
      console.log('\n📋 Portal Configuration:');
      console.log('   • Business Profile: JobbLogg prosjektdokumentasjon');
      console.log('   • Language: Norwegian (nb-NO)');
      console.log('   • Customer Updates: Email, Address, Phone, Tax ID');
      console.log('   • Payment Methods: Update enabled');
      console.log('   • Invoice History: Enabled');
      console.log('   • Subscription Updates: Enabled with prorations');
      console.log('   • Subscription Cancellation: At period end with reasons');
      console.log(`   • Return URL: ${FRONTEND_URL}/subscription?portal_return=true`);
      
      console.log('\n🎯 Next Steps:');
      console.log('   1. Test the billing portal in your application');
      console.log('   2. Verify Norwegian localization is working');
      console.log('   3. Test return URL handling');
      console.log('   4. Configure webhook endpoints if needed');
      
    } else {
      console.error('❌ Failed to configure Customer Portal:', result.error);
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Error setting up billing portal:', error);
    process.exit(1);
  }
}

async function verifyConfiguration() {
  console.log('\n🔍 Verifying portal configuration...');

  try {
    // Get the active configuration
    const config = await client.query(api.billingPortal.getActivePortalConfiguration, {});

    if (config) {
      console.log('✅ Portal configuration found in database');
      console.log(`📋 Configuration ID: ${config.configurationId}`);
      console.log(`📅 Created: ${new Date(config.createdAt).toLocaleString('nb-NO')}`);
      console.log(`📅 Updated: ${new Date(config.updatedAt).toLocaleString('nb-NO')}`);
      console.log(`🟢 Active: ${config.isActive ? 'Yes' : 'No'}`);
    } else {
      console.log('⚠️  No portal configuration found in database');
    }

  } catch (error) {
    console.error('❌ Error verifying configuration:', error);
  }
}

async function main() {
  console.log('🎯 JobbLogg Billing Portal Setup');
  console.log('================================\n');

  await setupBillingPortal();
  await verifyConfiguration();

  console.log('\n✨ Setup complete!');
  console.log('\n💡 Tips:');
  console.log('   • The portal will use Norwegian language automatically');
  console.log('   • Users can update payment methods, view invoices, and manage subscriptions');
  console.log('   • All changes are synced back to JobbLogg automatically');
  console.log('   • Portal sessions expire after 24 hours for security');
  
  console.log('\n🔧 Troubleshooting:');
  console.log('   • If portal doesn\'t open, check Stripe API keys');
  console.log('   • If return URL fails, verify FRONTEND_URL environment variable');
  console.log('   • Check Convex logs for detailed error messages');
  
  process.exit(0);
}

// Handle script execution
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  });
}

export { setupBillingPortal, verifyConfiguration };
