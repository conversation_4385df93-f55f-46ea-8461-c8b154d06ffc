#!/bin/bash

# JobbLogg Stripe CLI Webhook Testing Script
# This script sets up and manages Stripe CLI webhook testing for development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WEBHOOK_ENDPOINT="http://localhost:3000/stripe"
CONVEX_DEV_URL="https://enchanted-quail-174.convex.site/stripe"
LOG_FILE="stripe-webhook-test.log"

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Stripe CLI is installed
check_stripe_cli() {
    print_header "Checking Stripe CLI Installation"
    
    if ! command -v stripe &> /dev/null; then
        print_error "Stripe CLI is not installed"
        echo ""
        echo "Install Stripe CLI:"
        echo "  macOS: brew install stripe/stripe-cli/stripe"
        echo "  Linux: https://stripe.com/docs/stripe-cli#install"
        echo "  Windows: https://stripe.com/docs/stripe-cli#install"
        exit 1
    fi
    
    local version=$(stripe --version)
    print_success "Stripe CLI installed: $version"
}

# Check if user is logged in to Stripe
check_stripe_login() {
    print_header "Checking Stripe Authentication"
    
    if ! stripe config --list &> /dev/null; then
        print_error "Not logged in to Stripe"
        echo ""
        echo "Login to Stripe:"
        echo "  stripe login"
        exit 1
    fi
    
    print_success "Logged in to Stripe"
    stripe config --list | grep -E "(account_id|test_mode)"
}

# Start webhook forwarding
start_webhook_forwarding() {
    local endpoint=${1:-$WEBHOOK_ENDPOINT}
    
    print_header "Starting Webhook Forwarding"
    print_info "Forwarding to: $endpoint"
    print_info "Log file: $LOG_FILE"
    
    echo ""
    print_warning "This will start webhook forwarding. Press Ctrl+C to stop."
    echo ""
    
    # Start forwarding with logging
    stripe listen --forward-to "$endpoint" --log-level debug 2>&1 | tee "$LOG_FILE"
}

# Test individual webhook events
test_webhook_events() {
    print_header "Testing Individual Webhook Events"
    
    local events=(
        "customer.created"
        "checkout.session.completed"
        "customer.subscription.created"
        "customer.subscription.updated"
        "customer.subscription.deleted"
        "customer.subscription.trial_will_end"
        "invoice.paid"
        "invoice.payment_failed"
        "payment_intent.succeeded"
        "payment_intent.payment_failed"
    )
    
    for event in "${events[@]}"; do
        print_info "Testing event: $event"
        
        if stripe trigger "$event" --add="metadata[test]=true" --add="metadata[source]=stripe-cli"; then
            print_success "Successfully triggered: $event"
        else
            print_error "Failed to trigger: $event"
        fi
        
        # Brief pause between events
        sleep 2
    done
}

# Test webhook endpoint connectivity
test_endpoint_connectivity() {
    local endpoint=${1:-$CONVEX_DEV_URL}
    
    print_header "Testing Webhook Endpoint Connectivity"
    print_info "Testing endpoint: $endpoint"
    
    # Test with a simple POST request (will fail signature verification but should reach endpoint)
    local response=$(curl -s -w "%{http_code}" -X POST \
        -H "Content-Type: application/json" \
        -d '{"test": "connectivity"}' \
        "$endpoint" -o /tmp/webhook_test_response.txt)
    
    local body=$(cat /tmp/webhook_test_response.txt)
    
    if [[ "$response" == "400" ]] && [[ "$body" == *"signature"* ]]; then
        print_success "Endpoint is reachable and signature verification is working"
    elif [[ "$response" == "200" ]]; then
        print_success "Endpoint is reachable: $body"
    else
        print_error "Endpoint test failed: HTTP $response - $body"
        return 1
    fi
    
    rm -f /tmp/webhook_test_response.txt
}

# Show webhook testing menu
show_menu() {
    print_header "JobbLogg Stripe Webhook Testing"
    echo ""
    echo "Choose an option:"
    echo "1) Check Stripe CLI setup"
    echo "2) Test endpoint connectivity"
    echo "3) Start webhook forwarding (localhost)"
    echo "4) Start webhook forwarding (Convex dev)"
    echo "5) Test all webhook events"
    echo "6) Test specific event"
    echo "7) View webhook logs"
    echo "8) Clean up test data"
    echo "9) Show webhook setup instructions"
    echo "0) Exit"
    echo ""
}

# Show setup instructions
show_setup_instructions() {
    print_header "Stripe Webhook Setup Instructions"
    echo ""
    echo "1. Install and login to Stripe CLI:"
    echo "   stripe login"
    echo ""
    echo "2. For local development testing:"
    echo "   ./scripts/stripe-webhook-testing.sh"
    echo "   Choose option 3 to start forwarding"
    echo ""
    echo "3. For Convex development testing:"
    echo "   ./scripts/stripe-webhook-testing.sh"
    echo "   Choose option 4 to start forwarding"
    echo ""
    echo "4. In another terminal, trigger test events:"
    echo "   stripe trigger checkout.session.completed"
    echo ""
    echo "5. Required environment variables:"
    echo "   STRIPE_SECRET_KEY=sk_test_..."
    echo "   STRIPE_WEBHOOK_SECRET=whsec_..."
    echo ""
    echo "6. Webhook endpoint URLs:"
    echo "   Local: $WEBHOOK_ENDPOINT"
    echo "   Convex Dev: $CONVEX_DEV_URL"
    echo ""
}

# Clean up test data
cleanup_test_data() {
    print_header "Cleaning Up Test Data"
    
    print_info "This will clean up test webhook events and error logs"
    read -p "Continue? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Clean up log file
        if [[ -f "$LOG_FILE" ]]; then
            rm "$LOG_FILE"
            print_success "Removed log file: $LOG_FILE"
        fi
        
        print_info "Test data cleanup completed"
        print_warning "Note: Database cleanup should be done via Convex functions"
    else
        print_info "Cleanup cancelled"
    fi
}

# Main script logic
main() {
    if [[ $# -eq 0 ]]; then
        # Interactive mode
        while true; do
            show_menu
            read -p "Enter your choice (0-9): " choice
            echo ""
            
            case $choice in
                1) check_stripe_cli && check_stripe_login ;;
                2) test_endpoint_connectivity ;;
                3) start_webhook_forwarding "$WEBHOOK_ENDPOINT" ;;
                4) start_webhook_forwarding "$CONVEX_DEV_URL" ;;
                5) test_webhook_events ;;
                6) 
                    read -p "Enter event name (e.g., checkout.session.completed): " event_name
                    if [[ -n "$event_name" ]]; then
                        stripe trigger "$event_name" --add="metadata[test]=true"
                    fi
                    ;;
                7) 
                    if [[ -f "$LOG_FILE" ]]; then
                        tail -f "$LOG_FILE"
                    else
                        print_warning "No log file found: $LOG_FILE"
                    fi
                    ;;
                8) cleanup_test_data ;;
                9) show_setup_instructions ;;
                0) print_info "Goodbye!"; exit 0 ;;
                *) print_error "Invalid option. Please try again." ;;
            esac
            
            echo ""
            read -p "Press Enter to continue..."
            clear
        done
    else
        # Command line mode
        case $1 in
            "check") check_stripe_cli && check_stripe_login ;;
            "test-endpoint") test_endpoint_connectivity "$2" ;;
            "forward") start_webhook_forwarding "$2" ;;
            "test-events") test_webhook_events ;;
            "trigger") stripe trigger "$2" --add="metadata[test]=true" ;;
            "cleanup") cleanup_test_data ;;
            "help") show_setup_instructions ;;
            *) 
                echo "Usage: $0 [check|test-endpoint|forward|test-events|trigger|cleanup|help]"
                exit 1
                ;;
        esac
    fi
}

# Make script executable and run
main "$@"
