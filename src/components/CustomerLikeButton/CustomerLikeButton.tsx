import React, { useState } from 'react';
import { useMutation, useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { TextMuted } from '../ui';
import { PersonalizedLikeDisplay } from './PersonalizedLikeDisplay';

interface CustomerLikeButtonProps {
  logEntryId: string;
  projectId: string;
  sharedId: string;
  customerSessionId: string;
  customerName?: string;
  customerEmail?: string;
  isArchived?: boolean;
  showCount?: boolean;
  /** Project customer name for personalized like display */
  projectCustomerName?: string;
  /** Detailed like information for personalized display */
  likeDetails?: Array<{
    customerSessionId: string;
    customerName?: string;
    createdAt: number;
  }>;
}

export const CustomerLikeButton: React.FC<CustomerLikeButtonProps> = ({
  logEntryId,
  projectId,
  sharedId,
  customerSessionId,
  customerName,
  customerEmail,
  isArchived = false,
  showCount = true,
  projectCustomerName,
  likeDetails
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get current like status for this customer
    // const likeStatus = useQuery(
  //   api.imageLikes.getLikeStatus,
  //   { logEntryId: logEntryId as any, customerSessionId }
  // );
  const likeStatus = { hasLiked: false, liked: false };
  // Get total like count for this image
    // const likeCount = useQuery(
  //   api.imageLikes.getLikeCount,
  //   { logEntryId: logEntryId as any }
  // );
  const likeCount = { count: 0 }; const toggleLike = useMutation(api.imageLikes.toggleLike);
  const handleLikeToggle = async () => {
    if (isSubmitting || isArchived) return;

    setIsSubmitting(true);
    setIsAnimating(true);

    try {
      await toggleLike({
        logEntryId: logEntryId as any,
        projectId: projectId as any,
        sharedId,
        customerSessionId,
        customerName,
        customerEmail
      });

      // Animation duration
      setTimeout(() => setIsAnimating(false), 300);
    } catch (error) {
      console.error('Error toggling like:', error);
      setIsAnimating(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isArchived) {
    return null; // Don't show like button on archived projects
  }

  const isLiked = likeStatus?.liked || false;
  const totalLikes = likeCount?.count || 0;

  return (
    <div className="flex items-center gap-2">
      <button
        onClick={handleLikeToggle}
        disabled={isSubmitting}
        className={`
          group relative flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200
          ${isLiked
            ? 'bg-jobblogg-error text-white shadow-lg'
            : 'bg-white/90 backdrop-blur-sm text-jobblogg-text-medium hover:bg-jobblogg-error hover:text-white shadow-md'
          }
          ${isSubmitting ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-110'}
          ${isAnimating ? 'animate-pulse scale-125' : ''}
        `}
        aria-label={isLiked ? 'Fjern like' : 'Lik dette bildet'}
        title={isLiked ? 'Fjern like' : 'Lik dette bildet'}
      >
        {/* Heart Icon */}
        <svg
          className={`w-5 h-5 transition-all duration-200 ${isAnimating ? 'scale-125' : ''}`}
          fill={isLiked ? 'currentColor' : 'none'}
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={isLiked ? 0 : 2}
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>

        {/* Loading Spinner */}
        {isSubmitting && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin opacity-50"></div>
          </div>
        )}

        {/* Ripple Effect */}
        {isAnimating && (
          <div className="absolute inset-0 rounded-full bg-jobblogg-error opacity-30 animate-ping"></div>
        )}
      </button>

      {/* Enhanced Like Display */}
      {likeDetails ? (
        <PersonalizedLikeDisplay
          totalLikes={totalLikes}
          likes={likeDetails}
          currentCustomerSessionId={customerSessionId}
          projectCustomerName={projectCustomerName}
          showCount={showCount}
        />
      ) : (
        /* Fallback to original display if no detailed like information */
        showCount && totalLikes > 0 && (
          <TextMuted className="text-sm font-medium">
            {totalLikes} {totalLikes === 1 ? 'like' : 'likes'}
          </TextMuted>
        )
      )}
    </div>
  );
};

export default CustomerLikeButton;
