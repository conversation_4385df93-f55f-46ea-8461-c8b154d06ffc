import React from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { TextStrong, TextMedium, TextMuted, PrimaryButton } from '../ui';

interface EditHistoryModalProps {
  entryId: string;
  isOpen: boolean;
  onClose: () => void;
  isSharedView?: boolean;
  sharedId?: string;
}

interface EditHistoryVersion {
  version: number;
  editedAt: number;
  description: string;
  imageUrl?: string | null;
  changeType: string;
  changeSummary: string;
}

const EditHistoryModal: React.FC<EditHistoryModalProps> = ({
  entryId: _entryId,
  isOpen,
  onClose,
  isSharedView: _isSharedView = false,
  sharedId: _sharedId
}) => {
  // const { user } = useUser(); // TODO: Re-enable when needed

  // Fetch edit history - use different query for shared view
    // const editHistory = useQuery(
  //   isSharedView && sharedId ?
  //     api.logEntries.getLogEntryHistoryShared :
  //     api.logEntries.getLogEntryHistory,
  //   entryId ? (
  //     isSharedView && sharedId ? {
  //       entryId: entryId as any,
  //       sharedId: sharedId
  //     } : user?.id ? {
  //       entryId: entryId as any,
  //       userId: user.id
  //     } : "skip"
  //   ) : "skip"
  // ) as EditHistoryVersion[] | undefined;
  const editHistory: EditHistoryVersion[] | undefined = []; // Temporarily provide fallback array due to type instantiation issues

  // Format date in Norwegian format: DD.MM.YYYY HH:MM
  const formatNorwegianDateTime = (timestamp: number): string => {
    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return 'Ugyldig dato';
      }

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');

      return `${day}.${month}.${year} ${hours}:${minutes}`;
    } catch (error) {
      return 'Ugyldig dato';
    }
  };

  const getChangeTypeIcon = (changeType: string) => {
    switch (changeType) {
      case 'original':
        return (
          <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      case 'description':
        return (
          <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
        );
      case 'image':
        return (
          <svg className="w-4 h-4 text-jobblogg-highlight" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        );
      case 'both':
        return (
          <svg className="w-4 h-4 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  const getChangeTypeColor = (changeType: string) => {
    switch (changeType) {
      case 'original':
        return 'bg-jobblogg-primary-soft text-jobblogg-primary';
      case 'description':
        return 'bg-jobblogg-accent-soft text-jobblogg-accent';
      case 'image':
        return 'bg-jobblogg-highlight-soft text-jobblogg-highlight';
      case 'both':
        return 'bg-jobblogg-warning-soft text-jobblogg-warning';
      default:
        return 'bg-jobblogg-neutral text-jobblogg-text-muted';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-jobblogg-border">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
              <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <TextStrong as="h2" className="text-xl">📜 Redigeringshistorikk</TextStrong>
              <TextMuted className="text-sm">Kronologisk oversikt over alle endringer</TextMuted>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-jobblogg-neutral rounded-full transition-colors duration-200"
            title="Lukk"
          >
            <svg className="w-5 h-5 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {editHistory === undefined ? (
            /* Loading State */
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="animate-pulse">
                  <div className="flex items-start gap-4 p-4 bg-jobblogg-neutral rounded-lg">
                    <div className="w-10 h-10 bg-jobblogg-border rounded-full"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-jobblogg-border rounded w-1/4"></div>
                      <div className="h-3 bg-jobblogg-border rounded w-1/2"></div>
                      <div className="h-16 bg-jobblogg-border rounded"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : editHistory.length === 0 ? (
            /* Empty State */
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-jobblogg-neutral rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <TextMedium className="text-jobblogg-text-muted">
                Ingen redigeringshistorikk tilgjengelig
              </TextMedium>
            </div>
          ) : (
            /* History Timeline */
            <div className="space-y-6">
              {editHistory.map((version, index) => (
                <div key={version.version} className="relative">
                  {/* Timeline Line */}
                  {index < editHistory.length - 1 && (
                    <div className="absolute left-5 top-12 w-0.5 h-full bg-jobblogg-border"></div>
                  )}

                  <div className="flex items-start gap-4">
                    {/* Version Badge */}
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${getChangeTypeColor(version.changeType)}`}>
                      {getChangeTypeIcon(version.changeType)}
                    </div>

                    {/* Version Content */}
                    <div className="flex-1 bg-jobblogg-neutral rounded-lg p-4">
                      {/* Version Header */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <TextStrong className="text-sm">
                            Versjon {version.version}
                            {version.changeType === 'original' && ' (Opprinnelig)'}
                          </TextStrong>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getChangeTypeColor(version.changeType)}`}>
                            {version.changeSummary}
                          </span>
                        </div>
                        <TextMuted className="text-xs">
                          {formatNorwegianDateTime(version.editedAt)}
                        </TextMuted>
                      </div>

                      {/* Version Content */}
                      <div className="space-y-3">
                        {/* Description */}
                        <div>
                          <TextMuted className="text-xs font-medium mb-1">Beskrivelse:</TextMuted>
                          <TextMedium className="text-sm leading-relaxed">
                            {version.description || (
                              <span className="text-jobblogg-text-muted italic">Ingen beskrivelse</span>
                            )}
                          </TextMedium>
                        </div>

                        {/* Image */}
                        {version.imageUrl && (
                          <div>
                            <TextMuted className="text-xs font-medium mb-2">Bilde:</TextMuted>
                            <img
                              src={version.imageUrl}
                              alt={`Versjon ${version.version}`}
                              className="w-full max-w-sm h-32 object-cover rounded-lg border border-jobblogg-border"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-jobblogg-border">
          <PrimaryButton onClick={onClose}>
            Lukk
          </PrimaryButton>
        </div>
      </div>
    </div>
  );
};

export default EditHistoryModal;
