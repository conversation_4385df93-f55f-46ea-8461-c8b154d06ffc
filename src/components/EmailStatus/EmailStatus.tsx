import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextMuted, TextStrong } from '../ui';

interface EmailStatusProps {
  projectId: string;
  className?: string;
}

export const EmailStatus: React.FC<EmailStatusProps> = ({ projectId: _projectId, className = '' }) => {
    // const emailTracking = useQuery(
  //   api.emailTracking.getProjectEmailTracking,
  //   projectId ? { projectId: projectId as any } : "skip"
  // );
  const emailTracking = undefined;
  if (!emailTracking || !Array.isArray(emailTracking) || (emailTracking as any)?.length === 0) {
    return null;
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return '📤';
      case 'delivered':
        return '✅';
      case 'bounced':
        return '⚠️';
      case 'failed':
        return '❌';
      case 'pending':
        return '⏳';
      default:
        return '📧';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'sent':
        return 'Sendt';
      case 'delivered':
        return 'Levert';
      case 'bounced':
        return 'Returnert';
      case 'failed':
        return 'Feilet';
      case 'pending':
        return 'Venter';
      default:
        return 'Ukjent';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'text-jobblogg-primary';
      case 'delivered':
        return 'text-jobblogg-success';
      case 'bounced':
        return 'text-jobblogg-warning';
      case 'failed':
        return 'text-jobblogg-error';
      case 'pending':
        return 'text-jobblogg-text-muted';
      default:
        return 'text-jobblogg-text-medium';
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center gap-2">
        <span className="text-sm">📧</span>
        <TextStrong className="text-sm">E-poststatus</TextStrong>
      </div>

      <div className="space-y-2">
        {/* TODO: Re-enable when emailTracking is available */}
        {/* {emailTracking.map((email) => ( */}
        {(emailTracking as any)?.map?.((email: any) => (
          <div
            key={email.id}
            className="flex items-center justify-between p-3 bg-jobblogg-card-bg border border-jobblogg-border rounded-lg"
          >
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-sm">{getStatusIcon(email.status)}</span>
                <TextStrong className="text-sm">{email.recipientEmail}</TextStrong>
                <span className={`text-xs font-medium ${getStatusColor(email.status)}`}>
                  {getStatusText(email.status)}
                </span>
              </div>

              <TextMuted className="text-xs">
                Sendt: {formatDate(email.sentAt)}
                {email.deliveredAt && (
                  <span className="ml-2">
                    • Levert: {formatDate(email.deliveredAt)}
                  </span>
                )}
                {email.bouncedAt && (
                  <span className="ml-2">
                    • Returnert: {formatDate(email.bouncedAt)}
                  </span>
                )}
              </TextMuted>

              {email.errorMessage && (
                <TextMuted className="text-xs text-jobblogg-error mt-1">
                  Feil: {email.errorMessage}
                </TextMuted>
              )}

              {email.bounceReason && (
                <TextMuted className="text-xs text-jobblogg-warning mt-1">
                  Årsak: {email.bounceReason}
                </TextMuted>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default EmailStatus;
