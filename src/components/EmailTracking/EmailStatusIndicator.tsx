import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
// import { TextMuted } from '../ui'; // TODO: Use for muted text styling

interface EmailStatusIndicatorProps {
  projectId: string;
  className?: string;
}

export const EmailStatusIndicator: React.FC<EmailStatusIndicatorProps> = ({
  projectId,
  className = ''
}) => {
  // Don't query if projectId is missing or invalid
  const emailTracking = useQuery(
    api.emailTracking.getProjectEmailTracking,
    projectId ? { projectId: projectId as any } : "skip"
  );
  if (!emailTracking || emailTracking.length === 0) {
    return null;
  }

  // Get the most recent customer notification email
  const customerEmails = emailTracking.filter((email: any) =>
    email.subject?.includes('Nytt prosjekt') ||
    email.recipientEmail?.includes('@')
  );

  if (customerEmails.length === 0) {
    return null;
  }

  const latestEmail = customerEmails.sort((a: any, b: any) => b.sentAt - a.sentAt)[0];

  const getStatusIcon = (status: string, projectAccessedAt?: number) => {
    if (projectAccessedAt) return '👁️';  // Project accessed by customer
    switch (status) {
      case 'delivered':
        return '✅';
      case 'sent':
        return '📤';
      case 'failed':
        return '❌';
      case 'bounced':
        return '⚠️';
      default:
        return '📧';
    }
  };

  const getStatusText = (status: string, projectAccessedAt?: number) => {
    if (projectAccessedAt) return 'Kunde har besøkt prosjekt';  // Project accessed
    switch (status) {
      case 'delivered':
        return 'E-post levert';
      case 'sent':
        return 'E-post sendt';
      case 'failed':
        return 'E-post feilet';
      case 'bounced':
        return 'E-post returnert';
      default:
        return 'E-post status ukjent';
    }
  };

  const getStatusBadgeClasses = (status: string, projectAccessedAt?: number) => {
    if (projectAccessedAt) return 'bg-jobblogg-accent text-white';  // Project accessed - blue accent
    switch (status) {
      case 'delivered':
        return 'bg-jobblogg-success text-white';
      case 'sent':
        return 'bg-jobblogg-primary text-white';
      case 'failed':
        return 'bg-jobblogg-error text-white';
      case 'bounced':
        return 'bg-jobblogg-warning text-white';
      default:
        return 'bg-jobblogg-text-muted text-white';
    }
  };

  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClasses(latestEmail.status, (latestEmail as any).projectAccessedAt)} ${className}`}>
      <span>
        {getStatusIcon(latestEmail.status, (latestEmail as any).projectAccessedAt)}
      </span>
      <span>
        {getStatusText(latestEmail.status, (latestEmail as any).projectAccessedAt)}
      </span>
    </div>
  );
};

export default EmailStatusIndicator;
