import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TextStrong, TextMuted } from '../ui';

interface EmailTrackingDashboardProps {
  projectId: string;
  className?: string;
  showTitle?: boolean;
  compact?: boolean;
}

interface EmailTrackingRecord {
  id: string;
  recipientEmail: string;
  recipientName?: string;
  subject: string;
  status: 'pending' | 'sent' | 'delivered' | 'bounced' | 'failed' | 'complained';
  sentAt: number;
  deliveredAt?: number;
  bouncedAt?: number;
  projectAccessedAt?: number;
  projectAccessCount?: number;
  // Legacy fields (deprecated)
  openedAt?: number;
  clickedAt?: number;
  openCount?: number;
  clickCount?: number;
  errorMessage?: string;
  bounceReason?: string;
}

export const EmailTrackingDashboard: React.FC<EmailTrackingDashboardProps> = ({
  projectId: _projectId,
  className = '',
  showTitle = true,
  compact = false
}) => {
  // Temporarily disable email tracking functionality
  const emailTracking: any[] = [];
  // const emailTracking = useQuery(
  //   api.emailTracking.getProjectEmailTracking,
  //   projectId ? { projectId: projectId as any } : "skip"
  // );

  if (!emailTracking || emailTracking.length === 0) {
    return null;
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return '📤';
      case 'delivered':
        return '✅';
      case 'bounced':
        return '⚠️';
      case 'failed':
        return '❌';
      case 'pending':
        return '⏳';
      default:
        return '📧';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'sent':
        return 'Sendt';
      case 'delivered':
        return 'Levert';
      case 'bounced':
        return 'Returnert';
      case 'failed':
        return 'Feilet';
      case 'pending':
        return 'Venter';
      default:
        return 'Ukjent';
    }
  };

  const getStatusBadgeClasses = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-jobblogg-primary text-white';
      case 'delivered':
        return 'bg-jobblogg-success text-white';
      case 'bounced':
        return 'bg-jobblogg-warning text-white';
      case 'failed':
        return 'bg-jobblogg-error text-white';
      case 'pending':
        return 'bg-jobblogg-text-muted text-white';
      default:
        return 'bg-jobblogg-text-medium text-white';
    }
  };

  const getEngagementIcon = (email: EmailTrackingRecord) => {
    if ((email as any).projectAccessedAt) return '👁️'; // Project accessed
    return null;
  };

  const getEngagementText = (email: EmailTrackingRecord) => {
    if ((email as any).projectAccessedAt) return 'Kunde har besøkt prosjekt';
    return null;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatRelativeTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Akkurat nå';
    if (minutes < 60) return `${minutes} min siden`;
    if (hours < 24) return `${hours} timer siden`;
    if (days < 7) return `${days} dager siden`;
    return formatDate(timestamp);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {showTitle && (
        <div className="flex items-center gap-2">
          <span className="text-sm">📧</span>
          <TextStrong className="text-sm">Kundevarsling</TextStrong>
        </div>
      )}

      <div className="space-y-2">
        {emailTracking.map((email) => (
          <div
            key={email.id}
            className={`${
              compact
                ? 'p-3 bg-jobblogg-card-bg border border-jobblogg-border rounded-lg'
                : 'p-4 bg-white border border-jobblogg-border rounded-xl shadow-soft'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                {/* Email header */}
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-sm">{getStatusIcon(email.status)}</span>
                  <TextStrong className="text-sm truncate">
                    {email.recipientName || email.recipientEmail}
                  </TextStrong>
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${getStatusBadgeClasses(email.status)}`}>
                    {getStatusText(email.status)}
                  </span>
                </div>

                {/* Email details */}
                <div className="space-y-1">
                  <TextMuted className="text-xs">
                    <span className="font-medium">Til:</span> {email.recipientEmail}
                  </TextMuted>

                  <TextMuted className="text-xs">
                    <span className="font-medium">Sendt:</span> {formatRelativeTime(email.sentAt)}
                  </TextMuted>

                  {email.deliveredAt && (
                    <TextMuted className="text-xs text-jobblogg-success">
                      <span className="font-medium">Levert:</span> {formatRelativeTime(email.deliveredAt)}
                    </TextMuted>
                  )}

                  {/* Project access tracking */}
                  {(email as any).projectAccessedAt && (
                    <div className="flex items-center gap-2 mt-2">
                      {getEngagementIcon(email) && (
                        <span className="text-xs">{getEngagementIcon(email)}</span>
                      )}
                      <TextMuted className="text-xs text-jobblogg-primary">
                        {getEngagementText(email)}
                        {(email as any).projectAccessedAt && ` (${formatRelativeTime((email as any).projectAccessedAt)})`}
                        {(email as any).projectAccessCount && (email as any).projectAccessCount > 1 && (
                          <span className="ml-1">• {(email as any).projectAccessCount} besøk</span>
                        )}
                      </TextMuted>
                    </div>
                  )}

                  {/* Error information */}
                  {email.errorMessage && (
                    <TextMuted className="text-xs text-jobblogg-error mt-2 p-2 bg-jobblogg-error-soft rounded">
                      <span className="font-medium">Feil:</span> {email.errorMessage}
                    </TextMuted>
                  )}

                  {email.bounceReason && (
                    <TextMuted className="text-xs text-jobblogg-warning mt-2 p-2 bg-jobblogg-warning-soft rounded">
                      <span className="font-medium">Årsak:</span> {email.bounceReason}
                    </TextMuted>
                  )}
                </div>
              </div>

              {/* Status indicator */}
              <div className="flex-shrink-0 ml-3">
                {email.status === 'delivered' && (email as any).projectAccessedAt && (
                  <div className="w-2 h-2 bg-jobblogg-accent rounded-full" title="Kunde har besøkt prosjekt" />
                )}
                {email.status === 'delivered' && !(email as any).projectAccessedAt && (
                  <div className="w-2 h-2 bg-jobblogg-success rounded-full" title="E-post levert" />
                )}
                {email.status === 'failed' && (
                  <div className="w-2 h-2 bg-jobblogg-error rounded-full" title="E-post feilet" />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Summary for multiple emails */}
      {emailTracking.length > 1 && !compact && (
        <div className="mt-4 p-3 bg-jobblogg-background-soft rounded-lg border border-jobblogg-border">
          <TextMuted className="text-xs">
            <span className="font-medium">Totalt:</span> {emailTracking.length} e-poster sendt
            {emailTracking.filter(e => e.status === 'delivered').length > 0 && (
              <span className="ml-2">
                • {emailTracking.filter(e => e.status === 'delivered').length} levert
              </span>
            )}
            {emailTracking.filter(e => (e as any).projectAccessedAt).length > 0 && (
              <span className="ml-2">
                • {emailTracking.filter(e => (e as any).projectAccessedAt).length} prosjekt besøkt
              </span>
            )}
          </TextMuted>
        </div>
      )}
    </div>
  );
};

export default EmailTrackingDashboard;
