import React, { Suspense } from 'react';
import { createLazyComponent, preloadComponent } from '../utils/lazyLoading';

// Enhanced loading component for lazy-loaded pages with micro-interactions
const PageLoadingSpinner: React.FC = () => (
  <div className="min-h-screen bg-white flex items-center justify-center animate-fade-in">
    <div className="text-center animate-bounce-in">
      <div className="relative mb-6">
        <div className="w-12 h-12 border-4 border-jobblogg-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
        <div className="absolute inset-0 w-12 h-12 border-4 border-jobblogg-primary/20 rounded-full animate-pulse-soft mx-auto"></div>
      </div>
      <p className="text-jobblogg-text-medium animate-pulse-soft">Laster inn...</p>
      <div className="flex justify-center gap-1 mt-3">
        <div className="w-2 h-2 bg-jobblogg-primary rounded-full animate-loading-dots"></div>
        <div className="w-2 h-2 bg-jobblogg-primary rounded-full animate-loading-dots" style={{ animationDelay: '0.2s' }}></div>
        <div className="w-2 h-2 bg-jobblogg-primary rounded-full animate-loading-dots" style={{ animationDelay: '0.4s' }}></div>
      </div>
    </div>
  </div>
);

// Lazy-loaded page components
// Import Dashboard directly for offline support in development
import DashboardComponent from '../pages/Dashboard/Dashboard';

export const LazyDashboard = createLazyComponent(
  () => {
    // In development, when offline, return the direct import to avoid network requests
    if (import.meta.env.DEV && !navigator.onLine) {
      return Promise.resolve({ default: DashboardComponent });
    }
    // Normal lazy loading when online or in production
    return import('../pages/Dashboard/Dashboard');
  }
);

export const LazyCreateProject = createLazyComponent(
  () => import('../pages/CreateProject/CreateProject')
);

// Import ProjectDetail directly for offline support in development
import ProjectDetailComponent from '../pages/ProjectDetail/ProjectDetail';

export const LazyProjectDetail = createLazyComponent(
  () => {
    // In development, when offline, return the direct import to avoid network requests
    if (import.meta.env.DEV && !navigator.onLine) {
      return Promise.resolve({ default: ProjectDetailComponent });
    }
    // Normal lazy loading when online or in production
    return import('../pages/ProjectDetail/ProjectDetail');
  }
);

export const LazyProjectEdit = createLazyComponent(
  () => import('../pages/ProjectEdit/ProjectEdit')
);

// Import ProjectLog directly for offline support in development
import ProjectLogComponent from '../pages/ProjectLog/ProjectLog';

export const LazyProjectLog = createLazyComponent(
  () => {
    // In development, when offline, return the direct import to avoid network requests
    if (import.meta.env.DEV && !navigator.onLine) {
      return Promise.resolve({ default: ProjectLogComponent });
    }
    // Normal lazy loading when online or in production
    return import('../pages/ProjectLog/ProjectLog');
  }
);

export const LazySignIn = createLazyComponent(
  () => import('../pages/SignIn/SignIn')
);

export const LazySignUp = createLazyComponent(
  () => import('../pages/SignUp/SignUp')
);

export const LazySharedProject = createLazyComponent(
  () => import('../pages/SharedProject/SharedProject')
);

export const LazyContractorOnboarding = createLazyComponent(
  () => import('../pages/ContractorOnboarding/ContractorOnboardingWizard')
);

export const LazyConversations = createLazyComponent(
  () => import('../pages/Conversations/Conversations')
);

export const LazyArchivedProjects = createLazyComponent(
  () => import('../pages/ArchivedProjects')
);

export const LazyGoogleMapsTest = createLazyComponent(
  () => import('../pages/GoogleMapsTest')
);

export const LazyStripeTest = createLazyComponent(
  () => import('../debug/StripeTest')
);

// Checkout Components
export const LazyCheckoutSuccess = createLazyComponent(
  () => import('../pages/CheckoutSuccess')
);

export const LazyCheckoutCancel = createLazyComponent(
  () => import('../pages/CheckoutCancel')
);

export const LazyReactivationSuccess = createLazyComponent(
  () => import('../pages/ReactivationSuccess')
);

// Test Components
export const LazyCheckoutSuccessTest = createLazyComponent(
  () => import('../debug/CheckoutSuccessTest')
);

export const LazyCheckoutCancelTest = createLazyComponent(
  () => import('../debug/CheckoutCancelTest')
);

export const LazyWebhookMonitor = createLazyComponent(
  () => import('../debug/WebhookMonitor')
);

export const LazyTeamManagement = createLazyComponent(
  () => import('../pages/TeamManagement/TeamManagement')
);

export const LazyTeamProjects = createLazyComponent(
  () => import('../pages/TeamProjects/TeamProjects')
);

export const LazyTeamOnboarding = createLazyComponent(
  () => import('../pages/TeamOnboarding/TeamOnboarding')
);

export const LazyAcceptInvite = createLazyComponent(
  () => import('../pages/AcceptInvite/AcceptInvite')
);

export const LazyBlockedUser = createLazyComponent(
  () => import('../pages/BlockedUser/BlockedUser')
);

export const LazyInvitationsList = createLazyComponent(
  () => import('../pages/Invitations/InvitationsList')
);

export const LazyInvitationDetail = createLazyComponent(
  () => import('../pages/Invitations/InvitationDetail')
);

export const LazyNotifications = createLazyComponent(
  () => import('../pages/Notifications/Notifications').then(module => ({ default: module.Notifications }))
);

export const LazySubscriptionManagement = createLazyComponent(
  () => import('../pages/Subscription/SubscriptionManagement')
);

// Privacy and legal pages
export const LazyPrivacyPolicy = createLazyComponent(
  () => import('../pages/Privacy/PrivacyPolicy')
);

export const LazyCookiePolicy = createLazyComponent(
  () => import('../pages/Privacy/CookiePolicy')
);

export const LazyTermsOfService = createLazyComponent(
  () => import('../pages/Privacy/TermsOfService')
);

export const LazyPrivacySettings = createLazyComponent(
  () => import('../pages/Privacy/PrivacySettings')
);

// Help page
export const LazyHelpPage = createLazyComponent(
  () => import('../pages/Help/HelpPage')
);

// Landing page
export const LazyLandingPage = createLazyComponent(
  () => import('../pages/Landing/LandingPage')
);

// Pricing page
export const LazyPricingPage = createLazyComponent(
  () => import('../pages/Pricing/PricingPage')
);




// Wrapper component with Suspense
interface LazyPageWrapperProps {
  children: React.ReactNode;
  fallback?: React.ComponentType;
}

export const LazyPageWrapper: React.FC<LazyPageWrapperProps> = ({
  children,
  fallback: Fallback = PageLoadingSpinner
}) => (
  <Suspense fallback={<Fallback />}>
    {children}
  </Suspense>
);



// Image optimization component with lazy loading
interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className = '',
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzlDQTNBRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxhc3RlciBiaWxkZS4uLjwvdGV4dD48L3N2Zz4=',
  sizes,
  loading = 'lazy',
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Placeholder */}
      {!isLoaded && !hasError && (
        <img
          src={placeholder}
          alt=""
          className="absolute inset-0 w-full h-full object-cover blur-sm"
          aria-hidden="true"
        />
      )}

      {/* Main image */}
      <img
        src={src}
        alt={alt}
        className={`
          w-full h-full object-cover transition-opacity duration-300
          ${isLoaded ? 'opacity-100' : 'opacity-0'}
          ${hasError ? 'hidden' : ''}
        `}
        sizes={sizes}
        loading={loading}
        onLoad={handleLoad}
        onError={handleError}
      />

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 bg-jobblogg-neutral flex items-center justify-center">
          <div className="text-center text-jobblogg-text-muted">
            <svg className="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-sm">Kunne ikke laste bilde</p>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
};

// Virtual scrolling component for large lists
interface VirtualScrollProps {
  items: any[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: any, index: number) => React.ReactNode;
  className?: string;
}

export const VirtualScroll: React.FC<VirtualScrollProps> = ({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className = ''
}) => {
  const [scrollTop, setScrollTop] = React.useState(0);

  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );

  const visibleItems = items.slice(visibleStart, visibleEnd);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  };

  return (
    <div
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${visibleStart * itemHeight}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map((item, index) => (
            <div key={visibleStart + index} style={{ height: itemHeight }}>
              {renderItem(item, visibleStart + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// Preload critical routes for better performance
export const preloadCriticalRoutes = () => {
  // Preload the most commonly accessed routes
  preloadComponent(() => import('../pages/Dashboard/Dashboard'));
  preloadComponent(() => import('../pages/CreateProject/CreateProject'));
  preloadComponent(() => import('../pages/ProjectDetail/ProjectDetail'));
};
