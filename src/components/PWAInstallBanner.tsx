import React, { useState } from 'react';
import { useP<PERSON>, PWAUtils } from '../hooks/usePWA';
// import { useOfflineProjects } from '../hooks/useOfflineProjects'; // TODO: Re-enable when offline projects indicator is implemented
import { PrimaryButton } from './ui';
import { PWAValueProposition, PWAInstallInstructions } from './pwa/PWAValueProposition';

interface PWAInstallBannerProps {
  className?: string;
  /** Whether to show as milestone prompt */
  isMilestonePrompt?: boolean;
  /** Milestone type if this is a milestone prompt */
  milestoneType?: 'firstProjectCreated' | 'firstProjectCompleted' | 'tenthProjectCreated' | 'firstWeekActive' | 'firstMonthActive';
}

export const PWAInstallBanner: React.FC<PWAInstallBannerProps> = ({
  className = '',
  isMilestonePrompt = false,
  milestoneType
}) => {
  const {
    isInstallable,
    isInstalled,
    shouldShowInstallPrompt,
    installApp,
    dismissInstallPrompt,
    recordMilestone,
    checkMilestonePrompt,
    requestNotificationPermission,
    notificationPermission,
    isPushSupported,
    getInstallStatus
  } = usePWA();
  // const offlineProjectsData = useOfflineProjects(); // TODO: Implement offline projects indicator
  const [isInstalling, setIsInstalling] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Enhanced visibility logic using preferences system
  const shouldShow = () => {
    // Never show if PWA is already installed
    if (isInstalled) return false;

    // Never show if not installable
    if (!isInstallable) return false;

    // For milestone prompts, check milestone-specific logic
    if (isMilestonePrompt && milestoneType) {
      return checkMilestonePrompt(milestoneType);
    }

    // For regular prompts, use preferences system
    return shouldShowInstallPrompt;
  };

  // Don't show banner if conditions aren't met
  if (!shouldShow()) {
    return null;
  }

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      const success = await installApp();
      if (success) {
        // After successful installation, request notification permission
        if (isPushSupported && notificationPermission === 'default') {
          try {
            await requestNotificationPermission();
          } catch (notificationError) {
            console.warn('Failed to request notification permission:', notificationError);
            // Don't fail the installation if notification permission fails
          }
        }

        // If this was a milestone prompt, record the milestone
        if (isMilestonePrompt && milestoneType) {
          recordMilestone(milestoneType);
        }
      }
    } catch (error) {
      console.error('Install failed:', error instanceof Error ? error.message : String(error));
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = (isPermanent: boolean = false) => {
    dismissInstallPrompt(isPermanent);

    // If this was a milestone prompt, record that we showed it
    if (isMilestonePrompt && milestoneType) {
      recordMilestone(milestoneType);
    }
  };

  const handleTemporaryDismiss = () => handleDismiss(false);
  const handlePermanentDismiss = () => handleDismiss(true);

  const installStatus = getInstallStatus();
  const milestoneMessages = {
    firstProjectCreated: '🎉 Gratulerer med ditt første prosjekt! Installer appen for enda bedre opplevelse.',
    firstProjectCompleted: '✅ Flott jobbet med prosjektet! Installer appen for å holde deg oppdatert på fremtidige prosjekter.',
    tenthProjectCreated: '🚀 Du har opprettet 10 prosjekter! Installer appen for optimal produktivitet.',
    firstWeekActive: '📅 Du har brukt JobbLogg i en uke! Installer appen for raskere tilgang.',
    firstMonthActive: '🏆 En måned med JobbLogg! Installer appen for den beste opplevelsen.'
  };

  return (
    <div className={`
      fixed bottom-4 left-4 right-4 z-50
      bg-white border border-jobblogg-primary/20 rounded-xl shadow-lg
      p-4 animate-slide-up
      md:left-auto md:right-4 md:max-w-md
      ${className}
    `}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-start gap-3">
          {/* App Icon */}
          <div className="flex-shrink-0 w-12 h-12 bg-jobblogg-primary rounded-xl flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-jobblogg-text-strong text-sm mb-1">
              {isMilestonePrompt && milestoneType ? 'Installer JobbLogg-appen' : 'Installer JobbLogg'}
            </h3>
            <p className="text-jobblogg-text-medium text-xs mb-2 leading-relaxed">
              {isMilestonePrompt && milestoneType && milestoneMessages[milestoneType]
                ? milestoneMessages[milestoneType]
                : 'Få pushvarslinger direkte på telefonen og rask tilgang fra hjemskjermen.'
              }
            </p>
          </div>

          {/* Close Button */}
          <button
            onClick={handleTemporaryDismiss}
            className="flex-shrink-0 w-6 h-6 text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors rounded-full hover:bg-jobblogg-neutral flex items-center justify-center"
            aria-label="Lukk installasjonsbanner"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Value Proposition */}
        {!showDetails ? (
          <PWAValueProposition
            variant="compact"
            showIndustryContext={false}
            className="px-1"
          />
        ) : (
          <PWAValueProposition
            variant="banner"
            showIndustryContext={true}
            className="px-1"
          />
        )}

        {/* Toggle Details Button */}
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="w-full text-xs text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors py-1"
        >
          {showDetails ? '▲ Vis mindre' : '▼ Vis mer om fordelene'}
        </button>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2">
          <PrimaryButton
            onClick={handleInstall}
            loading={isInstalling}
            size="sm"
            className="w-full text-sm px-4 py-2 min-h-[36px]"
          >
            {isInstalling ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Installerer...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Installer nå
              </>
            )}
          </PrimaryButton>

          <div className="flex gap-2">
            <button
              onClick={handleTemporaryDismiss}
              className="flex-1 text-jobblogg-text-medium hover:text-jobblogg-text-strong text-sm px-3 py-2 rounded-lg hover:bg-jobblogg-neutral transition-colors min-h-[36px]"
            >
              Ikke nå
            </button>
            <button
              onClick={handlePermanentDismiss}
              className="flex-1 text-jobblogg-text-muted hover:text-jobblogg-text-medium text-sm px-3 py-2 rounded-lg hover:bg-jobblogg-neutral transition-colors min-h-[36px]"
            >
              Ikke vis igjen
            </button>
          </div>
        </div>

        {/* Install Instructions (for browsers that don't support beforeinstallprompt) */}
        {showDetails && (
          <PWAInstallInstructions className="mt-2" />
        )}

        {/* Debug Info (development only) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-3 pt-3 border-t border-jobblogg-border">
            <details className="text-xs text-jobblogg-text-muted">
              <summary className="cursor-pointer hover:text-jobblogg-text-medium">
                Debug Info
              </summary>
              <div className="mt-2 space-y-1">
                <div>Status: {installStatus.reason}</div>
                <div>Dismissals: {installStatus.dismissalCount}</div>
                {installStatus.nextPromptDate && (
                  <div>Next prompt: {installStatus.nextPromptDate.toLocaleDateString('nb-NO')}</div>
                )}
                {isMilestonePrompt && (
                  <div>Milestone: {milestoneType}</div>
                )}
              </div>
            </details>
          </div>
        )}
      </div>
    </div>
  );
};

// PWA Status Indicator Component
export const PWAStatusIndicator: React.FC = () => {
  const {
    isOnline,
    isUpdateAvailable,
    updateApp,
    notificationPermission,
    isPushSupported,
    requestNotificationPermission
  } = usePWA();
  const [isUpdating, setIsUpdating] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      await updateApp();
    } catch (error) {
      console.error('Update failed:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Determine status and color based on connectivity and notifications
  const getStatusInfo = () => {
    if (isOnline) {
      if (notificationPermission === 'granted') {
        return {
          status: 'Tilkoblet',
          color: 'bg-jobblogg-success-soft text-jobblogg-success border-jobblogg-success/20',
          dotColor: 'bg-jobblogg-success',
          icon: '🔔'
        };
      } else {
        return {
          status: 'Online',
          color: 'bg-jobblogg-primary-soft text-jobblogg-primary border-jobblogg-primary/20',
          dotColor: 'bg-jobblogg-primary',
          icon: '🌐'
        };
      }
    } else {
      return {
        status: 'Offline',
        color: 'bg-jobblogg-warning-soft text-jobblogg-warning border-jobblogg-warning/20',
        dotColor: 'bg-jobblogg-warning',
        icon: '📱'
      };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <div className="fixed top-4 right-4 z-40 flex flex-col gap-2">
      {/* Enhanced Status Indicator */}
      <div
        className={`
          px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-2 transition-all duration-300 cursor-pointer
          ${statusInfo.color} border
        `}
        onClick={() => setShowDetails(!showDetails)}
      >
        <div className={`w-2 h-2 rounded-full ${statusInfo.dotColor}`} />
        <span>{statusInfo.icon} {statusInfo.status}</span>
        {/* TODO: Add offline projects indicator when needed */}
      </div>

      {/* Detailed Status Panel */}
      {showDetails && (
        <div className="bg-white border border-jobblogg-border rounded-xl shadow-lg p-4 min-w-64 animate-slide-down">
          <div className="space-y-3">
            {/* Connection Status */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-jobblogg-text-strong">Tilkobling:</span>
              <span className={`text-xs px-2 py-1 rounded-full ${statusInfo.color}`}>
                {statusInfo.status}
              </span>
            </div>

            {/* Push Notification Status */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-jobblogg-text-strong">Pushvarslinger:</span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                notificationPermission === 'granted'
                  ? 'bg-jobblogg-success-soft text-jobblogg-success'
                  : notificationPermission === 'denied'
                  ? 'bg-jobblogg-error-soft text-jobblogg-error'
                  : 'bg-jobblogg-neutral text-jobblogg-text-muted'
              }`}>
                {notificationPermission === 'granted' ? '🔔 Aktivert' :
                 notificationPermission === 'denied' ? '🔕 Blokkert' :
                 '⏳ Ikke spurt'}
              </span>
            </div>

            {/* Notification Features */}
            {isPushSupported && (
              <>
                <div className="border-t border-jobblogg-border pt-3">
                  <h4 className="text-xs font-medium text-jobblogg-text-strong mb-2">Pushvarslinger for:</h4>
                  <div className="space-y-1 text-xs text-jobblogg-text-medium">
                    <div className="flex items-center gap-2">
                      <span>💬</span>
                      <span>Nye chatmeldinger</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span>📋</span>
                      <span>Prosjektoppdateringer</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span>👥</span>
                      <span>Teamsamarbeid</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span>📸</span>
                      <span>Nye bilder og dokumenter</span>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Enable Notifications */}
            {isPushSupported && notificationPermission !== 'granted' && (
              <div className="border-t border-jobblogg-border pt-3">
                <button
                  onClick={requestNotificationPermission}
                  className="w-full text-xs text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors font-medium"
                >
                  🔔 Aktiver pushvarslinger
                </button>
              </div>
            )}

            {/* Quick Actions */}
            {isOnline && (
              <div className="border-t border-jobblogg-border pt-3">
                <button
                  onClick={() => window.location.reload()}
                  className="w-full text-xs text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors"
                >
                  Oppdater app
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Update Available */}
      {isUpdateAvailable && (
        <div className="bg-jobblogg-primary-soft border border-jobblogg-primary/20 rounded-lg p-3 max-w-xs animate-slide-down">
          <div className="flex items-start gap-2">
            <div className="w-5 h-5 bg-jobblogg-primary rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-jobblogg-text-strong text-sm font-medium mb-1">
                Oppdatering tilgjengelig
              </p>
              <p className="text-jobblogg-text-medium text-xs mb-2">
                En ny versjon av JobbLogg er klar til installasjon.
              </p>
              <PrimaryButton
                onClick={handleUpdate}
                loading={isUpdating}
                size="sm"
                className="text-xs"
              >
                {isUpdating ? 'Oppdaterer...' : 'Oppdater nå'}
              </PrimaryButton>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// PWA Share Button Component
interface PWAShareButtonProps {
  title: string;
  text: string;
  url?: string;
  className?: string;
  children?: React.ReactNode;
}

export const PWAShareButton: React.FC<PWAShareButtonProps> = ({
  title,
  text,
  url,
  className = '',
  children
}) => {
  const [isSharing, setIsSharing] = useState(false);
  const [shareSuccess, setShareSuccess] = useState(false);

  const handleShare = async () => {
    setIsSharing(true);
    try {
      const success = await PWAUtils.shareContent({ title, text, url });
      if (success) {
        setShareSuccess(true);
        setTimeout(() => setShareSuccess(false), 2000);
      }
    } catch (error) {
      console.error('Share failed:', error);
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <PrimaryButton
      onClick={handleShare}
      loading={isSharing}
      variant="secondary"
      size="sm"
      className={`${shareSuccess ? 'bg-jobblogg-success-soft border-jobblogg-success text-jobblogg-success' : ''} ${className}`}
    >
      {shareSuccess ? (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Delt!
        </>
      ) : (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
          {children || 'Del'}
        </>
      )}
    </PrimaryButton>
  );
};
