import React from 'react';
import { useQuery } from "convex/react";
import { useUser } from '@clerk/clerk-react';
import { api } from "../../../convex/_generated/api";
import { StatsCard } from '../ui';
import { getUserRole } from '../../utils/userRole';

interface ChatStatsCardProps {
  /** Additional CSS classes */
  className?: string;
  /** Animation delay */
  animationDelay?: string;
  /** Click handler for navigation */
  onClick?: () => void;
}

/**
 * Chat statistics card component for dashboard
 * Shows unread message count with real-time updates
 *
 * @example
 * ```tsx
 * <ChatStatsCard
 *   animationDelay="0.2s"
 *   onClick={() => navigate('/conversations')}
 * />
 * ```
 */
export const ChatStatsCard: React.FC<ChatStatsCardProps> = ({
  className = '',
  animationDelay = '0s',
  onClick
}) => {
  const { user } = useUser();
  const { role: userRole } = getUserRole(user, 'contractor-dashboard');

  // Get unread message counts with proper user role determination
  const unreadData = useQuery(
    api.messages.getUnreadCounts,
    user?.id ? {
      userId: user.id,
      userRole: userRole
    } : "skip"
  );

  // Handle loading state
  if (user?.id && unreadData === undefined) {
    return (
      <div className={`animate-pulse ${className}`} style={{ animationDelay }}>
        <div className="bg-white rounded-xl p-6 shadow-lg border border-jobblogg-border">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-6 h-6 bg-jobblogg-border rounded animate-pulse"></div>
            <div className="h-4 bg-jobblogg-border rounded w-24 animate-pulse"></div>
          </div>
          <div className="h-8 bg-jobblogg-border rounded w-16 mb-2 animate-pulse"></div>
          <div className="h-3 bg-jobblogg-border rounded w-32 animate-pulse"></div>
        </div>
      </div>
    );
  }

  // Format subtitle based on unread data
  const getSubtitle = () => {
    if (!unreadData || unreadData.totalUnread === 0) {
      return "Alle meldinger lest";
    }

    const conversationCount = unreadData.conversationCounts?.length || 0;
    if (conversationCount === 1) {
      return "1 samtale med nye meldinger";
    } else {
      return `${conversationCount} samtaler med nye meldinger`;
    }
  };

  // Determine if there are unread messages
  const hasUnreadMessages = unreadData && unreadData.totalUnread > 0;

  return (
    <StatsCard
      title="Uleste meldinger"
      value={unreadData?.totalUnread || 0}
      subtitle={getSubtitle()}
      variant={hasUnreadMessages ? "warning" : "neutral"}
      animationDelay={animationDelay}
      onClick={onClick}
      className={className}
      icon={
        <svg
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          className={hasUnreadMessages ? "animate-pulse" : ""}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
      }
    />
  );
};
