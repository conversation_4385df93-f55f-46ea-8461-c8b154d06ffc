import React, { useState, useCallback, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Id } from '../../../convex/_generated/dataModel';
// import { TextMuted } from '../ui'; // TODO: Use for muted text styling
import { MobileEmojiBottomSheet } from './MobileEmojiBottomSheet';

// Enhanced emoji palette with prioritized quick reactions
const QUICK_REACTIONS = [
  { emoji: '👍', label: 'Liker', priority: 1 },
  { emoji: '❤️', label: 'Elsker', priority: 2 },
  { emoji: '😂', label: 'Morsom', priority: 3 },
  { emoji: '😮', label: 'Overrasket', priority: 4 },
  { emoji: '😢', label: 'Trist', priority: 5 },
  { emoji: '😡', label: 'Sint', priority: 6 },
  { emoji: '🎉', label: 'Feirer', priority: 7 },
  { emoji: '🔥', label: 'Fantastisk', priority: 8 }
];

interface ReactionData {
  emoji: string;
  userIds: string[];
  count: number;
}

interface EnhancedEmojiReactionsProps {
  /** Array of reactions for this message */
  reactions: ReactionData[];
  /** Message ID for the reaction */
  messageId: Id<"messages">;
  /** Current user ID */
  userId: string;
  /** Callback when user reacts */
  onReaction: (messageId: Id<"messages">, emoji: string) => Promise<void>;
  /** Additional CSS classes */
  className?: string;
  /** User names for tooltip display */
  userNames?: Record<string, string>;
}

/**
 * Enhanced emoji reactions component with "Liker" button and advanced palette
 * 
 * Features:
 * - Norwegian "Liker" button instead of "+" 
 * - Long-press/hover opens horizontal emoji palette
 * - Aggregated reaction display (top 3 emojis + count)
 * - Responsive design with mobile bottom-sheet
 * - Accessibility support with ARIA labels
 * - Performance optimized with GPU acceleration
 */
export const EnhancedEmojiReactions: React.FC<EnhancedEmojiReactionsProps> = ({
  reactions,
  messageId,
  userId,
  onReaction,
  className = '',
  userNames = {}
}) => {
  const [showPalette, setShowPalette] = useState(false);
  const [_selectedEmoji, setSelectedEmoji] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [draggedEmoji, setDraggedEmoji] = useState<string | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [palettePosition, setPalettePosition] = useState<{ top: number; left: number; arrow: 'top' | 'bottom' | 'left' | 'right' }>({ top: 0, left: 0, arrow: 'bottom' });
  const [messageHighlighted, setMessageHighlighted] = useState(false);
  const [isLongPressing, setIsLongPressing] = useState(false);
  const [clickStartTime, setClickStartTime] = useState<number | null>(null);
  
  const paletteRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);
  const touchStartPos = useRef<{ x: number; y: number } | null>(null);

  // Check if user has reacted (enforced: maximum one reaction per user per message)
  const userReactions = reactions.filter(r => r.userIds.includes(userId));
  const hasReacted = userReactions.length > 0;
  const userReaction = userReactions[0]; // Should only be one reaction per user

  // Get top 3 most used reactions for aggregated display
  const topReactions = reactions
    .sort((a, b) => b.count - a.count)
    .slice(0, 3);

  // Total reaction count
  const totalCount = reactions.reduce((sum, r) => sum + r.count, 0);

  // Detect mobile viewport and motion preferences
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 480);
    };

    const checkMotionPreference = () => {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      setPrefersReducedMotion(mediaQuery.matches);
    };

    checkMobile();
    checkMotionPreference();

    window.addEventListener('resize', checkMobile);

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    mediaQuery.addEventListener('change', checkMotionPreference);

    return () => {
      window.removeEventListener('resize', checkMobile);
      mediaQuery.removeEventListener('change', checkMotionPreference);
    };
  }, []);

  // Handle reaction selection
  const handleReaction = useCallback(async (emoji: string) => {
    if (isLoading) return;



    setIsLoading(true);
    setSelectedEmoji(emoji);

    try {
      await onReaction(messageId, emoji);



      // Haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
    } catch (error) {
      console.error('❌ EnhancedEmojiReactions: Failed to react:', error);
    } finally {
      setIsLoading(false);
      setSelectedEmoji(null);
      setShowPalette(false);
      setMessageHighlighted(false);
    }
  }, [messageId, onReaction, isLoading, userId]);

  // Calculate optimal palette position
  const calculatePalettePosition = useCallback(() => {
    if (!buttonRef.current) return;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    // const _viewportHeight = window.innerHeight; // TODO: Use for viewport calculations
    const paletteWidth = 560; // Desktop palette width (8 emojis × 44px + padding + gaps)
    const paletteHeight = 60; // Approximate palette height

    let top = buttonRect.top - paletteHeight - 8; // 8px gap
    let left = buttonRect.left + (buttonRect.width / 2) - (paletteWidth / 2);
    let arrow: 'top' | 'bottom' | 'left' | 'right' = 'bottom';

    // Adjust for viewport boundaries
    if (top < 10) {
      // Not enough space above, show below
      top = buttonRect.bottom + 8;
      arrow = 'top';
    }

    if (left < 10) {
      left = 10;
    } else if (left + paletteWidth > viewportWidth - 10) {
      left = viewportWidth - paletteWidth - 10;
    }

    setPalettePosition({ top, left, arrow });
  }, []);

  // Long press handlers
  const handleLongPressStart = useCallback((e: React.TouchEvent | React.MouseEvent) => {
    e.preventDefault();

    const startTime = Date.now();
    setClickStartTime(startTime);
    setIsLongPressing(false);

    if ('touches' in e) {
      touchStartPos.current = { x: e.touches[0].clientX, y: e.touches[0].clientY };
    }

    // Clear any existing timer
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
    }

    longPressTimer.current = setTimeout(() => {
      setIsLongPressing(true);
      setShowPalette(true);
      setMessageHighlighted(true);

      // Calculate position for desktop
      if (!isMobile) {
        calculatePalettePosition();
      }

      // Haptic feedback for long press
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
    }, 450);
  }, [isMobile, calculatePalettePosition]);

  // Handle click - distinguish between single click and long press
  const handleLikerClick = useCallback((e: React.MouseEvent) => {
    // Prevent default click behavior if this was a long press
    if (isLongPressing) {
      e.preventDefault();
      return;
    }

    // Check if this was a quick click (not a long press)
    const clickDuration = clickStartTime ? Date.now() - clickStartTime : 0;
    if (clickDuration >= 400) {
      // This was likely a long press, don't trigger single click
      return;
    }

    // Single click behavior - trigger default reaction (👍)
    handleReaction('👍');
  }, [isLongPressing, clickStartTime, handleReaction]);

  const handleLongPressEnd = useCallback(() => {
    // Clear the timer
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    // Reset long press state after a short delay to prevent click interference
    setTimeout(() => {
      setIsLongPressing(false);
      setClickStartTime(null);
    }, 100);

    // If dragged emoji exists, select it
    if (draggedEmoji) {
      handleReaction(draggedEmoji);
      setDraggedEmoji(null);
    }
  }, [draggedEmoji, handleReaction]);

  // Mobile touch click handler
  const handleMobileTouch = useCallback(() => {
    // For mobile, always show the bottom sheet on touch
    setShowPalette(true);
    setMessageHighlighted(true);

    // Haptic feedback
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  }, []);

  // Touch move handler for drag-to-select
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!showPalette || !touchStartPos.current) return;

    const touch = e.touches[0];
    const deltaX = Math.abs(touch.clientX - touchStartPos.current.x);
    const deltaY = Math.abs(touch.clientY - touchStartPos.current.y);

    // Cancel if moved too far
    if (deltaX > 150 || deltaY > 150) {
      setShowPalette(false);
      setDraggedEmoji(null);
      handleLongPressEnd();
      return;
    }

    // Find emoji under finger
    const element = document.elementFromPoint(touch.clientX, touch.clientY);
    const emojiButton = element?.closest('[data-emoji]');
    const emoji = emojiButton?.getAttribute('data-emoji');

    setDraggedEmoji(emoji || null);
  }, [showPalette, handleLongPressEnd]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
        longPressTimer.current = null;
      }
    };
  }, []);

  // Close palette when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (paletteRef.current && !paletteRef.current.contains(event.target as Node) &&
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setShowPalette(false);
        setMessageHighlighted(false);
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowPalette(false);
        setMessageHighlighted(false);
      }
    };

    if (showPalette) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        document.removeEventListener('keydown', handleEscape);
      };
    }
  }, [showPalette]);

  // Generate tooltip text for reaction counts with proper "Du" handling
  const getTooltipText = useCallback((reaction: ReactionData) => {
    const userHasReacted = reaction.userIds.includes(userId);

    // Get other users (excluding current user)
    const otherUserIds = reaction.userIds.filter(id => id !== userId);
    const otherNames = otherUserIds
      .map(id => userNames?.[id] || 'Kunde')
      .slice(0, 2); // Show max 2 other names

    const totalOthers = otherUserIds.length;
    const remainingOthers = Math.max(0, totalOthers - otherNames.length);

    // Build the name list starting with "Du" if user reacted
    const nameList: string[] = [];

    if (userHasReacted) {
      nameList.push('Du');
    }

    // Add other names
    nameList.push(...otherNames);

    // Handle remaining count
    if (remainingOthers > 0) {
      nameList.push(`${remainingOthers} ${remainingOthers === 1 ? 'annen' : 'andre'}`);
    }

    // Format the final text
    if (nameList.length === 0) return '';
    if (nameList.length === 1) {
      return `${nameList[0]} reagerte med ${reaction.emoji}`;
    }
    if (nameList.length === 2) {
      return `${nameList[0]} og ${nameList[1]} reagerte med ${reaction.emoji}`;
    }

    // 3 or more: "Du, Anna og 2 andre reagerte med 👍"
    const lastItem = nameList.pop();
    const firstItems = nameList.join(', ');
    return `${firstItems} og ${lastItem} reagerte med ${reaction.emoji}`;
  }, [userNames, userId]);

  // Get tooltip text for all reactions when user hasn't reacted
  const getAllReactionsTooltip = useCallback(() => {
    if (reactions.length === 0) return '';

    // If there's only one reaction type, use getTooltipText for that reaction
    if (reactions.length === 1) {
      return getTooltipText(reactions[0]);
    }

    // For multiple reaction types, create a combined tooltip
    // First, collect all unique user IDs who reacted
    const allUserIds = new Set<string>();
    reactions.forEach(reaction => {
      reaction.userIds.forEach(id => allUserIds.add(id));
    });

    // Get names for all users (up to 3)
    const userNamesList = Array.from(allUserIds)
      .map(id => userNames?.[id] || 'Kunde')
      .slice(0, 2);

    const remainingCount = Math.max(0, allUserIds.size - userNamesList.length);

    // Format similar to getTooltipText
    if (userNamesList.length === 0) return '';
    if (userNamesList.length === 1) {
      return `${userNamesList[0]} reagerte`;
    }
    if (userNamesList.length === 2 && remainingCount === 0) {
      return `${userNamesList[0]} og ${userNamesList[1]} reagerte`;
    }

    // Handle case with remaining users
    if (remainingCount > 0) {
      userNamesList.push(`${remainingCount} ${remainingCount === 1 ? 'annen' : 'andre'}`);
    }

    const lastItem = userNamesList.pop();
    const firstItems = userNamesList.join(', ');
    return `${firstItems} og ${lastItem} reagerte`;
  }, [reactions, userNames, getTooltipText]);

  // Don't render anything if no reactions and palette is closed
  if (totalCount === 0 && !showPalette) {
    return (
      <div className={`relative ${className}`}>
        {/* Message Highlight Overlay */}
        {messageHighlighted && (
          <div className="absolute -inset-2 bg-jobblogg-primary bg-opacity-5 rounded-lg pointer-events-none transition-opacity duration-200" />
        )}

        <div className="relative flex items-start gap-1 mt-1">
          <button
            ref={buttonRef}
            onClick={isMobile ? handleMobileTouch : handleLikerClick}
            onMouseDown={!isMobile ? handleLongPressStart : undefined}
            onMouseUp={!isMobile ? handleLongPressEnd : undefined}
            onMouseLeave={!isMobile ? handleLongPressEnd : undefined}
            onTouchStart={isMobile ? handleLongPressStart : undefined}
            onTouchEnd={isMobile ? handleLongPressEnd : undefined}
            onTouchMove={isMobile ? handleTouchMove : undefined}
            className={`
              text-xs font-semibold
              transition-all duration-150
              focus:outline-none focus:ring-1 focus:ring-jobblogg-primary focus:ring-offset-1
              min-h-[44px] min-w-[44px] flex items-center justify-center
              -m-2 p-2 rounded-md
              ${hasReacted
                ? 'text-jobblogg-primary bg-jobblogg-primary bg-opacity-10'
                : 'text-jobblogg-text-muted hover:text-jobblogg-primary hover:underline hover:bg-jobblogg-surface'
              }
              ${showPalette ? 'bg-jobblogg-primary bg-opacity-5' : ''}
            `}
            aria-label={hasReacted ? `Reager, valgt: ${userReaction?.emoji}` : 'Reager med emoji'}
            aria-pressed={hasReacted}
            disabled={isLoading}
          >
            {hasReacted ? (
              <span className="inline-flex items-center gap-1">
                <span className="text-sm">{userReaction?.emoji}</span>
                <span className="text-xs font-medium">
                  {userReaction?.count === 1 ? 'Du' : `Du +${userReaction.count - 1}`}
                </span>
              </span>
            ) : (
              'Liker'
            )}
          </button>

          {/* Desktop Palette with Visual Connection */}
          {showPalette && !isMobile && (
            <EmojiPalette
              ref={paletteRef}
              onSelect={handleReaction}
              selectedEmoji={draggedEmoji}
              isLoading={isLoading}
              onClose={() => {
                setShowPalette(false);
                setMessageHighlighted(false);
              }}
              prefersReducedMotion={prefersReducedMotion}
              position={palettePosition}
            />
          )}

          {/* Mobile Bottom Sheet */}
          <MobileEmojiBottomSheet
            isOpen={showPalette && isMobile}
            onSelect={handleReaction}
            onClose={() => {
              setShowPalette(false);
              setMessageHighlighted(false);
            }}
            selectedEmoji={draggedEmoji}
            isLoading={isLoading}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Message Highlight Overlay */}
      {messageHighlighted && (
        <div className="absolute -inset-2 bg-jobblogg-primary bg-opacity-5 rounded-lg pointer-events-none transition-opacity duration-200" />
      )}

      <div className="relative flex items-start gap-2 mt-1">
        {/* Facebook-Style Unified Reaction Bar */}
        <div className="flex items-center gap-2">
          {/* Liker Action Button - Show when user hasn't reacted */}
          {!hasReacted && (
            <button
              ref={buttonRef}
              onClick={isMobile ? handleMobileTouch : handleLikerClick}
              onMouseDown={!isMobile ? handleLongPressStart : undefined}
              onMouseUp={!isMobile ? handleLongPressEnd : undefined}
              onMouseLeave={!isMobile ? handleLongPressEnd : undefined}
              onTouchStart={isMobile ? handleLongPressStart : undefined}
              onTouchEnd={isMobile ? handleLongPressEnd : undefined}
              onTouchMove={isMobile ? handleTouchMove : undefined}
              className={`
                inline-flex items-center gap-1 text-xs font-semibold
                min-h-[32px] px-3 py-1 rounded-full
                transition-all duration-150 hover:bg-jobblogg-surface
                text-jobblogg-text-muted hover:text-jobblogg-primary
                ${showPalette ? 'bg-jobblogg-primary bg-opacity-5' : ''}
              `}
              title="Reager med emoji"
              aria-label="Reager med emoji"
              disabled={isLoading}
            >
              <span className="text-xs font-medium">Liker</span>
            </button>
          )}

          {/* Unified Reaction Display - Facebook Style */}
          {totalCount > 0 && (
            <button
              ref={hasReacted ? buttonRef : undefined}
              onClick={hasReacted ? () => {
                // Single-click removal: Remove user's existing reaction
                if (userReaction) {
                  handleReaction(userReaction.emoji);
                }
              } : () => {
                // Click on unified display shows breakdown or allows interaction
                if (topReactions.length === 1) {
                  handleReaction(topReactions[0].emoji);
                } else {
                  // For multiple reactions, trigger long press to show palette
                  if (!isMobile) {
                    // Create a synthetic event for long press
                    const syntheticEvent = { preventDefault: () => {} } as React.MouseEvent;
                    handleLongPressStart(syntheticEvent);
                  }
                }
              }}
              onMouseDown={!isMobile && hasReacted ? handleLongPressStart : undefined}
              onMouseUp={!isMobile && hasReacted ? handleLongPressEnd : undefined}
              onMouseLeave={!isMobile && hasReacted ? handleLongPressEnd : undefined}
              onTouchStart={isMobile && hasReacted ? handleLongPressStart : undefined}
              onTouchEnd={isMobile && hasReacted ? handleLongPressEnd : undefined}
              onTouchMove={isMobile && hasReacted ? handleTouchMove : undefined}
              className={`
                inline-flex items-center gap-1 text-xs font-semibold
                min-h-[32px] px-3 py-1 rounded-full
                transition-all duration-150 hover:bg-jobblogg-surface
                ${hasReacted
                  ? 'text-jobblogg-primary bg-jobblogg-primary bg-opacity-10 border border-jobblogg-primary border-opacity-20'
                  : 'text-jobblogg-text-muted hover:text-jobblogg-primary'
                }
                ${showPalette && hasReacted ? 'bg-jobblogg-primary bg-opacity-15' : ''}
              `}
              title={hasReacted ? getTooltipText(userReaction) : getAllReactionsTooltip()}
              aria-label={`${totalCount} reaksjoner, ${hasReacted ? 'du har reagert' : 'klikk for å reagere'}`}
              aria-pressed={hasReacted}
              disabled={isLoading}
            >
              {/* Overlapping Emoji Icons - Facebook Style */}
              <div className="flex items-center -space-x-1">
                {topReactions.slice(0, 3).map((reaction, index) => {
                  const isUserReaction = reaction.userIds.includes(userId);
                  return (
                    <span
                      key={reaction.emoji}
                      className={`
                        inline-flex items-center justify-center
                        w-5 h-5 text-xs rounded-full
                        ${isUserReaction ? 'bg-jobblogg-primary bg-opacity-20 ring-1 ring-jobblogg-primary ring-opacity-30' : 'bg-jobblogg-surface'}
                        ${index > 0 ? 'border-l border-white' : ''}
                      `}
                      style={{ zIndex: 10 - index }}
                    >
                      {reaction.emoji}
                    </span>
                  );
                })}
              </div>

              {/* Total Count */}
              <span className="text-xs font-medium ml-1">
                {totalCount}
              </span>
            </button>
          )}
        </div>



        {/* Desktop Palette with Visual Connection */}
        {showPalette && !isMobile && (
          <EmojiPalette
            ref={paletteRef}
            onSelect={handleReaction}
            selectedEmoji={draggedEmoji}
            isLoading={isLoading}
            onClose={() => {
              setShowPalette(false);
              setMessageHighlighted(false);
            }}
            prefersReducedMotion={prefersReducedMotion}
            position={palettePosition}
          />
        )}

        {/* Mobile Bottom Sheet */}
        <MobileEmojiBottomSheet
          isOpen={showPalette && isMobile}
          onSelect={handleReaction}
          onClose={() => {
            setShowPalette(false);
            setMessageHighlighted(false);
          }}
          selectedEmoji={draggedEmoji}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

// Emoji Palette Component
interface EmojiPaletteProps {
  onSelect: (emoji: string) => void;
  selectedEmoji: string | null;
  isLoading: boolean;
  onClose: () => void;
  prefersReducedMotion?: boolean;
  position?: { top: number; left: number; arrow: 'top' | 'bottom' | 'left' | 'right' };
}

const EmojiPalette = React.forwardRef<HTMLDivElement, EmojiPaletteProps>(
  ({ onSelect, selectedEmoji, isLoading, onClose: _onClose, prefersReducedMotion = false, position }, ref) => {
    const style = position ? {
      position: 'fixed' as const,
      top: position.top,
      left: position.left,
      zIndex: 9999 // Increased z-index to ensure it appears above all content
    } : {};

    const paletteContent = (
      <div
        ref={ref}
        style={style}
        className={`
          ${position ? '' : 'absolute z-50 bottom-full mb-2 left-1/2 transform -translate-x-1/2'}
          bg-white border border-jobblogg-border rounded-lg shadow-xl
          p-3 flex items-center gap-2 flex-wrap
          animate-in slide-in-from-bottom-2 duration-200
          w-auto min-w-[320px] max-w-[90vw]
          ${position?.arrow === 'bottom' ? 'mb-2' : ''}
          ${position?.arrow === 'top' ? 'mt-2' : ''}
        `}
        role="toolbar"
        aria-label="Velg emoji reaksjon"
      >
        {/* Visual Connection Arrow */}
        {position && (
          <div
            className={`
              absolute w-0 h-0 border-solid
              ${position.arrow === 'bottom'
                ? 'top-full left-1/2 transform -translate-x-1/2 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-jobblogg-border'
                : ''
              }
              ${position.arrow === 'top'
                ? 'bottom-full left-1/2 transform -translate-x-1/2 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-jobblogg-border'
                : ''
              }
            `}
          />
        )}

        {/* Inner arrow for clean appearance */}
        {position && (
          <div
            className={`
              absolute w-0 h-0 border-solid
              ${position.arrow === 'bottom'
                ? 'top-full left-1/2 transform -translate-x-1/2 -mt-px'
                : ''
              }
              ${position.arrow === 'top'
                ? 'bottom-full left-1/2 transform -translate-x-1/2 -mb-px'
                : ''
              }
            `}
            style={{
              ...(position.arrow === 'bottom' && {
                borderLeft: '7px solid transparent',
                borderRight: '7px solid transparent',
                borderTop: '7px solid white'
              }),
              ...(position.arrow === 'top' && {
                borderLeft: '7px solid transparent',
                borderRight: '7px solid transparent',
                borderBottom: '7px solid white'
              })
            }}
          />
        )}

        {QUICK_REACTIONS.map(({ emoji, label, priority }) => (
          <button
            key={emoji}
            data-emoji={emoji}
            onClick={() => onSelect(emoji)}
            disabled={isLoading}
            className={`
              min-h-[44px] min-w-[44px] p-2 rounded-lg
              hover:bg-jobblogg-surface-hover
              ${prefersReducedMotion ? 'transition-colors duration-150' : 'transition-all duration-150 hover:scale-110'}
              focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-1
              ${priority === 1 ? 'ring-1 ring-jobblogg-primary ring-opacity-30' : ''}
              ${selectedEmoji === emoji
                ? prefersReducedMotion
                  ? 'bg-jobblogg-primary text-white'
                  : 'scale-125 bg-jobblogg-primary text-white'
                : ''
              }
              ${selectedEmoji && selectedEmoji !== emoji
                ? prefersReducedMotion
                  ? 'opacity-70'
                  : 'scale-90 opacity-70'
                : ''
              }
            `}
            aria-label={`Reager med ${emoji} (${label})`}
            title={label}
          >
            <span className="text-xl">{emoji}</span>
          </button>
        ))}
      </div>
    );

    // Use portal for fixed positioning to avoid clipping by parent containers
    if (position && typeof document !== 'undefined') {
      return createPortal(paletteContent, document.body);
    }

    return paletteContent;
  }
);

EmojiPalette.displayName = 'EmojiPalette';

export default EnhancedEmojiReactions;
