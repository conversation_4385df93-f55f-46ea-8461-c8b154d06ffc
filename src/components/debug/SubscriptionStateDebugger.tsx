import { useUser } from '@clerk/clerk-react';
import { useAction, useQuery } from 'convex/react';
import React, { useState } from 'react';
import { api } from '../../../convex/_generated/api';

/**
 * Debug component to investigate subscription state synchronization issues
 */
export const SubscriptionStateDebugger: React.FC = () => {
  const { user } = useUser();
  const [debugResult, setDebugResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const debugAction = useAction(api.debugSubscriptionState.debugSubscriptionState);
  const forceSyncAction = useAction(api.webhookDebug.forceSyncSubscriptionStatus);
  const emergencyFixAction = useAction(api.quickFix.emergencyFixSubscriptionStatus);

  const quickDebugQuery = useQuery(
    api.debugSubscriptionState.getSubscriptionDebugInfo,
    user?.id ? { userId: user.id } : "skip"
  );

  const recentWebhooks = useQuery(api.webhookDebug.getRecentWebhookEvents, { limit: 10 });
  const subscriptionUpdates = useQuery(api.webhookDebug.getSubscriptionUpdateEvents, { limit: 5 });

  const runFullDebug = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const result = await debugAction({ userId: user.id });
      setDebugResult(result);
      console.log('🔍 Full debug result:', result);
    } catch (error) {
      console.error('❌ Debug action failed:', error);
      setDebugResult({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const forceSync = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const result = await forceSyncAction({ userId: user.id });
      console.log('🔄 Force sync result:', result);
      alert(`Force sync result: ${result.success ? 'Success' : 'Failed'}\n${JSON.stringify(result, null, 2)}`);

      // Refresh debug data
      await runFullDebug();
    } catch (error) {
      console.error('❌ Force sync failed:', error);
      alert(`Force sync failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const emergencyFix = async () => {
    if (!user?.id) return;

    const reason = prompt('Reason for emergency fix (required):');
    if (!reason) return;

    setIsLoading(true);
    try {
      const result = await emergencyFixAction({
        userId: user.id,
        reason: `Manual fix by user: ${reason}`
      });
      console.log('🚨 Emergency fix result:', result);

      if (result.success) {
        alert(`✅ Emergency fix successful!\n\nAction: ${result.action}\nOld status: ${result.subscription?.oldStatus}\nNew status: ${result.subscription?.newStatus}`);

        // Trigger a page refresh to update all components
        window.location.reload();
      } else {
        alert(`❌ Emergency fix failed: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Emergency fix failed:', error);
      alert(`Emergency fix failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user?.id) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">User not loaded yet...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 bg-white rounded-xl shadow-soft border border-jobblogg-border">
      <div className="text-center">
        <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
          🔍 Subscription State Debugger
        </h2>
        <p className="text-jobblogg-text-medium">
          Investigate subscription state synchronization issues
        </p>
      </div>

      {/* Quick Debug Info */}
      <div className="bg-jobblogg-neutral-soft rounded-lg p-4">
        <h3 className="font-medium text-jobblogg-text-strong mb-3">Quick Debug Info:</h3>
        {quickDebugQuery ? (
          <div className="space-y-2 text-sm">
            <div>
              <strong>User ID:</strong> {user.id}
            </div>
            <div>
              <strong>Current Time:</strong> {new Date(quickDebugQuery.currentTime).toISOString()}
            </div>
            <div>
              <strong>Subscription Status:</strong> {quickDebugQuery.subscription?.status || 'No subscription'}
            </div>
            <div>
              <strong>Plan Level:</strong> {quickDebugQuery.subscription?.planLevel || 'N/A'}
            </div>
            <div>
              <strong>Trial End:</strong> {quickDebugQuery.subscription?.trialEnd ? 
                new Date(quickDebugQuery.subscription.trialEnd).toISOString() : 'N/A'}
            </div>
            <div>
              <strong>Trial Converted At:</strong> {quickDebugQuery.subscription?.trialConvertedAt ? 
                new Date(quickDebugQuery.subscription.trialConvertedAt).toISOString() : 'N/A'}
            </div>
            <div>
              <strong>User Has Active Subscription:</strong> {quickDebugQuery.user?.hasActiveSubscription ? 'Yes' : 'No'}
            </div>
          </div>
        ) : (
          <p className="text-jobblogg-text-medium">Loading quick debug info...</p>
        )}
      </div>

      {/* Action Buttons */}
      <div className="text-center space-y-3">
        <div className="flex gap-2 justify-center flex-wrap">
          <button
            onClick={runFullDebug}
            disabled={isLoading}
            className="px-4 py-2 bg-jobblogg-primary text-white rounded-xl font-semibold hover:bg-jobblogg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? 'Running...' : 'Debug'}
          </button>
          <button
            onClick={forceSync}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 text-white rounded-xl font-semibold hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? 'Syncing...' : 'Force Sync'}
          </button>
          <button
            onClick={emergencyFix}
            disabled={isLoading}
            className="px-4 py-2 bg-red-600 text-white rounded-xl font-semibold hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? 'Fixing...' : '🚨 EMERGENCY FIX'}
          </button>
        </div>
        <div className="text-xs text-jobblogg-text-medium space-y-1">
          <p><strong>Debug:</strong> Analyze current state</p>
          <p><strong>Force Sync:</strong> Update if trial ended</p>
          <p><strong>Emergency Fix:</strong> Manually set to active (use if webhooks failed)</p>
        </div>
      </div>

      {/* Webhook Events */}
      <div className="bg-jobblogg-neutral-soft rounded-lg p-4">
        <h3 className="font-medium text-jobblogg-text-strong mb-3">Recent Webhook Events:</h3>
        {recentWebhooks ? (
          <div className="space-y-2">
            {recentWebhooks.slice(0, 5).map((event, index) => (
              <div key={event._id} className="bg-white rounded p-2 border text-xs">
                <div className="flex justify-between items-center">
                  <span className={`px-2 py-1 rounded text-xs ${
                    event.processed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {event.type}
                  </span>
                  <span className="text-jobblogg-text-medium">
                    {new Date(event.createdAt).toLocaleString()}
                  </span>
                </div>
                {event.data && (
                  <div className="mt-1 text-jobblogg-text-medium">
                    Status: {event.data.status} | ID: {event.data.id?.slice(-8)}
                  </div>
                )}
                {event.error && (
                  <div className="mt-1 text-red-600 text-xs">
                    Error: {event.error}
                  </div>
                )}
              </div>
            ))}
            {recentWebhooks.length === 0 && (
              <p className="text-jobblogg-text-medium">No recent webhook events found</p>
            )}
          </div>
        ) : (
          <p className="text-jobblogg-text-medium">Loading webhook events...</p>
        )}
      </div>

      {/* Subscription Update Events */}
      <div className="bg-jobblogg-neutral-soft rounded-lg p-4">
        <h3 className="font-medium text-jobblogg-text-strong mb-3">Subscription Update Events:</h3>
        {subscriptionUpdates ? (
          <div className="space-y-2">
            {subscriptionUpdates.map((event, index) => (
              <div key={event._id} className="bg-white rounded p-2 border text-xs">
                <div className="flex justify-between items-center">
                  <span className={`px-2 py-1 rounded text-xs ${
                    event.processed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {event.previousStatus} → {event.status}
                  </span>
                  <span className="text-jobblogg-text-medium">
                    {new Date(event.createdAt).toLocaleString()}
                  </span>
                </div>
                <div className="mt-1 text-jobblogg-text-medium">
                  Subscription: {event.subscriptionId?.slice(-8)} | Customer: {event.customerId?.slice(-8)}
                </div>
                {event.error && (
                  <div className="mt-1 text-red-600 text-xs">
                    Error: {event.error}
                  </div>
                )}
              </div>
            ))}
            {subscriptionUpdates.length === 0 && (
              <p className="text-red-600">❌ No subscription update events found - this is likely the problem!</p>
            )}
          </div>
        ) : (
          <p className="text-jobblogg-text-medium">Loading subscription updates...</p>
        )}
      </div>

      {/* Full Debug Results */}
      {debugResult && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-jobblogg-text-strong mb-3">Full Debug Results:</h3>
          
          {debugResult.error ? (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <p className="text-red-800 font-medium">Error:</p>
              <p className="text-red-700">{debugResult.error}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Raw Subscription */}
              <div>
                <h4 className="font-medium text-jobblogg-text-strong mb-2">Raw Subscription Data:</h4>
                <div className="bg-white rounded p-3 border text-sm">
                  {debugResult.rawSubscription ? (
                    <div className="space-y-1">
                      <div><strong>Status:</strong> <span className={`px-2 py-1 rounded text-xs ${
                        debugResult.rawSubscription.status === 'active' ? 'bg-green-100 text-green-800' :
                        debugResult.rawSubscription.status === 'trialing' ? 'bg-blue-100 text-blue-800' :
                        'bg-red-100 text-red-800'
                      }`}>{debugResult.rawSubscription.status}</span></div>
                      <div><strong>Plan:</strong> {debugResult.rawSubscription.planLevel}</div>
                      <div><strong>Trial End:</strong> {debugResult.rawSubscription.trialEnd ? 
                        new Date(debugResult.rawSubscription.trialEnd).toISOString() : 'N/A'}</div>
                      <div><strong>Trial Converted:</strong> {debugResult.rawSubscription.trialConvertedAt ? 
                        new Date(debugResult.rawSubscription.trialConvertedAt).toISOString() : 'N/A'}</div>
                      <div><strong>Updated:</strong> {debugResult.rawSubscription.updatedAt ? 
                        new Date(debugResult.rawSubscription.updatedAt).toISOString() : 'N/A'}</div>
                    </div>
                  ) : (
                    <p className="text-red-600">No subscription found</p>
                  )}
                </div>
              </div>

              {/* Computed Status */}
              <div>
                <h4 className="font-medium text-jobblogg-text-strong mb-2">Computed Subscription Status:</h4>
                <div className="bg-white rounded p-3 border text-sm">
                  {debugResult.subscriptionStatus ? (
                    <div className="grid grid-cols-2 gap-2">
                      <div><strong>Has Active:</strong> {debugResult.subscriptionStatus.hasActiveSubscription ? '✅' : '❌'}</div>
                      <div><strong>In Trial:</strong> {debugResult.subscriptionStatus.isInTrial ? '✅' : '❌'}</div>
                      <div><strong>Trial Expired:</strong> {debugResult.subscriptionStatus.isTrialExpired ? '⚠️' : '✅'}</div>
                      <div><strong>Needs Upgrade:</strong> {debugResult.subscriptionStatus.needsUpgrade ? '⚠️' : '✅'}</div>
                      <div><strong>Can Create:</strong> {debugResult.subscriptionStatus.canCreateProjects ? '✅' : '❌'}</div>
                      <div><strong>Full Access:</strong> {debugResult.subscriptionStatus.hasFullAccess ? '✅' : '❌'}</div>
                    </div>
                  ) : (
                    <p className="text-red-600">No computed status</p>
                  )}
                </div>
              </div>

              {/* Time Analysis */}
              {debugResult.timeCalculations && (
                <div>
                  <h4 className="font-medium text-jobblogg-text-strong mb-2">Time Analysis:</h4>
                  <div className="bg-white rounded p-3 border text-sm">
                    <div className="space-y-1">
                      <div><strong>Trial Expired by Time:</strong> {debugResult.timeCalculations.isTrialExpiredByTime ? '⚠️ Yes' : '✅ No'}</div>
                      <div><strong>Minutes Until Expiration:</strong> {debugResult.timeCalculations.minutesUntilExpiration}</div>
                      <div><strong>Trial End Date:</strong> {debugResult.timeCalculations.trialEndDate}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Analysis */}
              <div>
                <h4 className="font-medium text-jobblogg-text-strong mb-2">Analysis:</h4>
                <div className="bg-white rounded p-3 border text-sm">
                  <div className="space-y-1">
                    <div><strong>Should Show Trial Banner:</strong> {debugResult.analysis.shouldShowTrialBanner ? '⚠️ Yes' : '✅ No'}</div>
                    <div><strong>Status Mismatch:</strong> {debugResult.analysis.statusMismatch ? '⚠️ Yes' : '✅ No'}</div>
                    <div><strong>Has All Data:</strong> {debugResult.analysis.hasRawSubscription && debugResult.analysis.hasComputedStatus ? '✅ Yes' : '❌ No'}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Raw JSON */}
          <details className="mt-4">
            <summary className="cursor-pointer font-medium text-jobblogg-text-strong">Raw JSON Data</summary>
            <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto max-h-96">
              {JSON.stringify(debugResult, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
};
