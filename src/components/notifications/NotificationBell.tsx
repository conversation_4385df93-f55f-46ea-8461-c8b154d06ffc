import React, { useState, useRef, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { NotificationDropdown } from './NotificationDropdown';

interface NotificationBellProps {
  className?: string;
}

export const NotificationBell: React.FC<NotificationBellProps> = ({ className = '' }) => {
  const { user } = useUser();
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Query comprehensive notification log
  const notifications = useQuery(
    api.notifications.getNotificationLog,
    user?.id ? {
      userId: user.id,
      limit: 10,
      includeRead: false, // Only show unread in dropdown
    } : "skip"
  );

  // Query notification statistics
  const notificationStats = useQuery(
    api.notifications.getNotificationStats,
    user?.id ? { userId: user.id } : "skip"
  );

  const unreadCount = notificationStats?.unreadCount || 0;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showDropdown]);

  const handleBellClick = () => {
    setShowDropdown(!showDropdown);
  };

  const handleCloseDropdown = () => {
    setShowDropdown(false);
  };

  const handleMarkAsRead = (_notificationIds?: string[]) => {
    // Refresh notifications after marking as read
    // The useQuery will automatically update
  };

  return (
    <div className={`relative ${className}`} data-notification-bell>
      {/* Bell Button */}
      <button
        onClick={handleBellClick}
        className={`
          relative p-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2
          ${unreadCount > 0
            ? 'text-jobblogg-warning hover:bg-jobblogg-warning/10'
            : 'text-jobblogg-text-medium hover:bg-jobblogg-neutral hover:text-jobblogg-text-strong'
          }
        `}
        aria-label={`Notifikasjoner${unreadCount > 0 ? ` (${unreadCount} nye)` : ''}`}
      >
        <svg
          className={`w-6 h-6 ${unreadCount > 0 ? 'animate-pulse' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
          />
        </svg>

        {/* Notification Badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-jobblogg-error rounded-full min-w-[20px] h-5 animate-bounce">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      {showDropdown && (
        <div ref={dropdownRef}>
          <NotificationDropdown
            notifications={notifications || []}
            onClose={handleCloseDropdown}
            onMarkAsRead={handleMarkAsRead}
          />
        </div>
      )}

    </div>
  );
};

export default NotificationBell;
