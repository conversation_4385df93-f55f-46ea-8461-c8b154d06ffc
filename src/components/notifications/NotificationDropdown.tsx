import React from 'react';
import { Link } from 'react-router-dom';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface NotificationDropdownProps {
  notifications: any[];
  onClose: () => void;
  onMarkAsRead: (notificationIds?: string[]) => void;
}

export const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  notifications,
  onClose,
  onMarkAsRead,
}) => {
  const markAsRead = useMutation(api.notifications.markNotificationsAsRead);

  const formatTimeAgo = (timeAgo: number): string => {
    const minutes = Math.floor(timeAgo / (1000 * 60));
    const hours = Math.floor(timeAgo / (1000 * 60 * 60));
    const days = Math.floor(timeAgo / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Akkurat nå';
    if (minutes < 60) return `${minutes} min siden`;
    if (hours < 24) return `${hours} timer siden`;
    if (days === 1) return 'I går';
    if (days < 7) return `${days} dager siden`;
    
    return new Date(Date.now() - timeAgo).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short'
    });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'invitation_accepted':
        return (
          <div className="w-8 h-8 bg-jobblogg-accent/10 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-jobblogg-accent" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'invitation_declined':
        return (
          <div className="w-8 h-8 bg-jobblogg-error/10 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-jobblogg-error" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
        );
      case 'chat_message':
        return (
          <div className="w-8 h-8 bg-jobblogg-primary/10 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
        );
      case 'project_update':
      case 'project_status_change':
        return (
          <div className="w-8 h-8 bg-jobblogg-warning/10 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
        );
      case 'team_invitation':
        return (
          <div className="w-8 h-8 bg-jobblogg-primary/10 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
        );
      case 'role_changed':
        return (
          <div className="w-8 h-8 bg-jobblogg-accent/10 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-jobblogg-neutral/10 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-jobblogg-text-muted" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
        );
    }
  };

  const getNotificationLink = (notification: any) => {
    switch (notification.type) {
      case 'invitation_accepted':
      case 'invitation_declined':
        if (notification.data?.projectId) {
          return `/project/${notification.data.projectId}`;
        }
        return '/invitations';
      case 'chat_message':
        if (notification.data?.projectId) {
          return `/project/${notification.data.projectId}#chat`;
        }
        return '/projects';
      case 'project_update':
      case 'project_status_change':
        if (notification.data?.projectId) {
          return `/project/${notification.data.projectId}`;
        }
        return '/projects';
      case 'team_invitation':
        return '/team';
      case 'role_changed':
        return '/team';
      default:
        return '/';
    }
  };

  const handleNotificationClick = async (notification: any) => {
    if (!notification.isRead) {
      await markAsRead({
        userId: notification.userId,
        notificationIds: [notification._id],
      });
      onMarkAsRead([notification._id]);
    }
    onClose();
  };

  const handleMarkAllAsRead = async () => {
    const unreadNotifications = notifications.filter(n => !n.isRead);
    if (unreadNotifications.length > 0) {
      await markAsRead({
        userId: unreadNotifications[0].userId,
        notificationIds: unreadNotifications.map(n => n._id),
      });
      onMarkAsRead(unreadNotifications.map(n => n._id));
    }
  };

  return (
    <div className="absolute right-0 top-full mt-2 w-96 bg-white rounded-xl shadow-xl border border-jobblogg-border z-50">
      {/* Header */}
      <div className="p-4 border-b border-jobblogg-border">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-jobblogg-text-strong">Varsler</h3>
          {notifications.some(n => !n.isRead) && (
            <button
              onClick={handleMarkAllAsRead}
              className="text-sm text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors"
            >
              Merk alle som lest
            </button>
          )}
        </div>
      </div>

      {/* Notifications List */}
      <div className="max-h-96 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-6 text-center text-jobblogg-text-muted">
            <svg className="w-12 h-12 mx-auto mb-3 text-jobblogg-text-muted/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 17h5l-5 5v-5zM11 19H6a2 2 0 01-2-2V7a2 2 0 012-2h8a2 2 0 012 2v4" />
            </svg>
            <p className="text-sm">Ingen nye varsler</p>
          </div>
        ) : (
          <div className="divide-y divide-jobblogg-border">
            {notifications.map((notification) => (
              <Link
                key={notification._id}
                to={getNotificationLink(notification)}
                onClick={() => handleNotificationClick(notification)}
                className={`block p-4 hover:bg-jobblogg-neutral/30 transition-colors ${
                  !notification.isRead ? 'bg-jobblogg-primary/5' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  {getNotificationIcon(notification.type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <p className={`text-sm font-medium ${
                        !notification.isRead ? 'text-jobblogg-text-strong' : 'text-jobblogg-text-medium'
                      }`}>
                        {notification.title}
                      </p>
                      {!notification.isRead && (
                        <div className="w-2 h-2 bg-jobblogg-primary rounded-full flex-shrink-0 mt-1"></div>
                      )}
                    </div>
                    <p className="text-sm text-jobblogg-text-muted mt-1 line-clamp-2">
                      {notification.message}
                    </p>
                    <p className="text-xs text-jobblogg-text-muted mt-2">
                      {formatTimeAgo(notification.timeAgo)}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="p-3 border-t border-jobblogg-border">
          <Link
            to="/notifications"
            onClick={onClose}
            className="block text-center text-sm text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors"
          >
            Se alle varsler
          </Link>
        </div>
      )}
    </div>
  );
};
