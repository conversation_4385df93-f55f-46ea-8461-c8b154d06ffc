import React, { useState } from 'react';
import { usePWA } from '../../hooks/usePWA';
// import { StatsCard } from '../ui'; // Unused after refactoring

interface DashboardPWACardProps {
  /** Animation delay for staggered animations */
  animationDelay?: string;
  /** Additional CSS classes */
  className?: string;
  /** Click handler */
  onClick?: () => void;
}

/**
 * Dashboard PWA Installation Card
 * Shows PWA installation option in dashboard stats section
 * Only appears when PWA installation is available and user preferences allow it
 */
export const DashboardPWACard: React.FC<DashboardPWACardProps> = ({
  // animationDelay = '0s', // Unused after refactoring
  className = '',
  onClick
}) => {
  const {
    isInstallable,
    isInstalled,
    shouldShowInstallPrompt,
    installApp,
    dismissInstallPrompt,
    getInstallStatus
  } = usePWA();
  
  const [isInstalling, setIsInstalling] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Don't show if PWA is already installed or not installable
  if (isInstalled || !isInstallable || !shouldShowInstallPrompt) {
    return null;
  }

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      const success = await installApp();
      if (success) {
        console.log('[PWA Card] Installation successful');
      }
    } catch (error) {
      console.error('[PWA Card] Installation failed:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = () => {
    dismissInstallPrompt(false); // Temporary dismissal
    if (onClick) onClick();
  };

  const handlePermanentDismiss = () => {
    dismissInstallPrompt(true); // Permanent dismissal
    if (onClick) onClick();
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  const installStatus = getInstallStatus();

  return (
    <div className={`bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 cursor-pointer transition-all duration-200 hover:shadow-lg ${className}`} onClick={toggleDetails}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 rounded-lg bg-jobblogg-primary/10 flex items-center justify-center">
            <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-jobblogg-text-strong">📱 Installer JobbLogg</h3>
            <p className="text-sm text-jobblogg-text-muted">Rask tilgang fra hjemskjermen</p>
          </div>
        </div>
        <div className="text-2xl font-bold text-jobblogg-primary">PWA</div>
      </div>
      {/* PWA Installation Details */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-jobblogg-border space-y-4">
          {/* Benefits */}
          <div className="space-y-2">
            <h4 className="font-medium text-jobblogg-text-strong text-sm">
              Fordeler med JobbLogg-appen:
            </h4>
            <ul className="text-xs text-jobblogg-text-medium space-y-1">
              <li className="flex items-center gap-2">
                <div className="w-1 h-1 bg-jobblogg-primary rounded-full" />
                Rask tilgang fra hjemskjermen
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1 h-1 bg-jobblogg-primary rounded-full" />
                Fungerer offline på byggeplassen
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1 h-1 bg-jobblogg-primary rounded-full" />
                Push-varsler for nye meldinger
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1 h-1 bg-jobblogg-primary rounded-full" />
                Ingen app store nødvendig
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleInstall();
              }}
              disabled={isInstalling}
              className="w-full px-3 py-2 bg-jobblogg-primary hover:bg-jobblogg-primary-dark text-white text-sm font-medium rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed min-h-[36px] flex items-center justify-center gap-2"
            >
              {isInstalling ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Installerer...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Installer nå
                </>
              )}
            </button>

            <div className="flex gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDismiss();
                }}
                className="flex-1 px-3 py-2 bg-jobblogg-neutral hover:bg-jobblogg-border text-jobblogg-text-medium text-sm font-medium rounded-lg transition-colors duration-200 min-h-[36px]"
              >
                Ikke nå
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handlePermanentDismiss();
                }}
                className="flex-1 px-3 py-2 bg-jobblogg-neutral hover:bg-jobblogg-border text-jobblogg-text-muted text-sm font-medium rounded-lg transition-colors duration-200 min-h-[36px]"
              >
                Ikke vis igjen
              </button>
            </div>
          </div>

          {/* Debug Info (only in development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-3 pt-3 border-t border-jobblogg-border">
              <details className="text-xs text-jobblogg-text-muted">
                <summary className="cursor-pointer hover:text-jobblogg-text-medium">
                  Debug Info
                </summary>
                <div className="mt-2 space-y-1">
                  <div>Status: {installStatus.reason}</div>
                  <div>Dismissals: {installStatus.dismissalCount}</div>
                  {installStatus.nextPromptDate && (
                    <div>Next prompt: {installStatus.nextPromptDate.toLocaleDateString('nb-NO')}</div>
                  )}
                </div>
              </details>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Compact PWA Installation Button for Header Actions
 * Minimal button that can be placed in dashboard header actions
 */
export const PWAInstallButton: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  const {
    isInstallable,
    isInstalled,
    shouldShowInstallPrompt,
    installApp
  } = usePWA();
  
  const [isInstalling, setIsInstalling] = useState(false);

  // Don't show if PWA is already installed or not installable
  if (isInstalled || !isInstallable || !shouldShowInstallPrompt) {
    return null;
  }

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      await installApp();
    } catch (error) {
      console.error('[PWA Button] Installation failed:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  return (
    <button
      onClick={handleInstall}
      disabled={isInstalling}
      className={`
        inline-flex items-center gap-2 px-3 py-2 
        bg-jobblogg-primary hover:bg-jobblogg-primary-dark 
        text-white text-sm font-medium rounded-lg 
        transition-colors duration-200 
        disabled:opacity-50 disabled:cursor-not-allowed
        min-h-[44px] ${className}
      `}
      title="Installer JobbLogg som app"
    >
      {isInstalling ? (
        <>
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          <span className="hidden sm:inline">Installerer...</span>
        </>
      ) : (
        <>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          <span className="hidden sm:inline">Installer app</span>
        </>
      )}
    </button>
  );
};

/**
 * PWA Status Indicator for showing current installation status
 * Can be used in settings or profile areas
 */
export const PWAStatusIndicator: React.FC<{
  className?: string;
  showDetails?: boolean;
}> = ({ className = '', showDetails = false }) => {
  const {
    isInstalled,
    isInstallable,
    shouldShowInstallPrompt,
    getInstallStatus
  } = usePWA();

  const status = getInstallStatus();

  let statusText = '';
  let statusColor = '';
  let statusIcon = null;

  if (isInstalled) {
    statusText = 'PWA installert';
    statusColor = 'text-jobblogg-success';
    statusIcon = (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
      </svg>
    );
  } else if (isInstallable && shouldShowInstallPrompt) {
    statusText = 'Kan installeres';
    statusColor = 'text-jobblogg-primary';
    statusIcon = (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3" />
      </svg>
    );
  } else if (isInstallable) {
    statusText = 'Installasjon tilgjengelig';
    statusColor = 'text-jobblogg-text-muted';
    statusIcon = (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    );
  } else {
    statusText = 'PWA ikke tilgjengelig';
    statusColor = 'text-jobblogg-text-muted';
    statusIcon = (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636" />
      </svg>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className={`flex items-center gap-2 ${statusColor}`}>
        {statusIcon}
        <span className="text-sm font-medium">{statusText}</span>
      </div>
      
      {showDetails && (
        <div className="text-xs text-jobblogg-text-muted">
          {status.reason}
        </div>
      )}
    </div>
  );
};
