import React from 'react';

interface PWAValuePropositionProps {
  /** Variant of the value proposition display */
  variant?: 'banner' | 'modal' | 'card' | 'compact';
  /** Additional CSS classes */
  className?: string;
  /** Whether to show the construction industry context */
  showIndustryContext?: boolean;
}

/**
 * PWA Value Proposition Component
 * Explains the benefits of installing JobbLogg as a PWA
 * Tailored for Norwegian construction industry context
 */
export const PWAValueProposition: React.FC<PWAValuePropositionProps> = ({
  variant = 'banner',
  className = '',
  showIndustryContext = true
}) => {
  const benefits = [
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      ),
      title: 'Rask tilgang',
      description: 'Åpne JobbLogg direkte fra hjemskjermen uten å åpne nettleseren'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a8.966 8.966 0 008.354-5.646z" />
        </svg>
      ),
      title: 'Fungerer offline',
      description: 'Dokumenter arbeid selv uten internettforbindelse på byggeplassen'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z" />
        </svg>
      ),
      title: 'Push-varsler',
      description: 'Få beskjed med en gang når det skjer noe nytt i prosjektene dine'
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      ),
      title: 'Enkel bildedeling',
      description: 'Ta bilder direkte i appen og del dem øyeblikkelig med teamet'
    }
  ];

  const industryBenefits = [
    {
      icon: '🏗️',
      title: 'Perfekt for byggeplassen',
      description: 'Designet spesielt for entreprenører og håndverkere som jobber på ulike steder'
    },
    {
      icon: '📱',
      title: 'Ingen app store',
      description: 'Installer direkte fra nettleseren - ingen nedlasting fra app store nødvendig'
    },
    {
      icon: '🔒',
      title: 'Sikker og privat',
      description: 'Dine prosjektdata lagres trygt og er kun tilgjengelig for deg og ditt team'
    }
  ];

  if (variant === 'compact') {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-jobblogg-primary rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 className="font-semibold text-jobblogg-text-strong">Installer JobbLogg-appen</h3>
            <p className="text-sm text-jobblogg-text-medium">Rask tilgang og offline-funksjonalitet</p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-2 text-xs">
          {benefits.slice(0, 4).map((benefit, index) => (
            <div key={index} className="flex items-center gap-2 text-jobblogg-text-medium">
              <div className="text-jobblogg-primary">{benefit.icon}</div>
              <span>{benefit.title}</span>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <div className={`bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-4 ${className}`}>
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-jobblogg-primary rounded-xl flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-jobblogg-text-strong">
              Installer JobbLogg som app
            </h3>
            <p className="text-jobblogg-text-medium">
              Få en bedre opplevelse med app-funksjonalitet
            </p>
          </div>
        </div>

        <div className="grid gap-3">
          {benefits.map((benefit, index) => (
            <div key={index} className="flex items-start gap-3">
              <div className="flex-shrink-0 w-8 h-8 bg-jobblogg-primary/10 rounded-lg flex items-center justify-center text-jobblogg-primary">
                {benefit.icon}
              </div>
              <div>
                <h4 className="font-medium text-jobblogg-text-strong text-sm">
                  {benefit.title}
                </h4>
                <p className="text-jobblogg-text-medium text-sm">
                  {benefit.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {showIndustryContext && (
          <div className="pt-4 border-t border-jobblogg-border space-y-3">
            <h4 className="font-medium text-jobblogg-text-strong text-sm">
              Spesielt utviklet for byggebransjen
            </h4>
            {industryBenefits.map((benefit, index) => (
              <div key={index} className="flex items-start gap-3">
                <span className="text-lg">{benefit.icon}</span>
                <div>
                  <h5 className="font-medium text-jobblogg-text-strong text-sm">
                    {benefit.title}
                  </h5>
                  <p className="text-jobblogg-text-medium text-xs">
                    {benefit.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  // Default banner variant
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-jobblogg-primary rounded-xl flex items-center justify-center">
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        </div>
        <div>
          <h3 className="font-semibold text-jobblogg-text-strong">
            Installer JobbLogg som app
          </h3>
          <p className="text-sm text-jobblogg-text-medium">
            Få rask tilgang og offline-funksjonalitet
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {benefits.map((benefit, index) => (
          <div key={index} className="flex items-center gap-3">
            <div className="flex-shrink-0 w-6 h-6 text-jobblogg-primary">
              {benefit.icon}
            </div>
            <div>
              <h4 className="font-medium text-jobblogg-text-strong text-sm">
                {benefit.title}
              </h4>
              <p className="text-jobblogg-text-medium text-xs">
                {benefit.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      {showIndustryContext && (
        <div className="pt-3 border-t border-jobblogg-border">
          <p className="text-xs text-jobblogg-text-muted">
            💡 <strong>Perfekt for byggeplassen:</strong> JobbLogg fungerer offline og lar deg dokumentere arbeid selv uten internettforbindelse. 
            Installer appen for den beste opplevelsen på jobb.
          </p>
        </div>
      )}
    </div>
  );
};

/**
 * PWA Installation Instructions Component
 * Shows browser-specific installation instructions
 */
export const PWAInstallInstructions: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  const getInstallInstructions = (): string => {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      return 'Trykk på meny-ikonet (⋮) og velg "Installer JobbLogg"';
    } else if (userAgent.includes('firefox')) {
      return 'Trykk på adresselinjen og velg "Installer denne siden som app"';
    } else if (userAgent.includes('safari')) {
      return 'Trykk på Del-knappen og velg "Legg til på hjemskjerm"';
    } else if (userAgent.includes('edg')) {
      return 'Trykk på meny-ikonet (⋯) og velg "Installer denne siden som app"';
    } else {
      return 'Se nettleserens meny for å installere som app';
    }
  };

  return (
    <div className={`bg-jobblogg-neutral rounded-lg p-3 ${className}`}>
      <div className="flex items-start gap-2">
        <svg className="w-4 h-4 text-jobblogg-primary mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <div>
          <h4 className="font-medium text-jobblogg-text-strong text-sm mb-1">
            Installasjonsinstruksjoner
          </h4>
          <p className="text-jobblogg-text-medium text-xs">
            {getInstallInstructions()}
          </p>
        </div>
      </div>
    </div>
  );
};
