import React, { useEffect, useState } from 'react';
import { usePWA } from '../../hooks/usePWA';
import { usePWAMilestones, usePWAMilestoneBanner } from '../../hooks/usePWAMilestones';
import { PWAInstallBanner } from '../PWAInstallBanner';

interface SmartPWAPromptManagerProps {
  /** Additional CSS classes */
  className?: string;
  /** Whether to enable milestone-based prompts */
  enableMilestonePrompts?: boolean;
  /** Whether to enable regular time-based prompts */
  enableRegularPrompts?: boolean;
}

/**
 * Smart PWA Prompt Manager
 * Intelligently manages PWA installation prompts based on user behavior,
 * milestones, and preferences to maximize installation conversion while
 * respecting user preferences and avoiding spam
 */
export const SmartPWAPromptManager: React.FC<SmartPWAPromptManagerProps> = ({
  className = '',
  enableMilestonePrompts = true,
  enableRegularPrompts = true
}) => {
  const {
    isInstalled,
    isInstallable,
    shouldShowInstallPrompt
  } = usePWA();

  const {
    shouldShowMilestoneBanner,
    milestoneType,
    isMilestonePrompt
  } = usePWAMilestoneBanner();

  // Removed unused state variables

  // Don't show anything if PWA is already installed or not installable
  if (isInstalled || !isInstallable) {
    return null;
  }

  // Priority 1: Milestone-based prompts (highest conversion rate)
  if (enableMilestonePrompts && shouldShowMilestoneBanner && milestoneType) {
    return (
      <PWAInstallBanner
        isMilestonePrompt={isMilestonePrompt}
        milestoneType={milestoneType}
        className={`${className} milestone-prompt`}
      />
    );
  }

  // Priority 2: Regular time-based prompts (when preferences allow)
  if (enableRegularPrompts && shouldShowInstallPrompt) {
    return (
      <PWAInstallBanner
        isMilestonePrompt={false}
        className={`${className} regular-prompt`}
      />
    );
  }

  return null;
};

/**
 * PWA Prompt Context Provider
 * Provides context for PWA prompts across the application
 */
interface PWAPromptContextType {
  triggerMilestoneCheck: () => void;
  suppressPrompts: (duration: number) => void;
  canShowPrompts: boolean;
}

const PWAPromptContext = React.createContext<PWAPromptContextType | null>(null);

export const PWAPromptProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [suppressedUntil, setSuppressedUntil] = useState<number | null>(null);
  const { recordFirstWeekActive, recordFirstMonthActive } = usePWAMilestones();

  const triggerMilestoneCheck = () => {
    // Check time-based milestones
    recordFirstWeekActive();
    recordFirstMonthActive();
  };

  const suppressPrompts = (duration: number) => {
    setSuppressedUntil(Date.now() + duration);
  };

  const canShowPrompts = !suppressedUntil || Date.now() > suppressedUntil;

  // Auto-check milestones periodically
  useEffect(() => {
    const interval = setInterval(triggerMilestoneCheck, 60000); // Check every minute
    return () => clearInterval(interval);
  }, []);

  const contextValue: PWAPromptContextType = {
    triggerMilestoneCheck,
    suppressPrompts,
    canShowPrompts
  };

  return (
    <PWAPromptContext.Provider value={contextValue}>
      {children}
    </PWAPromptContext.Provider>
  );
};

export const usePWAPromptContext = () => {
  const context = React.useContext(PWAPromptContext);
  if (!context) {
    throw new Error('usePWAPromptContext must be used within PWAPromptProvider');
  }
  return context;
};

/**
 * PWA Installation Success Celebration
 * Shows a brief celebration when PWA is successfully installed
 */
export const PWAInstallationSuccess: React.FC = () => {
  const { isInstalled } = usePWA();
  const [showCelebration, setShowCelebration] = useState(false);
  const [wasInstalled, setWasInstalled] = useState(isInstalled);

  useEffect(() => {
    // Show celebration when PWA gets installed
    if (isInstalled && !wasInstalled) {
      setShowCelebration(true);
      setWasInstalled(true);
      
      // Hide celebration after 5 seconds
      const timer = setTimeout(() => {
        setShowCelebration(false);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [isInstalled, wasInstalled]);

  if (!showCelebration) {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 right-4 z-50 animate-slide-down md:left-auto md:right-4 md:max-w-sm">
      <div className="bg-jobblogg-success text-white rounded-xl shadow-lg p-4">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <div>
            <h3 className="font-semibold text-sm">🎉 JobbLogg installert!</h3>
            <p className="text-white/90 text-xs">
              Du finner appen på hjemskjermen din. Takk for at du bruker JobbLogg!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * PWA Feature Highlight
 * Highlights PWA features after installation or for engaged users
 */
export const PWAFeatureHighlight: React.FC<{
  feature: 'offline' | 'notifications' | 'homescreen' | 'performance';
  onDismiss?: () => void;
}> = ({ feature, onDismiss }) => {
  const { isInstalled } = usePWA();

  if (!isInstalled) {
    return null;
  }

  const features = {
    offline: {
      icon: '🌐',
      title: 'Fungerer offline',
      description: 'JobbLogg fungerer nå selv uten internettforbindelse på byggeplassen!'
    },
    notifications: {
      icon: '🔔',
      title: 'Push-varsler aktivert',
      description: 'Du får nå beskjed med en gang når det skjer noe nytt i prosjektene dine.'
    },
    homescreen: {
      icon: '📱',
      title: 'Rask tilgang',
      description: 'Åpne JobbLogg direkte fra hjemskjermen for raskere tilgang.'
    },
    performance: {
      icon: '⚡',
      title: 'Bedre ytelse',
      description: 'Appen laster nå raskere og bruker mindre data.'
    }
  };

  const currentFeature = features[feature];

  return (
    <div className="fixed bottom-4 left-4 right-4 z-40 animate-slide-up md:left-auto md:right-4 md:max-w-sm">
      <div className="bg-white border border-jobblogg-primary/20 rounded-xl shadow-lg p-4">
        <div className="flex items-start gap-3">
          <div className="text-2xl">{currentFeature.icon}</div>
          <div className="flex-1">
            <h3 className="font-semibold text-jobblogg-text-strong text-sm mb-1">
              {currentFeature.title}
            </h3>
            <p className="text-jobblogg-text-medium text-xs mb-3">
              {currentFeature.description}
            </p>
            <button
              onClick={onDismiss}
              className="text-jobblogg-primary hover:text-jobblogg-primary-dark text-xs font-medium"
            >
              Flott! 👍
            </button>
          </div>
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="w-6 h-6 text-jobblogg-text-muted hover:text-jobblogg-text-medium transition-colors rounded-full hover:bg-jobblogg-neutral flex items-center justify-center"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * PWA Installation Analytics Hook
 * Tracks PWA installation funnel for analytics
 */
export const usePWAAnalytics = () => {
  const { isInstalled, shouldShowInstallPrompt } = usePWA();
  const { getMilestoneStats } = usePWAMilestones();

  const trackPromptShown = (type: 'regular' | 'milestone', milestone?: string) => {
    console.log('[PWA Analytics] Prompt shown:', { type, milestone });
    // TODO: Send to analytics service
  };

  const trackPromptDismissed = (type: 'regular' | 'milestone', isPermanent: boolean) => {
    console.log('[PWA Analytics] Prompt dismissed:', { type, isPermanent });
    // TODO: Send to analytics service
  };

  const trackInstallationStarted = () => {
    console.log('[PWA Analytics] Installation started');
    // TODO: Send to analytics service
  };

  const trackInstallationCompleted = () => {
    console.log('[PWA Analytics] Installation completed');
    // TODO: Send to analytics service
  };

  const getAnalyticsData = () => {
    const milestoneStats = getMilestoneStats();
    return {
      isInstalled,
      canShowPrompts: shouldShowInstallPrompt,
      milestoneStats,
      timestamp: Date.now()
    };
  };

  return {
    trackPromptShown,
    trackPromptDismissed,
    trackInstallationStarted,
    trackInstallationCompleted,
    getAnalyticsData
  };
};
