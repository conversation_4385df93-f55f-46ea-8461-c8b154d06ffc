/**
 * PWA Components Barrel Export
 * Centralized exports for all PWA-related components
 */

export { DashboardPWACard, PWAInstallButton, PWAStatusIndicator } from './DashboardPWACard';
export { PWAValueProposition, PWAInstallInstructions } from './PWAValueProposition';
export {
  SmartPWAPromptManager,
  PWAPromptProvider,
  usePWAPromptContext,
  PWAInstallationSuccess,
  PWAFeatureHighlight,
  usePWAAnalytics
} from './SmartPWAPromptManager';

// Re-export enhanced PWA banner from parent directory
export { PWAInstallBanner } from '../PWAInstallBanner';

// Re-export PWA utilities and preferences
export { getPWAPreferencesManager, PWAPreferences } from '../../utils/pwaPreferences';
export type { PWAPreferences as PWAPreferencesType } from '../../utils/pwaPreferences';

// Re-export PWA milestone hooks
export { usePWAMilestones, usePWAProjectMilestones, usePWAMilestoneBanner } from '../../hooks/usePWAMilestones';
