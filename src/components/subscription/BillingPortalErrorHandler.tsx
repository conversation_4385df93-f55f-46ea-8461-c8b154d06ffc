import React, { useState } from 'react';
import { PrimaryButton, SecondaryButton } from '../ui/Button';
import { Modal } from '../ui/Dialog';
import { Heading3 } from '../ui/Typography';

export interface BillingPortalError {
  type: 'session_creation' | 'session_expired' | 'network_error' | 'access_denied' | 'unknown';
  message: string;
  details?: string;
  retryable: boolean;
}

export interface BillingPortalErrorHandlerProps {
  error: BillingPortalError | null;
  onRetry: () => void;
  onClose: () => void;
  onContactSupport: () => void;
  isRetrying?: boolean;
}

export const BillingPortalErrorHandler: React.FC<BillingPortalErrorHandlerProps> = ({
  error,
  onRetry,
  onClose,
  onContactSupport,
  isRetrying = false
}) => {
  if (!error) return null;

  const getErrorContent = (error: BillingPortalError) => {
    switch (error.type) {
      case 'session_creation':
        return {
          title: 'Kunne ikke åpne faktureringsportal',
          description: 'Vi kunne ikke opprette en sikker økt til faktureringsportalen. Dette kan skyldes midlertidige tekniske problemer.',
          icon: '🔒',
          suggestions: [
            'Prøv igjen om et øyeblikk',
            'Sjekk internettforbindelsen din',
            'Kontakt support hvis problemet vedvarer'
          ]
        };

      case 'session_expired':
        return {
          title: 'Økten har utløpt',
          description: 'Din økt til faktureringsportalen har utløpt av sikkerhetshensyn. Du må starte en ny økt.',
          icon: '⏰',
          suggestions: [
            'Klikk "Prøv igjen" for å starte en ny økt',
            'Sørg for at du fullfører endringene raskt',
            'Kontakt support hvis du trenger hjelp'
          ]
        };

      case 'network_error':
        return {
          title: 'Nettverksfeil',
          description: 'Vi kunne ikke koble til faktureringsportalen på grunn av nettverksproblemer.',
          icon: '🌐',
          suggestions: [
            'Sjekk internettforbindelsen din',
            'Prøv å oppdatere siden',
            'Prøv igjen om noen minutter'
          ]
        };

      case 'access_denied':
        return {
          title: 'Tilgang nektet',
          description: 'Du har ikke tilgang til faktureringsportalen. Dette kan skyldes at abonnementet ditt er inaktivt eller at du ikke har nødvendige rettigheter.',
          icon: '🚫',
          suggestions: [
            'Kontroller at abonnementet ditt er aktivt',
            'Kontakt administratoren hvis du er teammedlem',
            'Kontakt support for hjelp med tilgang'
          ]
        };

      default:
        return {
          title: 'Ukjent feil',
          description: 'Det oppstod en uventet feil ved åpning av faktureringsportalen.',
          icon: '❌',
          suggestions: [
            'Prøv å oppdatere siden',
            'Prøv igjen om noen minutter',
            'Kontakt support hvis problemet vedvarer'
          ]
        };
    }
  };

  const errorContent = getErrorContent(error);

  return (
    <Modal isOpen={true} onClose={onClose} size="md">
      <div className="space-y-6">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-jobblogg-error-light rounded-full flex items-center justify-center mx-auto">
            <span className="text-3xl">{errorContent.icon}</span>
          </div>
          
          <div>
            <Heading3 className="mb-2">{errorContent.title}</Heading3>
            <p className="text-jobblogg-text-muted">
              {errorContent.description}
            </p>
          </div>
        </div>

        {/* Error Details */}
        {error.details && (
          <div className="bg-jobblogg-card-bg rounded-lg p-4">
            <h4 className="font-medium text-jobblogg-text mb-2">Tekniske detaljer:</h4>
            <p className="text-sm text-jobblogg-text-muted font-mono">
              {error.details}
            </p>
          </div>
        )}

        {/* Suggestions */}
        <div className="space-y-3">
          <h4 className="font-medium text-jobblogg-text">Hva kan du gjøre?</h4>
          <ul className="space-y-2">
            {errorContent.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start space-x-2 text-sm">
                <span className="w-1.5 h-1.5 bg-jobblogg-primary rounded-full mt-2 flex-shrink-0"></span>
                <span className="text-jobblogg-text-muted">{suggestion}</span>
              </li>
            ))}
          </ul>
        </div>

        {/* Alternative Actions */}
        <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-lg p-4">
          <h4 className="font-medium text-jobblogg-text mb-2">Alternative løsninger</h4>
          <div className="space-y-2 text-sm text-jobblogg-text-muted">
            <p>• Du kan administrere abonnementet ditt direkt i JobbLogg</p>
            <p>• Kontakt support for manuell hjelp med faktureringsendringer</p>
            <p>• Sjekk fakturaer og betalingshistorikk i din e-post</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <SecondaryButton onClick={onClose} className="flex-1">
            Lukk
          </SecondaryButton>
          
          {error.retryable && (
            <PrimaryButton
              onClick={onRetry}
              disabled={isRetrying}
              className="flex-1"
            >
              {isRetrying ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Prøver igjen...</span>
                </div>
              ) : (
                'Prøv igjen'
              )}
            </PrimaryButton>
          )}
          
          <SecondaryButton onClick={onContactSupport} className="flex-1">
            Kontakt support
          </SecondaryButton>
        </div>

        {/* Support Information */}
        <div className="text-center text-xs text-jobblogg-text-muted">
          <p>
            Trenger du hjelp? Kontakt oss på{' '}
            <a href="mailto:<EMAIL>" className="text-jobblogg-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </Modal>
  );
};

/**
 * Hook for managing billing portal errors
 */
export function useBillingPortalErrorHandler() {
  const [error, setError] = useState<BillingPortalError | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);

  const handleError = (error: any, context?: string) => {
    console.error('Billing portal error:', error, context);

    let portalError: BillingPortalError;

    if (error?.message?.includes('session')) {
      if (error.message.includes('expired')) {
        portalError = {
          type: 'session_expired',
          message: 'Økten har utløpt',
          details: error.message,
          retryable: true,
        };
      } else {
        portalError = {
          type: 'session_creation',
          message: 'Kunne ikke opprette portal-økt',
          details: error.message,
          retryable: true,
        };
      }
    } else if (error?.message?.includes('network') || error?.message?.includes('fetch')) {
      portalError = {
        type: 'network_error',
        message: 'Nettverksfeil',
        details: error.message,
        retryable: true,
      };
    } else if (error?.message?.includes('access') || error?.message?.includes('permission')) {
      portalError = {
        type: 'access_denied',
        message: 'Tilgang nektet',
        details: error.message,
        retryable: false,
      };
    } else {
      portalError = {
        type: 'unknown',
        message: 'Ukjent feil',
        details: error?.message || 'Ingen detaljer tilgjengelig',
        retryable: true,
      };
    }

    setError(portalError);
  };

  const clearError = () => {
    setError(null);
    setIsRetrying(false);
  };

  const retry = async (retryFunction: () => Promise<void>) => {
    if (!error?.retryable) return;

    setIsRetrying(true);
    try {
      await retryFunction();
      clearError();
    } catch (newError) {
      handleError(newError, 'retry');
    } finally {
      setIsRetrying(false);
    }
  };

  const contactSupport = () => {
    const subject = encodeURIComponent('Faktureringsportal - Teknisk problem');
    const body = encodeURIComponent(`
Hei,

Jeg opplever problemer med faktureringsportalen:

Feiltype: ${error?.type}
Feilmelding: ${error?.message}
Detaljer: ${error?.details || 'Ingen detaljer'}

Takk for hjelpen!
    `.trim());

    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  return {
    error,
    isRetrying,
    handleError,
    clearError,
    retry,
    contactSupport,
  };
}

/**
 * Utility function to create standardized portal errors
 */
export function createPortalError(
  type: BillingPortalError['type'],
  message: string,
  details?: string,
  retryable = true
): BillingPortalError {
  return {
    type,
    message,
    details,
    retryable,
  };
}
