import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { MobileUpgradeBottomSheet } from './MobileUpgradeBottomSheet';
import { useIsMobile, useDeviceCapabilities } from '../../hooks/useResponsive';

interface DisabledButtonProps {
  children: React.ReactNode;
  feature: 'create_project' | 'team_management' | 'project_sharing' | 'file_upload' | 'full_access';
  reason?: 'trial_expired' | 'grace_period' | 'no_subscription';
  className?: string;
  showTooltip?: boolean;
  tooltipPosition?: 'top' | 'bottom' | 'left' | 'right';
}

const FEATURE_MESSAGES = {
  create_project: 'Opprett nye prosjekter',
  team_management: 'Administrer team',
  project_sharing: 'Del prosjekter',
  file_upload: 'Last opp filer',
  full_access: 'Full tilgang'
};

export const DisabledButton: React.FC<DisabledButtonProps> = ({
  children,
  feature,
  reason = 'trial_expired',
  className = '',
  showTooltip = true,
  tooltipPosition = 'top'
}) => {
  const { user } = useUser();
  const [showTooltip_, setShowTooltip_] = useState(false);
  const [showMobileBottomSheet, setShowMobileBottomSheet] = useState(false);
  const isMobile = useIsMobile();
  const { hasVibration } = useDeviceCapabilities();

  const handleUpgrade = () => {
    if (!user) return;
    window.location.href = '/subscription';
  };

  const handleClick = () => {
    if (isMobile) {
      setShowMobileBottomSheet(true);
      // Haptic feedback on mobile
      if (hasVibration) {
        navigator.vibrate(50);
      }
    } else {
      handleUpgrade();
    }
  };

  const getReasonText = () => {
    switch (reason) {
      case 'trial_expired':
        return 'Prøveperioden er utløpt';
      case 'grace_period':
        return 'Begrenset tilgang';
      case 'no_subscription':
        return 'Krever abonnement';
      default:
        return 'Ikke tilgjengelig';
    }
  };

  const getTooltipPosition = () => {
    const positions = {
      top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
      bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
      left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
      right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
    };
    return positions[tooltipPosition];
  };

  const getArrowPosition = () => {
    const arrows = {
      top: 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-800',
      bottom: 'bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-800',
      left: 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-gray-800',
      right: 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-gray-800'
    };
    return arrows[tooltipPosition];
  };

  return (
    <>
      <div
        className={`relative inline-block ${className}`}
        onMouseEnter={() => !isMobile && showTooltip && setShowTooltip_(true)}
        onMouseLeave={() => !isMobile && setShowTooltip_(false)}
        onClick={handleClick}
      >
        {/* Disabled button */}
        <div className="relative">
          <div className="opacity-50 pointer-events-none select-none">
            {children}
          </div>

          {/* Lock icon overlay with touch target */}
          <div className="absolute inset-0 flex items-center justify-center">
            {/* Invisible touch target for mobile - minimum 44px */}
            <div className="absolute inset-0 min-w-[44px] min-h-[44px] cursor-pointer" />

            {/* Visible lock icon */}
            <div className="bg-white/90 rounded-full p-1 shadow-sm pointer-events-none">
              <svg className="w-3 h-3 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* Desktop tooltip - only show on non-mobile */}
        {!isMobile && showTooltip && showTooltip_ && (
          <div className={`absolute z-50 ${getTooltipPosition()}`}>
            <div className="bg-gray-800 text-white text-xs rounded-lg px-3 py-2 max-w-xs shadow-lg">
              <div className="font-medium mb-1">{FEATURE_MESSAGES[feature]}</div>
              <div className="text-gray-300 mb-2">{getReasonText()}</div>
              <button
                onClick={handleUpgrade}
                className="inline-flex items-center gap-1 text-xs bg-jobblogg-primary hover:bg-jobblogg-primary-dark px-2 py-1 rounded transition-colors duration-200 min-h-[32px]"
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                Oppgrader
              </button>
            </div>
            {/* Tooltip arrow */}
            <div className={`absolute w-0 h-0 border-4 ${getArrowPosition()}`}></div>
          </div>
        )}
      </div>

      {/* Mobile bottom sheet */}
      <MobileUpgradeBottomSheet
        isOpen={showMobileBottomSheet}
        onClose={() => setShowMobileBottomSheet(false)}
        feature={feature}
        reason={reason}
      />
    </>
  );
};
