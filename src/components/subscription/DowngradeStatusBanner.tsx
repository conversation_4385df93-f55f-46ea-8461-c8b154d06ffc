import React from 'react';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { PrimaryButton } from '../ui/Button';
import { Card } from '../ui/Card';

interface DowngradeStatusBannerProps {
  className?: string;
}

export const DowngradeStatusBanner: React.FC<DowngradeStatusBannerProps> = ({
  className = ''
}) => {
  const { 
    isInDowngrade, 
    downgradeStatus, 
    subscription,
    isLoading 
  } = useSubscriptionAccess();

  // Don't show banner if not in downgrade or still loading
  if (isLoading || !isInDowngrade || !downgradeStatus) {
    return null;
  }

  const getStageInfo = (stage: number) => {
    switch (stage) {
      case 1:
        return {
          title: 'Betalingsproblem - Nådeperiode',
          description: 'Du har full tilgang mens vi løser betalingsproblemet.',
          color: 'bg-jobblogg-warning-light border-jobblogg-warning',
          textColor: 'text-jobblogg-warning-dark',
          icon: '⚠️',
          urgency: 'medium'
        };
      case 2:
        return {
          title: 'Begrenset tilgang',
          description: 'Kontoen din har begrenset tilgang på grunn av utestående betaling.',
          color: 'bg-jobblogg-error-light border-jobblogg-error',
          textColor: 'text-jobblogg-error-dark',
          icon: '🔒',
          urgency: 'high'
        };
      case 3:
        return {
          title: 'Konto suspendert',
          description: 'Kontoen din er suspendert. Du kan kun se eksisterende data.',
          color: 'bg-jobblogg-error-light border-jobblogg-error',
          textColor: 'text-jobblogg-error-dark',
          icon: '🚫',
          urgency: 'critical'
        };
      default:
        return {
          title: 'Betalingsproblem',
          description: 'Det er et problem med betalingen din.',
          color: 'bg-jobblogg-warning-light border-jobblogg-warning',
          textColor: 'text-jobblogg-warning-dark',
          icon: '⚠️',
          urgency: 'medium'
        };
    }
  };

  const stageInfo = getStageInfo(downgradeStatus.currentStage);
  const daysUntilEscalation = downgradeStatus.daysUntilEscalation;

  const handleResolvePayment = () => {
    // Navigate to subscription management
    window.location.href = '/subscription';
  };

  return (
    <Card className={`${stageInfo.color} border-2 ${className}`}>
      <div className="p-4 sm:p-6">
        <div className="flex items-start space-x-4">
          <div className="text-2xl flex-shrink-0">
            {stageInfo.icon}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div className="flex-1">
                <h3 className={`text-lg font-semibold ${stageInfo.textColor}`}>
                  {stageInfo.title}
                </h3>
                <p className={`mt-1 text-sm ${stageInfo.textColor} opacity-90`}>
                  {stageInfo.description}
                </p>
                
                {daysUntilEscalation !== null && daysUntilEscalation > 0 && (
                  <p className={`mt-2 text-sm font-medium ${stageInfo.textColor}`}>
                    {daysUntilEscalation === 1 
                      ? 'Ytterligere begrensninger i morgen'
                      : `Ytterligere begrensninger om ${daysUntilEscalation} dager`
                    }
                  </p>
                )}
              </div>
              
              <div className="mt-4 sm:mt-0 sm:ml-4 flex-shrink-0">
                <PrimaryButton
                  onClick={handleResolvePayment}
                  className="w-full sm:w-auto text-sm px-4 py-2"
                >
                  Løs betalingsproblem
                </PrimaryButton>
              </div>
            </div>

            {/* Current restrictions */}
            {downgradeStatus.restrictions && (
              <div className="mt-4 pt-4 border-t border-current opacity-20">
                <h4 className={`text-sm font-medium ${stageInfo.textColor} mb-2`}>
                  Gjeldende begrensninger:
                </h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                  {!downgradeStatus.restrictions.canCreateProjects && (
                    <div className={`${stageInfo.textColor} opacity-75`}>
                      • Kan ikke opprette nye prosjekter
                    </div>
                  )}
                  {!downgradeStatus.restrictions.canUploadFiles && (
                    <div className={`${stageInfo.textColor} opacity-75`}>
                      • Kan ikke laste opp filer
                    </div>
                  )}
                  {!downgradeStatus.restrictions.canInviteTeamMembers && (
                    <div className={`${stageInfo.textColor} opacity-75`}>
                      • Kan ikke invitere teammedlemmer
                    </div>
                  )}
                  {!downgradeStatus.restrictions.canShareProjects && (
                    <div className={`${stageInfo.textColor} opacity-75`}>
                      • Kan ikke dele prosjekter
                    </div>
                  )}
                  {downgradeStatus.restrictions.canAccessProjects && (
                    <div className={`${stageInfo.textColor} opacity-75`}>
                      • Kan se eksisterende prosjekter
                    </div>
                  )}
                  {downgradeStatus.restrictions.canExportData && (
                    <div className={`${stageInfo.textColor} opacity-75`}>
                      • Kan eksportere data
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

// Compact version for use in navigation or smaller spaces
export const DowngradeStatusIndicator: React.FC<{ className?: string }> = ({
  className = ''
}) => {
  const { 
    isInDowngrade, 
    downgradeStatus,
    isLoading 
  } = useSubscriptionAccess();

  if (isLoading || !isInDowngrade || !downgradeStatus) {
    return null;
  }

  const getStageColor = (stage: number) => {
    switch (stage) {
      case 1:
        return 'bg-jobblogg-warning text-white';
      case 2:
      case 3:
        return 'bg-jobblogg-error text-white';
      default:
        return 'bg-jobblogg-warning text-white';
    }
  };

  const getStageText = (stage: number) => {
    switch (stage) {
      case 1:
        return 'Betalingsproblem';
      case 2:
        return 'Begrenset tilgang';
      case 3:
        return 'Suspendert';
      default:
        return 'Betalingsproblem';
    }
  };

  return (
    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStageColor(downgradeStatus.currentStage)} ${className}`}>
      <div className="w-2 h-2 bg-current rounded-full mr-2 animate-pulse"></div>
      {getStageText(downgradeStatus.currentStage)}
    </div>
  );
};
