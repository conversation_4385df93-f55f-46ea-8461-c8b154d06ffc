import { useUser } from '@clerk/clerk-react';
import { useMutation, useQuery } from 'convex/react';
import React, { useEffect, useState } from 'react';
import { api } from '../../../convex/_generated/api';
import { PrimaryButton } from '../ui/Button';
import { Heading3 } from '../ui/Typography';

export interface EnhancedBillingPortalProps {
  className?: string;
  defaultFlow?: 'subscription_cancel' | 'subscription_update' | 'payment_method_update' | 'invoice_history';
  returnUrl?: string;
  onPortalOpen?: () => void;
  onPortalReturn?: (flow?: string) => void;
}

export const EnhancedBillingPortal: React.FC<EnhancedBillingPortalProps> = ({
  className = '',
  defaultFlow,
  returnUrl,
  onPortalOpen,
  onPortalReturn
}) => {
  const { user } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFlow, setSelectedFlow] = useState<string | undefined>(defaultFlow);

  const createPortalSession = useMutation(api.billingPortal?.createEnhancedPortalSession);
  const handlePortalReturn = useMutation(api.billingPortal?.handlePortalReturn);
  const portalHistory = useQuery(
    api.billingPortal?.getPortalSessionHistory,
    user ? { userId: user.id } : "skip"
  );

  // Handle portal return from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const portalReturn = urlParams.get('portal_return');
    const flow = urlParams.get('flow');
    const sessionId = urlParams.get('session_id');

    if (portalReturn === 'true' && user) {
      handlePortalReturnFromUrl(flow, sessionId);
    }
  }, [user]);

  const handlePortalReturnFromUrl = async (flow: string | null, sessionId: string | null) => {
    if (!user) return;

    try {
      await handlePortalReturn({
        userId: user.id,
        sessionId: sessionId || undefined,
        flow: flow || undefined,
      });

      // Clean up URL parameters
      const url = new URL(window.location.href);
      url.searchParams.delete('portal_return');
      url.searchParams.delete('flow');
      url.searchParams.delete('session_id');
      window.history.replaceState({}, '', url.toString());

      // Notify parent component
      onPortalReturn?.(flow || undefined);

      // Show success message based on flow
      if (flow) {
        showFlowSuccessMessage(flow);
      }
    } catch (error) {
      console.error('Failed to handle portal return:', error);
    }
  };

  const showFlowSuccessMessage = (flow: string) => {
    const messages = {
      subscription_cancel: 'Abonnementet ditt er avbrutt.',
      subscription_update: 'Abonnementet ditt er oppdatert.',
      payment_method_update: 'Betalingsmetoden din er oppdatert.',
      invoice_history: 'Fakturahistorikk er tilgjengelig.',
    };

    const message = messages[flow as keyof typeof messages] || 'Endringene er lagret.';
    alert(message); // In a real app, use a proper notification system
  };

  const openPortal = async (flow?: string) => {
    if (!user || !api.billingPortal?.createEnhancedPortalSession) return;

    setIsLoading(true);
    setError(null);

    try {
      const flowData = flow ? {
        type: flow as any,
        after_completion: {
          type: 'redirect' as const,
          redirect: {
            return_url: returnUrl || `${window.location.origin}/subscription?portal_return=true&flow=${flow}`,
          },
        },
      } : undefined;

      const result = await createPortalSession({
        userId: user.id,
        returnUrl: returnUrl || `${window.location.origin}/subscription?portal_return=true`,
        locale: 'nb',
        flowData,
      });

      onPortalOpen?.();

      // Poll for portal URL
      const pollForPortalUrl = async (requestId: string, attempts = 0) => {
        if (attempts > 10) {
          throw new Error('Portal-økt tok for lang tid å opprette');
        }

        // Wait a bit before checking
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In a real implementation, you'd query the portal request status
        // For now, we'll simulate the portal opening
        console.log('Portal session initiated:', result);
        
        // Simulate portal URL being ready
        if (attempts > 2) {
          // In reality, this would be the actual Stripe portal URL
          const mockPortalUrl = `https://billing.stripe.com/session/mock_${requestId}`;
          window.location.href = mockPortalUrl;
          return;
        }

        return pollForPortalUrl(requestId, attempts + 1);
      };

      if (result.portalRequestId) {
        await pollForPortalUrl(result.portalRequestId);
      }

    } catch (error) {
      console.error('Failed to open billing portal:', error);
      setError(error instanceof Error ? error.message : 'Kunne ikke åpne faktureringsportal');
    } finally {
      setIsLoading(false);
    }
  };

  const portalOptions = [
    {
      id: 'subscription_update',
      title: 'Endre abonnement',
      description: 'Oppgrader, nedgrader eller endre faktureringsperiode',
      icon: '📊',
    },
    {
      id: 'payment_method_update',
      title: 'Oppdater betalingsmetode',
      description: 'Legg til, fjern eller endre betalingskort',
      icon: '💳',
    },
    {
      id: 'invoice_history',
      title: 'Fakturahistorikk',
      description: 'Se og last ned tidligere fakturaer',
      icon: '📄',
    },
    {
      id: 'subscription_cancel',
      title: 'Avbryt abonnement',
      description: 'Avbryt abonnementet ditt',
      icon: '❌',
    },
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center space-y-2">
        <Heading3>Administrer fakturering</Heading3>
        <p className="text-jobblogg-text-muted">
          Administrer abonnement, betalingsmetoder og fakturaer i Stripe sin sikre portal
        </p>
      </div>

      {error && (
        <div className="bg-jobblogg-error-light border border-jobblogg-error rounded-lg p-4">
          <p className="text-jobblogg-error text-sm">{error}</p>
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {portalOptions.map((option) => (
          <button
            key={option.id}
            onClick={() => openPortal(option.id)}
            disabled={isLoading}
            className={`p-4 border border-jobblogg-border rounded-lg text-left hover:border-jobblogg-primary-light hover:bg-jobblogg-primary-light transition-colors ${
              selectedFlow === option.id ? 'border-jobblogg-primary bg-jobblogg-primary-light' : ''
            } ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            <div className="flex items-start space-x-3">
              <div className="text-2xl">{option.icon}</div>
              <div className="flex-1">
                <h4 className="font-medium text-jobblogg-text mb-1">{option.title}</h4>
                <p className="text-sm text-jobblogg-text-muted">{option.description}</p>
              </div>
            </div>
          </button>
        ))}
      </div>

      {/* General Portal Access */}
      <div className="text-center space-y-4">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-jobblogg-border"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-jobblogg-text-muted">eller</span>
          </div>
        </div>

        <PrimaryButton
          onClick={() => openPortal()}
          disabled={isLoading}
          className="w-full sm:w-auto"
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Åpner portal...</span>
            </div>
          ) : (
            'Åpne fullstendig faktureringsportal'
          )}
        </PrimaryButton>
      </div>

      {/* Portal Information */}
      <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-lg p-4">
        <h4 className="font-medium text-jobblogg-text mb-2">ℹ️ Om faktureringsportalen</h4>
        <ul className="text-sm text-jobblogg-text-muted space-y-1">
          <li>• Sikker portal levert av Stripe, vår betalingsleverandør</li>
          <li>• Alle endringer synkroniseres automatisk med JobbLogg</li>
          <li>• Du kan trygt oppdatere betalingsinformasjon og abonnementsdetaljer</li>
          <li>• Fakturaer og kvitteringer er tilgjengelige for nedlasting</li>
        </ul>
      </div>

      {/* Recent Portal Activity */}
      {portalHistory && portalHistory.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-jobblogg-text">Nylig aktivitet</h4>
          <div className="space-y-2">
            {portalHistory.slice(0, 3).map((session: any) => (
              <div key={session._id} className="flex items-center justify-between p-3 bg-jobblogg-card-bg rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    session.status === 'completed' ? 'bg-jobblogg-success' :
                    session.status === 'failed' ? 'bg-jobblogg-error' :
                    'bg-jobblogg-warning'
                  }`}></div>
                  <div>
                    <p className="text-sm font-medium text-jobblogg-text">
                      {session.requestedFlow ? getFlowDisplayName(session.requestedFlow) : 'Generell portal'}
                    </p>
                    <p className="text-xs text-jobblogg-text-muted">
                      {new Date(session.createdAt).toLocaleDateString('nb-NO')}
                    </p>
                  </div>
                </div>
                <div className={`text-xs px-2 py-1 rounded-full ${
                  session.status === 'completed' ? 'bg-jobblogg-success-light text-jobblogg-success' :
                  session.status === 'failed' ? 'bg-jobblogg-error-light text-jobblogg-error' :
                  'bg-jobblogg-warning-light text-jobblogg-warning'
                }`}>
                  {getStatusDisplayName(session.status)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

function getFlowDisplayName(flow: string): string {
  const flowNames = {
    subscription_update: 'Abonnementsendring',
    payment_method_update: 'Betalingsmetode',
    invoice_history: 'Fakturahistorikk',
    subscription_cancel: 'Avbryting',
  };
  return flowNames[flow as keyof typeof flowNames] || flow;
}

function getStatusDisplayName(status: string): string {
  const statusNames = {
    pending: 'Venter',
    processing: 'Behandler',
    completed: 'Fullført',
    failed: 'Feilet',
  };
  return statusNames[status as keyof typeof statusNames] || status;
}
