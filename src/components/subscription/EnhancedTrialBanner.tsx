import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { formatTimeRemaining, getTimeRemaining } from '../../utils/timeFormatting';

interface EnhancedTrialBannerProps {
  variant?: 'compact' | 'card' | 'smart';
  onDismiss?: () => void;
  showDismiss?: boolean;
  className?: string;
}

/**
 * Enhanced Trial Banner with three display variants:
 * - compact: Minimal progress bar with countdown
 * - card: Detailed card with benefits and actions
 * - smart: Adaptive banner that changes based on remaining time
 */
export const EnhancedTrialBanner: React.FC<EnhancedTrialBannerProps> = ({
  variant = 'smart',
  onDismiss,
  showDismiss = false,
  className = ''
}) => {
  const { subscription, isInTrial, isTrialExpired, isLoading } = useSubscriptionAccess();
  const { isAdministrator } = useUserRole();
  const [isDismissed, setIsDismissed] = useState(false);
  const [currentTime, setCurrentTime] = useState(Date.now());

  // Update time every minute for live countdown
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  // Don't show if loading, dismissed, or no subscription
  if (isLoading || isDismissed || !subscription) return null;

  // Only show during trial period
  if (!isInTrial || isTrialExpired) return null;

  const trialEnd = (subscription as any)?.trialEnd || 0;
  const trialStart = (subscription as any)?.trialStart || 0;
  const timeRemaining = formatTimeRemaining(trialEnd);
  const timeData = getTimeRemaining(trialEnd);
  
  // Calculate progress (0-100%)
  const totalDuration = trialEnd - trialStart;
  const elapsed = currentTime - trialStart;
  const progressPercent = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  const getPlanDisplayName = () => {
    const plan = (subscription as any)?.planLevel;
    switch(plan) {
      case 'basic': return 'Liten bedrift';
      case 'professional': return 'Mellomstor bedrift';
      case 'enterprise': return 'Stor bedrift';
      default: return 'Valgt plan';
    }
  };

  // Compact Variant - Minimal progress bar
  if (variant === 'compact') {
    return (
      <div className={`bg-gradient-to-r from-jobblogg-primary to-jobblogg-primary/80 text-white rounded-lg shadow-sm ${className}`}>
        <div className="px-4 py-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">
                Prøveperiode: {timeRemaining.text}
              </span>
            </div>
            {showDismiss && (
              <button
                onClick={handleDismiss}
                className="text-white/80 hover:text-white transition-colors p-1"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          
          {/* Progress bar */}
          <div className="w-full bg-white/20 rounded-full h-1.5">
            <div 
              className="bg-white h-1.5 rounded-full transition-all duration-1000 ease-out"
              style={{ width: `${progressPercent}%` }}
            />
          </div>
          
          <div className="flex justify-between text-xs text-white/80 mt-1">
            <span>Dag {Math.ceil(progressPercent / (100/7))}/7</span>
            <Link to="/subscription" className="hover:text-white underline">
              Se planer
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Card Variant - Detailed information card
  if (variant === 'card') {
    return (
      <div className={`bg-white border border-jobblogg-border rounded-xl shadow-sm overflow-hidden ${className}`}>
        <div className="bg-gradient-to-r from-jobblogg-primary to-jobblogg-primary/80 px-6 py-4">
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold">Prøveperioden er aktiv!</h3>
                <p className="text-sm text-white/90">
                  {getPlanDisplayName()} • {timeRemaining.text}
                </p>
              </div>
            </div>
            {showDismiss && (
              <button
                onClick={handleDismiss}
                className="text-white/80 hover:text-white transition-colors p-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
        
        <div className="p-6">
          {/* Progress section */}
          <div className="mb-4">
            <div className="flex justify-between text-sm text-jobblogg-text-medium mb-2">
              <span>Fremgang</span>
              <span>{Math.round(progressPercent)}% fullført</span>
            </div>
            <div className="w-full bg-jobblogg-surface rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-jobblogg-primary to-jobblogg-accent h-2 rounded-full transition-all duration-1000 ease-out"
                style={{ width: `${progressPercent}%` }}
              />
            </div>
          </div>

          {/* Benefits reminder */}
          <div className="bg-jobblogg-surface rounded-lg p-4 mb-4">
            <h4 className="font-medium text-jobblogg-text-strong mb-2">Hva du kan gjøre:</h4>
            <div className="grid grid-cols-2 gap-2 text-sm text-jobblogg-text-medium">
              <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-jobblogg-success rounded-full"></div>
                Ubegrenset prosjekter
              </div>
              <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-jobblogg-success rounded-full"></div>
                Chat med kunder
              </div>
              <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-jobblogg-success rounded-full"></div>
                Team samarbeid
              </div>
              <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-jobblogg-success rounded-full"></div>
                Alle funksjoner
              </div>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-3">
            <Link
              to="/subscription"
              className="flex-1 bg-jobblogg-primary text-white px-4 py-2 rounded-lg text-center text-sm font-medium hover:bg-jobblogg-primary/90 transition-colors"
            >
              Velg abonnement
            </Link>
            <Link
              to="/create"
              className="flex-1 border border-jobblogg-border text-jobblogg-text-medium px-4 py-2 rounded-lg text-center text-sm font-medium hover:bg-jobblogg-surface transition-colors"
            >
              Opprett prosjekt
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Smart Variant - Adaptive banner based on time remaining
  const getSmartVariantContent = () => {
    const { days, hours, isLessThanDay, isLessThanHour } = timeData;
    
    // Critical phase: Less than 1 hour
    if (isLessThanHour) {
      return {
        bgColor: 'bg-gradient-to-r from-red-500 to-red-600',
        textColor: 'text-white',
        urgency: 'critical',
        icon: (
          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center animate-pulse">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        ),
        title: '⏰ Prøveperioden utløper snart!',
        message: `Kun ${timeRemaining.text} - oppgrader nå for å fortsette`,
        actionText: 'Oppgrader nå!'
      };
    }
    
    // Urgent phase: Less than 1 day
    if (isLessThanDay) {
      return {
        bgColor: 'bg-gradient-to-r from-jobblogg-warning to-amber-500',
        textColor: 'text-white',
        urgency: 'urgent',
        icon: (
          <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        ),
        title: `${timeRemaining.text} av prøveperioden`,
        message: 'Få tilgang til alle funksjoner etter prøveperioden',
        actionText: 'Se planer'
      };
    }
    
    // Early phase: More than 1 day
    return {
      bgColor: 'bg-gradient-to-r from-jobblogg-primary to-jobblogg-accent',
      textColor: 'text-white',
      urgency: 'normal',
      icon: (
        <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      ),
      title: `${timeRemaining.text} av prøveperioden`,
      message: `Utforsk ${getPlanDisplayName()} - alle funksjoner inkludert`,
      actionText: 'Se hva som er inkludert'
    };
  };

  const smartContent = getSmartVariantContent();

  return (
    <div className={`${smartContent.bgColor} ${smartContent.textColor} rounded-lg shadow-lg ${className}`}>
      <div className="px-4 sm:px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {smartContent.icon}
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm sm:text-base truncate">
                {smartContent.title}
              </h3>
              <p className="text-xs sm:text-sm text-white/90 mt-1">
                {smartContent.message}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2 ml-4">
            {isAdministrator && (
              <Link
                to="/subscription"
                className="bg-white/20 hover:bg-white/30 px-3 py-1.5 rounded-lg text-xs sm:text-sm font-medium transition-colors whitespace-nowrap"
              >
                {smartContent.actionText}
              </Link>
            )}
            
            {showDismiss && (
              <button
                onClick={handleDismiss}
                className="text-white/80 hover:text-white transition-colors p-1"
                aria-label="Lukk banner"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
        
        {/* Progress indicator for smart variant */}
        <div className="mt-3">
          <div className="w-full bg-white/20 rounded-full h-1">
            <div 
              className="bg-white h-1 rounded-full transition-all duration-1000 ease-out"
              style={{ width: `${progressPercent}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-white/80 mt-1">
            <span>Dag {Math.ceil((Date.now() - trialStart) / (24 * 60 * 60 * 1000))}/7</span>
            <span>{timeRemaining.shortText}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
