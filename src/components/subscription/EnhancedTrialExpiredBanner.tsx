import React, { useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useAction } from 'convex/react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../../convex/_generated/api';
import { useRealTimeSubscriptionAccess } from '../../hooks/useRealTimeSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { formatTimeRemaining } from '../../utils/timeFormatting';
import { PrimaryButton, SecondaryButton } from '../ui';

interface EnhancedTrialExpiredBannerProps {
  onDismiss?: () => void;
  showDismiss?: boolean;
  className?: string;
}

/**
 * Enhanced trial expired banner with motivating design and clear CTA
 * Replaces duplicate trial expiration messages with a single, beautiful banner
 */
export const EnhancedTrialExpiredBanner: React.FC<EnhancedTrialExpiredBannerProps> = ({
  onDismiss,
  showDismiss = false,
  className = ''
}) => {
  const { user } = useUser();
  const navigate = useNavigate();
  const { 
    subscription, 
    isTrialExpired, 
    isInGracePeriod, 
    isRealTimeExpired,
    shouldShowExpiredStatus,
    trialEnd 
  } = useRealTimeSubscriptionAccess();
  const { isAdministrator } = useUserRole();
  const createPortalSession = useAction(api.subscriptions.createPortalSession);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  // Don't show if not expired, no subscription, or dismissed
  if (!subscription || (!isTrialExpired && !isInGracePeriod && !shouldShowExpiredStatus) || isDismissed) {
    return null;
  }

  const handleUpgrade = async () => {
    if (!user) return;

    setIsUpgrading(true);
    try {
      if (isAdministrator) {
        // Navigate to subscription management for administrators
        navigate('/subscription');
      } else {
        // For non-administrators, show a message or redirect to contact admin
        navigate('/subscription');
      }
    } catch (error) {
      console.error('Failed to navigate to subscription:', error);
      setIsUpgrading(false);
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    onDismiss?.();
  };

  const getGracePeriodTimeLeft = () => {
    if (!trialEnd) return { text: '0 dager', isUrgent: true };
    const gracePeriodEnd = trialEnd + (3 * 24 * 60 * 60 * 1000); // 3 days after trial end
    return formatTimeRemaining(gracePeriodEnd);
  };

  const graceTimeLeft = getGracePeriodTimeLeft();
  const isInGracePeriodState = isInGracePeriod && !shouldShowExpiredStatus;

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Gradient Background */}
      <div className="bg-gradient-to-r from-jobblogg-error via-jobblogg-error to-jobblogg-warning rounded-xl shadow-lg">
        <div className="bg-white/95 backdrop-blur-sm rounded-xl m-0.5 p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 flex-1">
              {/* Icon */}
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-gradient-to-br from-jobblogg-error to-jobblogg-warning rounded-full flex items-center justify-center shadow-lg">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
                  {isInGracePeriodState ? '⏰ Begrenset tilgang' : '🚨 Prøveperioden er utløpt'}
                </h3>
                
                <p className="text-jobblogg-text-medium mb-4 leading-relaxed">
                  {isInGracePeriodState ? (
                    <>
                      Du har <strong className="text-jobblogg-warning">{graceTimeLeft.text}</strong> med 
                      begrenset tilgang til JobbLogg. Oppgrader nå for å fortsette å bruke alle funksjoner.
                    </>
                  ) : (
                    <>
                      Din <strong>7-dagers gratis prøveperiode</strong> er nå over. 
                      Oppgrader til en betalt plan for å fortsette å dokumentere prosjektene dine med JobbLogg.
                    </>
                  )}
                </p>

                {/* Benefits reminder */}
                <div className="bg-jobblogg-primary-soft rounded-lg p-3 mb-4">
                  <p className="text-sm text-jobblogg-primary font-medium mb-2">
                    🎯 Med JobbLogg Professional får du:
                  </p>
                  <ul className="text-sm text-jobblogg-text-medium space-y-1">
                    <li>✅ Ubegrenset prosjekter og dokumentasjon</li>
                    <li>✅ Teamsamarbeid og prosjektdeling</li>
                    <li>✅ Avanserte rapporter og eksport</li>
                    <li>✅ Prioritert kundesupport</li>
                  </ul>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3">
                  {isAdministrator ? (
                    <>
                      <PrimaryButton
                        onClick={handleUpgrade}
                        disabled={isUpgrading}
                        className="bg-gradient-to-r from-jobblogg-primary to-jobblogg-success hover:from-jobblogg-primary/90 hover:to-jobblogg-success/90 shadow-lg"
                      >
                        {isUpgrading ? (
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                            Laster...
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                            Oppgrader til Professional
                          </div>
                        )}
                      </PrimaryButton>
                      
                      <SecondaryButton
                        onClick={() => navigate('/subscription')}
                        className="border-jobblogg-primary text-jobblogg-primary hover:bg-jobblogg-primary/10"
                      >
                        Se alle planer
                      </SecondaryButton>
                    </>
                  ) : (
                    <div className="bg-jobblogg-neutral-soft rounded-lg p-3">
                      <p className="text-sm text-jobblogg-text-medium">
                        <strong>Kun administratorer</strong> kan oppgradere abonnementet. 
                        Kontakt din administrator for å fortsette å bruke JobbLogg.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Dismiss Button */}
            {showDismiss && (
              <button
                onClick={handleDismiss}
                className="flex-shrink-0 ml-4 p-2 text-jobblogg-text-muted hover:text-jobblogg-text-medium hover:bg-jobblogg-neutral-soft rounded-lg transition-colors"
                aria-label="Lukk varsel"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
