import React, { useCallback, useState } from 'react';
import { Link } from 'react-router-dom';
import { useRealTimeTrialStatus } from '../../hooks/useRealTimeTrialStatus';
import { useRoleChangeListener } from '../../hooks/useRoleChangeHandler';
import { useUserRole } from '../../hooks/useUserRole';
import { getSubscriptionStatusInfo } from '../../utils/subscriptionStatusFormatting';
import { formatTimeRemaining, getDaysLeft } from '../../utils/timeFormatting';

export const MinimalTrialIndicator: React.FC = () => {
  const {
    subscription,
    isInTrial,
    isTrialExpired,
    isLoading,
    trialEnd,
    isRealTimeExpired,
    isStillCountingDown,
    shouldShowExpiredStatus
  } = useRealTimeTrialStatus();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();
  const [_forceUpdate, setForceUpdate] = useState(0);

  // Listen for role changes and force re-render when user becomes administrator
  const handleRoleChange = useCallback((event: CustomEvent) => {
    if (event.detail.becameAdmin) {
      console.log('MinimalTrialIndicator: User became administrator, forcing update');
      setForceUpdate(prev => prev + 1);
    }
  }, []);

  useRoleChangeListener(handleRoleChange);

  // Don't show if loading or no subscription
  if (isLoading || roleLoading || !subscription) return null;

  // Only show for active trials (not expired) to avoid duplicate with EnhancedTrialExpiredBanner
  if (!isInTrial || isTrialExpired || shouldShowExpiredStatus) return null;

  // Use real-time calculation for display
  const timeRemaining = formatTimeRemaining(trialEnd);
  const daysLeft = getDaysLeft(trialEnd); // For backward compatibility
  const isExpiringSoon = timeRemaining.isUrgent || daysLeft <= 2;

  // Get dynamic subscription status information
  const subscriptionStatusInfo = getSubscriptionStatusInfo(subscription);

  // This component now only handles active trial countdown, not expired state
  // Expired state is handled by EnhancedTrialExpiredBanner to avoid duplicates



  // Active trial state - show countdown for both administrators and regular users
  if (!isAdministrator) {
    // Show simple countdown for non-administrators
    return (
      <div className={`p-3 rounded-lg border ${
        isExpiringSoon
          ? 'border-jobblogg-warning bg-jobblogg-warning-soft'
          : 'border-jobblogg-primary bg-jobblogg-primary-soft'
      }`}>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
          }`}></div>
          <span className={`text-sm font-medium ${
            isExpiringSoon ? 'text-jobblogg-warning' : 'text-jobblogg-primary'
          }`}>
            {`Din bedrift har startet prøveabonnement: ${timeRemaining.text}`}
          </span>
        </div>
      </div>
    );
  }

  // Administrator view with clickable link to subscription management
  return (
    <Link
      to="/subscription"
      className={`block p-3 rounded-lg border transition-colors duration-200 ${
        isExpiringSoon
          ? 'border-jobblogg-warning bg-jobblogg-warning-soft hover:bg-jobblogg-warning-soft/80'
          : 'border-jobblogg-primary bg-jobblogg-primary-soft hover:bg-jobblogg-primary-soft/80'
      }`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
          }`}></div>
          <span className={`text-sm font-medium ${
            isExpiringSoon ? 'text-jobblogg-warning' : 'text-jobblogg-primary'
          }`}>
            {`Prøveperiode: ${timeRemaining.text}`}
          </span>
        </div>
        <svg className={`w-4 h-4 ${
          isExpiringSoon ? 'text-jobblogg-warning' : 'text-jobblogg-primary'
        }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </Link>
  );
};
