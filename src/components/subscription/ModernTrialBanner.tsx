import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRealTimeSubscriptionAccess } from '../../hooks/useRealTimeSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { formatTimeRemaining, getDaysLeft } from '../../utils/timeFormatting';
import './ModernTrialBanner.css';

interface ModernTrialBannerProps {
  className?: string;
}

/**
 * Modern 2025-standard trial banner component
 * Sticky positioning, mobile-first responsive design, single source of truth
 */
export const ModernTrialBanner: React.FC<ModernTrialBannerProps> = ({
  className = ''
}) => {
  const navigate = useNavigate();
  const {
    subscription,
    hasActiveSubscription,
    isInTrial,
    isTrialExpired,
    isRealTimeExpired,
    shouldShowExpiredStatus,
    trialEnd
  } = useRealTimeSubscriptionAccess();
  const { isAdministrator } = useUserRole();
  const [isDismissed, setIsDismissed] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Check if banner should be shown - CRITICAL FIX: Hide for active subscriptions!
  const shouldShow = (isInTrial || shouldShowExpiredStatus) && !isDismissed && subscription && !hasActiveSubscription;

  // Debug logging to track subscription state changes
  useEffect(() => {
    console.log('🎯 ModernTrialBanner state:', {
      hasActiveSubscription,
      isInTrial,
      shouldShowExpiredStatus,
      subscriptionStatus: subscription?.status,
      shouldShow,
      isDismissed,
      // Additional debugging
      subscription: subscription ? {
        _id: subscription._id,
        status: subscription.status,
        planLevel: subscription.planLevel,
        trialEnd: subscription.trialEnd,
        trialConvertedAt: subscription.trialConvertedAt,
        updatedAt: subscription.updatedAt,
      } : null,
      // Real-time values
      isTrialExpired,
      isRealTimeExpired,
      trialEnd,
      currentTime: Date.now(),
    });
  }, [hasActiveSubscription, isInTrial, shouldShowExpiredStatus, subscription, shouldShow, isDismissed, isTrialExpired, isRealTimeExpired, trialEnd]);

  // Animation effect
  useEffect(() => {
    if (shouldShow) {
      const timer = setTimeout(() => setIsVisible(true), 100);
      return () => clearTimeout(timer);
    } else {
      setIsVisible(false);
    }
  }, [shouldShow]);

  // Handle dismiss (temporary - comes back next session)
  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => setIsDismissed(true), 300); // Wait for animation
  };

  // Handle CTA click
  const handleUpgrade = () => {
    if (isAdministrator) {
      navigate('/subscription');
    } else {
      // For non-administrators, show message or redirect
      navigate('/subscription');
    }
  };

  // Handle force refresh for debugging
  const handleForceRefresh = () => {
    console.log('🔄 Force refreshing subscription data...');
    window.dispatchEvent(new CustomEvent('subscription-updated', {
      detail: { source: 'manual-refresh', timestamp: Date.now() }
    }));
  };

  // Don't render if shouldn't show
  if (!shouldShow) return null;

  // Calculate display text
  const timeRemaining = formatTimeRemaining(trialEnd);
  const daysLeft = getDaysLeft(trialEnd);
  
  const getDisplayText = () => {
    if (shouldShowExpiredStatus || isRealTimeExpired) {
      return 'Prøveperioden er utløpt';
    }
    
    if (daysLeft <= 1) {
      return `Prøveperioden utløper i dag`;
    }
    
    return `Prøveperioden utløper om ${daysLeft} ${daysLeft === 1 ? 'dag' : 'dager'}`;
  };

  return (
    <div
      className={`fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ease-out ${
        isVisible ? 'translate-y-0' : '-translate-y-full'
      } ${className}`}
      data-testid="modern-trial-banner"
      data-analytics="trial-banner"
    >
      <div
        className={`min-h-[48px] md:min-h-[56px] flex items-center shadow-medium ${
          shouldShowExpiredStatus || isRealTimeExpired
            ? 'bg-jobblogg-error text-white'
            : 'bg-jobblogg-primary text-white'
        }`}
        role="banner"
        aria-live="polite"
        aria-label="Prøveperiode varsel"
      >
        <div className="container-wide px-4 sm:px-6">
          <div className="flex items-center justify-between gap-4 py-2.5 md:py-3">
            {/* Icon + Text Section */}
            <div className="flex items-center gap-4 flex-1 min-w-0">
              {/* Warning Icon */}
              <div
                className="flex-shrink-0 w-7 h-7 flex items-center justify-center"
                aria-hidden="true"
              >
                <span className="text-xl leading-none">⚠️</span>
              </div>
              
              {/* Main Text */}
              <div className="flex-1 min-w-0">
                <p className="text-sm sm:text-base font-semibold leading-tight font-system">
                  {getDisplayText()}
                  {isAdministrator && (shouldShowExpiredStatus || isRealTimeExpired) && (
                    <span className="block sm:inline sm:ml-2 text-xs sm:text-sm font-medium opacity-90 mt-1 sm:mt-0">
                      Oppgrader for å fortsette å bruke alle funksjoner
                    </span>
                  )}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2 flex-shrink-0">
              {/* Debug Refresh Button (temporary) */}
              <button
                onClick={handleForceRefresh}
                className="bg-yellow-500 text-white px-2 py-1 rounded text-xs hover:bg-yellow-600 transition-colors"
                title="Force refresh subscription data (debug)"
              >
                🔄
              </button>

              {/* CTA Button */}
              {isAdministrator && (
                <button
                  onClick={handleUpgrade}
                  className={`
                    inline-flex items-center justify-center gap-2 px-3 py-2 sm:px-4 sm:py-2
                    rounded-xl text-sm font-semibold transition-all duration-200 ease-in-out
                    min-h-[44px] min-w-[44px] shadow-soft hover:shadow-medium
                    focus:outline-none focus:ring-2 focus:ring-offset-2
                    active:scale-[0.98] active:transition-transform active:duration-150
                    transform hover:scale-[1.02] hover:translate-y-[-1px]
                    ${shouldShowExpiredStatus || isRealTimeExpired
                      ? 'bg-white text-jobblogg-error hover:bg-gray-50 hover:text-jobblogg-error-dark focus:ring-white focus:ring-offset-jobblogg-error'
                      : 'bg-white text-jobblogg-primary hover:bg-gray-50 hover:text-jobblogg-primary-dark focus:ring-white focus:ring-offset-jobblogg-primary'
                    }
                  `.trim().replace(/\s+/g, ' ')}
                  aria-label="Oppgrader abonnement"
                  data-analytics="trial-banner-upgrade"
                >
                  <span className="hidden sm:inline">Oppgrader</span>
                  <span className="sm:hidden">↗</span>
                </button>
              )}

              {/* Dismiss Button */}
              <button
                onClick={handleDismiss}
                className={`
                  text-white transition-all duration-200 min-h-[48px] min-w-[48px]
                  flex items-center justify-center rounded-xl
                  focus:outline-none focus:ring-2 focus:ring-offset-2
                  hover:bg-white/10 active:bg-white/20
                  ${shouldShowExpiredStatus || isRealTimeExpired
                    ? 'hover:text-jobblogg-error-light focus:ring-white focus:ring-offset-jobblogg-error'
                    : 'hover:text-jobblogg-primary-light focus:ring-white focus:ring-offset-jobblogg-primary'
                  }
                `.trim().replace(/\s+/g, ' ')}
                aria-label="Lukk varsel midlertidig"
                data-analytics="trial-banner-dismiss"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2.5}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
