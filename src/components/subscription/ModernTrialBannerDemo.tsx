import React from 'react';
import { useRealTimeSubscriptionAccess } from '../../hooks/useRealTimeSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';

/**
 * Demo component to showcase the new Modern Trial Banner (2025 standard)
 */
export const ModernTrialBannerDemo: React.FC = () => {
  const {
    isInTrial,
    isTrialExpired,
    shouldShowExpiredStatus,
    trialEnd,
    currentTime
  } = useRealTimeSubscriptionAccess();
  const { isAdministrator } = useUserRole();

  const timeUntilExpiration = trialEnd - currentTime;
  const minutesLeft = Math.max(0, Math.floor(timeUntilExpiration / (60 * 1000)));

  return (
    <div className="space-y-6 p-6 bg-jobblogg-neutral-soft rounded-xl">
      <div className="text-center">
        <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
          🎨 Modern Trial Banner (JobbLogg Design System)
        </h2>
        <p className="text-jobblogg-text-medium">
          Sticky positioning, JobbLogg brand colors, mobile-first responsive
        </p>
      </div>

      {/* Current Status */}
      <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
        <h3 className="font-medium text-jobblogg-text-strong mb-3">Nåværende status:</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-jobblogg-text-medium">Aktiv prøveperiode:</span>
            <span className={`ml-2 font-medium ${isInTrial ? 'text-jobblogg-success' : 'text-jobblogg-error'}`}>
              {isInTrial ? '✅ Ja' : '❌ Nei'}
            </span>
          </div>
          <div>
            <span className="text-jobblogg-text-medium">Administrator:</span>
            <span className={`ml-2 font-medium ${isAdministrator ? 'text-jobblogg-success' : 'text-jobblogg-warning'}`}>
              {isAdministrator ? '✅ Ja' : '⚠️ Nei'}
            </span>
          </div>
          <div>
            <span className="text-jobblogg-text-medium">Utløpt (real-time):</span>
            <span className={`ml-2 font-medium ${shouldShowExpiredStatus ? 'text-jobblogg-error' : 'text-jobblogg-success'}`}>
              {shouldShowExpiredStatus ? '⚠️ Ja' : '✅ Nei'}
            </span>
          </div>
          <div>
            <span className="text-jobblogg-text-medium">Minutter igjen:</span>
            <span className={`ml-2 font-medium ${minutesLeft < 60 ? 'text-jobblogg-error' : 'text-jobblogg-primary'}`}>
              {minutesLeft}
            </span>
          </div>
        </div>
      </div>

      {/* Design Specifications */}
      <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
        <h3 className="font-medium text-jobblogg-text-strong mb-3">🎨 JobbLogg Design System Integration:</h3>
        <div className="space-y-3 text-sm">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-jobblogg-text-strong mb-2">Optimaliserte dimensjoner:</h4>
              <ul className="text-jobblogg-text-medium space-y-1">
                <li>• Mobile: Full bredde, 48px høyde (slankere profil)</li>
                <li>• Desktop: Full bredde, 56px høyde (redusert fra 64px)</li>
                <li>• Touch targets: 44-48px (optimalisert for touch)</li>
                <li>• Ikon: 28x28px (mer luft rundt)</li>
                <li>• Lukkeknapp: 24x24px ikon (bedre synlighet)</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-jobblogg-text-strong mb-2">JobbLogg Brand Colors:</h4>
              <ul className="text-jobblogg-text-medium space-y-1">
                <li>• Aktiv: <span className="bg-jobblogg-primary text-white px-2 py-1 rounded text-xs">#2563EB</span> (jobblogg-primary)</li>
                <li>• Utløpt: <span className="bg-jobblogg-error text-white px-2 py-1 rounded text-xs">#B91C1C</span> (jobblogg-error)</li>
                <li>• Tekst: Hvit for optimal kontrast</li>
                <li>• CTA: Hvit bakgrunn med brand color tekst</li>
              </ul>
            </div>
          </div>
          
          <div>
            <h4 className="font-medium text-jobblogg-text-strong mb-2">Funksjonalitet:</h4>
            <ul className="text-jobblogg-text-medium space-y-1">
              <li>✅ Sticky positioning øverst på siden</li>
              <li>✅ Mobile-first responsive design</li>
              <li>✅ Inter font (JobbLogg's brand font)</li>
              <li>✅ Subtil slide-down animasjon (0.3s ease-out)</li>
              <li>✅ Kan lukkes midlertidig (kommer tilbake neste session)</li>
              <li>✅ WCAG AA compliant (kontrast, ARIA-labels)</li>
              <li>✅ Touch-friendly på mobile enheter</li>
              <li>✅ Analytics tracking med data-attributter</li>
              <li>✅ Dynamiske farger basert på trial status</li>
              <li>✅ Konsistent med JobbLogg design system</li>
              <li>✅ Subtile hover-effekter på CTA-knapp</li>
              <li>✅ Forbedret touch targets for mobile</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Layout Structure */}
      <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
        <h3 className="font-medium text-jobblogg-text-strong mb-3">📐 Layout-struktur:</h3>
        <div className="bg-jobblogg-neutral-soft rounded p-3 font-mono text-sm">
          [⚠️ Ikon] [Hovedtekst som kan wrappe] [CTA-knapp] [× Lukkeknapp]
        </div>
        <p className="text-xs text-jobblogg-text-medium mt-2">
          Flexbox layout med responsive gap og proper touch targets
        </p>
      </div>

      {/* Recent Improvements */}
      <div className="bg-jobblogg-accent-soft rounded-lg p-4 border border-jobblogg-accent">
        <h3 className="font-medium text-jobblogg-accent mb-3">✨ Nylige forbedringer:</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-jobblogg-text-strong mb-2">Spacing & Proportioner:</h4>
            <ul className="text-jobblogg-text-medium space-y-1">
              <li>• Redusert høyde: 48px → 56px (slankere)</li>
              <li>• Mer luft rundt ikon (28x28px)</li>
              <li>• Forbedret gap mellom elementer (16px)</li>
              <li>• Optimalisert padding (10px → 12px)</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-jobblogg-text-strong mb-2">Interaktivitet:</h4>
            <ul className="text-jobblogg-text-medium space-y-1">
              <li>• CTA hover: Scale + lift effekt</li>
              <li>• Lukkeknapp: Større ikon (24x24px)</li>
              <li>• Subtil bakgrunnseffekt på hover</li>
              <li>• Forbedret touch targets</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Technical Implementation */}
      <div className="bg-jobblogg-primary-soft rounded-lg p-4 border border-jobblogg-primary">
        <h3 className="font-medium text-jobblogg-primary mb-3">🔧 Teknisk implementering:</h3>
        <ul className="text-sm text-jobblogg-text-medium space-y-1">
          <li>• <strong>ModernTrialBanner:</strong> Hovedkomponent med sticky positioning</li>
          <li>• <strong>TrialBannerSpacer:</strong> Forhindrer innhold fra å skjules bak banner</li>
          <li>• <strong>AuthenticatedLayout:</strong> Integrert på toppnivå for konsistent visning</li>
          <li>• <strong>Real-time sync:</strong> Bruker useRealTimeSubscriptionAccess hook</li>
          <li>• <strong>CSS Grid/Flexbox:</strong> Moderne layout-teknikker</li>
          <li>• <strong>Accessibility:</strong> ARIA-labels, focus management, high contrast support</li>
        </ul>
      </div>

      {/* Browser Compatibility */}
      <div className="bg-jobblogg-success-soft rounded-lg p-4 border border-jobblogg-success">
        <h3 className="font-medium text-jobblogg-success mb-3">🌐 Browser-kompatibilitet:</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-jobblogg-text-strong mb-1">Støttede browsere:</h4>
            <ul className="text-jobblogg-text-medium space-y-1">
              <li>✅ Chrome (moderne)</li>
              <li>✅ Firefox (moderne)</li>
              <li>✅ Safari (moderne)</li>
              <li>✅ Edge (moderne)</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-jobblogg-text-strong mb-1">Fallbacks:</h4>
            <ul className="text-jobblogg-text-medium space-y-1">
              <li>• Graceful degradation for eldre browsere</li>
              <li>• Reduced motion support</li>
              <li>• High contrast mode support</li>
              <li>• Touch-friendly på alle enheter</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
