import { useUser } from '@clerk/clerk-react';
import { useMutation, useQuery } from 'convex/react';
import React, { useEffect, useState } from "react";
import { api } from '../../../convex/_generated/api';
import { Card } from "../ui/Card";
import { Heading2 } from "../ui/Typography";

// Temporary mock preferences while backend is being fixed
const mockPreferences = {
  paymentFailureEmail: true,
  paymentFailureSms: false,
  trialReminderEmail: true,
  subscriptionUpdateEmail: true,
  paymentReceiptEmail: true,
};

interface NotificationPreferencesProps {
  className?: string;
}

export const NotificationPreferences: React.FC<NotificationPreferencesProps> = ({
  className = ""
}) => {
  const { user } = useUser();
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateMessage, setUpdateMessage] = useState<string | null>(null);
  const [localPreferences, setLocalPreferences] = useState(mockPreferences);

  // Check if API functions are available
  const hasAPI = !!api.paymentNotifications;

  // Try to fetch notification preferences - use conditional to avoid errors
  const preferences = useQuery(
    hasAPI ? api.paymentNotifications.getUserNotificationPreferences : undefined,
    user && hasAPI ? { userId: user.id } : "skip"
  );

  // Initialize preferences if they don't exist
  const initializePreferences = useMutation(
    hasAPI ? api.paymentNotifications.initializeUserNotificationPreferences : undefined
  );

  // Update preferences
  const updatePreferences = useMutation(
    hasAPI ? api.paymentNotifications.updateUserNotificationPreferences : undefined
  );

  // Use API preferences if available, otherwise use local mock
  const currentPreferences = hasAPI && preferences ? preferences : localPreferences;

  // Initialize preferences on first load if they don't exist
  useEffect(() => {
    if (user && preferences === null && hasAPI && initializePreferences) {
      initializePreferences({ userId: user.id })
        .catch(error => {
          console.error('Failed to initialize preferences:', error);
          setUpdateMessage('Feil ved initialisering av innstillinger');
        });
    }
  }, [user, preferences, hasAPI, initializePreferences]);

  const handlePreferenceChange = async (key: string, value: boolean) => {
    if (!user) return;

    setIsUpdating(true);
    setUpdateMessage(null);

    try {
      if (hasAPI && updatePreferences) {
        // Use real API if available
        await updatePreferences({
          userId: user.id,
          preferences: {
            [key]: value
          }
        });
      } else {
        // Use local mock for now
        setLocalPreferences(prev => ({
          ...prev,
          [key]: value
        }));
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      setUpdateMessage('Innstillinger oppdatert');
    } catch (error) {
      console.error('Failed to update preferences:', error);
      setUpdateMessage('Feil ved oppdatering av innstillinger');
    } finally {
      setIsUpdating(false);
    }
  };

  if (!user) {
    return null;
  }

  // Show loading state while preferences are being fetched (only if API is available)
  if (hasAPI && preferences === undefined) {
    return (
      <Card className={`space-y-6 ${className}`}>
        <Heading2>Varslingsinnstillinger</Heading2>
        <div className="text-center py-8">
          <p className="text-jobblogg-text-muted">Laster innstillinger...</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`space-y-6 ${className}`}>
      <Heading2>Varslingsinnstillinger</Heading2>
      <p className="text-jobblogg-text-muted">
        Administrer hvordan du vil motta varsler om betalinger og abonnement.
        {!hasAPI && (
          <span className="block mt-2 text-sm text-jobblogg-warning">
            ⚠️ Bruker midlertidig lokal lagring mens backend-problemer løses.
          </span>
        )}
      </p>

      {updateMessage && (
        <div className={`p-3 rounded-lg text-sm ${
          updateMessage.includes('Feil')
            ? 'bg-jobblogg-error-light text-jobblogg-error'
            : 'bg-jobblogg-success-light text-jobblogg-success'
        }`}>
          {updateMessage}
        </div>
      )}

      <div className="space-y-6">
        {/* Payment Failure Notifications */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-jobblogg-text">Betalingsfeil</h3>

          <div className="space-y-3">
            <label className="flex items-center justify-between p-4 bg-jobblogg-card-bg rounded-lg">
              <div>
                <div className="font-medium text-jobblogg-text">E-postvarsler</div>
                <div className="text-sm text-jobblogg-text-muted">
                  Få e-post når betalinger feiler
                </div>
              </div>
              <input
                type="checkbox"
                checked={currentPreferences?.paymentFailureEmail ?? true}
                onChange={(e) => handlePreferenceChange('paymentFailureEmail', e.target.checked)}
                disabled={isUpdating}
                className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border rounded focus:ring-jobblogg-primary focus:ring-2"
              />
            </label>

            <label className="flex items-center justify-between p-4 bg-jobblogg-card-bg rounded-lg">
              <div>
                <div className="font-medium text-jobblogg-text">SMS-varsler</div>
                <div className="text-sm text-jobblogg-text-muted">
                  Få SMS når betalinger feiler (krever telefonnummer)
                </div>
              </div>
              <input
                type="checkbox"
                checked={currentPreferences?.paymentFailureSms ?? false}
                onChange={(e) => handlePreferenceChange('paymentFailureSms', e.target.checked)}
                disabled={isUpdating}
                className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border rounded focus:ring-jobblogg-primary focus:ring-2"
              />
            </label>
          </div>
        </div>

        {/* Trial Reminders */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-jobblogg-text">Prøveperiode</h3>

          <div className="space-y-3">
            <label className="flex items-center justify-between p-4 bg-jobblogg-card-bg rounded-lg">
              <div>
                <div className="font-medium text-jobblogg-text">Påminnelser om prøveperiode</div>
                <div className="text-sm text-jobblogg-text-muted">
                  Få varsler når prøveperioden nærmer seg slutten
                </div>
              </div>
              <input
                type="checkbox"
                checked={currentPreferences?.trialReminderEmail ?? true}
                onChange={(e) => handlePreferenceChange('trialReminderEmail', e.target.checked)}
                disabled={isUpdating}
                className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border rounded focus:ring-jobblogg-primary focus:ring-2"
              />
            </label>
          </div>
        </div>

        {/* Subscription Updates */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-jobblogg-text">Abonnement</h3>

          <div className="space-y-3">
            <label className="flex items-center justify-between p-4 bg-jobblogg-card-bg rounded-lg">
              <div>
                <div className="font-medium text-jobblogg-text">Abonnementsendringer</div>
                <div className="text-sm text-jobblogg-text-muted">
                  Få varsler om endringer i abonnement og fakturering
                </div>
              </div>
              <input
                type="checkbox"
                checked={currentPreferences?.subscriptionUpdateEmail ?? true}
                onChange={(e) => handlePreferenceChange('subscriptionUpdateEmail', e.target.checked)}
                disabled={isUpdating}
                className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border rounded focus:ring-jobblogg-primary focus:ring-2"
              />
            </label>

            <label className="flex items-center justify-between p-4 bg-jobblogg-card-bg rounded-lg">
              <div>
                <div className="font-medium text-jobblogg-text">Kvitteringer</div>
                <div className="text-sm text-jobblogg-text-muted">
                  Få kvitteringer på e-post for vellykkede betalinger
                </div>
              </div>
              <input
                type="checkbox"
                checked={currentPreferences?.paymentReceiptEmail ?? true}
                onChange={(e) => handlePreferenceChange('paymentReceiptEmail', e.target.checked)}
                disabled={isUpdating}
                className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border rounded focus:ring-jobblogg-primary focus:ring-2"
              />
            </label>
          </div>
        </div>
      </div>
    </Card>
  );
};
