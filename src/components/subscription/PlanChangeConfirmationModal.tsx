import React, { useState } from 'react';
import { PrimaryButton, SecondaryButton } from '../ui/Button';
import { Modal } from '../ui/Dialog';
import { Heading3 } from '../ui/Typography';

export interface PlanChangeDetails {
  currentPlan: {
    id: string;
    name: string;
    price: number;
    billingInterval: 'month' | 'year';
  };
  newPlan: {
    id: string;
    name: string;
    price: number;
    billingInterval: 'month' | 'year';
  };
  proration?: {
    amount: number;
    description: string;
  };
  effectiveDate: string;
  nextBillingDate: string;
  isUpgrade: boolean;
}

export interface PlanChangeConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  planChangeDetails: PlanChangeDetails | null;
  isProcessing?: boolean;
}

export const PlanChangeConfirmationModal: React.FC<PlanChangeConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  planChangeDetails,
  isProcessing = false
}) => {
  const [hasReadTerms, setHasReadTerms] = useState(false);

  if (!planChangeDetails) return null;

  const { currentPlan, newPlan, proration, effectiveDate, nextBillingDate, isUpgrade } = planChangeDetails;

  const formatPrice = (price: number, interval: 'month' | 'year') => {
    const period = interval === 'year' ? 'år' : 'måned';
    return `${price.toLocaleString('nb-NO')} NOK per ${period}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const priceDifference = newPlan.price - currentPlan.price;
  const changeType = isUpgrade ? 'oppgradering' : 'nedgradering';
  const changeTypeCapitalized = isUpgrade ? 'Oppgradering' : 'Nedgradering';

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md">
      <div className="space-y-6">
        <div className="text-center space-y-2">
          <Heading3>Bekreft {changeType}</Heading3>
          <p className="text-jobblogg-text-muted">
            Gjennomgå detaljene før du bekrefter endringen av abonnementet ditt
          </p>
        </div>

        {/* Plan Change Summary */}
        <div className="bg-jobblogg-card-bg rounded-lg p-4 space-y-4">
          <h4 className="font-medium text-jobblogg-text">Planendring</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-sm text-jobblogg-text-muted">Fra:</div>
              <div className="font-medium text-jobblogg-text">{currentPlan.name}</div>
              <div className="text-sm text-jobblogg-text-muted">
                {formatPrice(currentPlan.price, currentPlan.billingInterval)}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="text-sm text-jobblogg-text-muted">Til:</div>
              <div className="font-medium text-jobblogg-text">{newPlan.name}</div>
              <div className="text-sm text-jobblogg-text-muted">
                {formatPrice(newPlan.price, newPlan.billingInterval)}
              </div>
            </div>
          </div>

          {/* Price Difference */}
          <div className="border-t border-jobblogg-border pt-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-jobblogg-text-muted">Prisendring:</span>
              <span className={`font-medium ${
                priceDifference > 0 ? 'text-jobblogg-error' : 'text-jobblogg-success'
              }`}>
                {priceDifference > 0 ? '+' : ''}{priceDifference.toLocaleString('nb-NO')} NOK
                {newPlan.billingInterval === 'year' ? ' per år' : ' per måned'}
              </span>
            </div>
          </div>
        </div>

        {/* Proration Details */}
        {proration && (
          <div className="bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-4 space-y-2">
            <h4 className="font-medium text-jobblogg-text">Forholdsmessig justering</h4>
            <p className="text-sm text-jobblogg-text-muted">{proration.description}</p>
            <div className="flex justify-between items-center pt-2 border-t border-jobblogg-warning">
              <span className="text-sm font-medium text-jobblogg-text">
                {proration.amount > 0 ? 'Tilleggsbeløp' : 'Kreditt'}:
              </span>
              <span className={`font-medium ${
                proration.amount > 0 ? 'text-jobblogg-error' : 'text-jobblogg-success'
              }`}>
                {proration.amount > 0 ? '+' : ''}{proration.amount.toLocaleString('nb-NO')} NOK
              </span>
            </div>
          </div>
        )}

        {/* Timing Information */}
        <div className="space-y-3">
          <h4 className="font-medium text-jobblogg-text">Viktige datoer</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-jobblogg-text-muted">Endringen trer i kraft:</span>
              <span className="text-jobblogg-text font-medium">{formatDate(effectiveDate)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-jobblogg-text-muted">Neste fakturadato:</span>
              <span className="text-jobblogg-text font-medium">{formatDate(nextBillingDate)}</span>
            </div>
          </div>
        </div>

        {/* Important Notes */}
        <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-lg p-4">
          <h4 className="font-medium text-jobblogg-text mb-2">Viktig informasjon</h4>
          <ul className="text-sm text-jobblogg-text-muted space-y-1">
            <li>• {changeTypeCapitalized}en trer i kraft umiddelbart</li>
            <li>• Du vil få tilgang til nye funksjoner med en gang</li>
            {isUpgrade ? (
              <li>• Du vil bli belastet forholdsmessig for resten av faktureringsperioden</li>
            ) : (
              <li>• Du beholder tilgang til alle funksjoner til neste faktureringsperiode</li>
            )}
            <li>• Du kan endre eller avbryte abonnementet når som helst</li>
          </ul>
        </div>

        {/* Terms Checkbox */}
        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="terms-agreement"
            checked={hasReadTerms}
            onChange={(e) => setHasReadTerms(e.target.checked)}
            className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border rounded focus:ring-jobblogg-primary focus:ring-2 mt-0.5"
          />
          <label htmlFor="terms-agreement" className="text-sm text-jobblogg-text-muted">
            Jeg bekrefter at jeg har lest og forstått endringene i abonnementet mitt, 
            inkludert prisendringer og faktureringsdetaljer.
          </label>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          <SecondaryButton
            onClick={onClose}
            disabled={isProcessing}
            className="flex-1"
          >
            Avbryt
          </SecondaryButton>
          <PrimaryButton
            onClick={onConfirm}
            disabled={!hasReadTerms || isProcessing}
            className="flex-1"
          >
            {isProcessing ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Behandler...</span>
              </div>
            ) : (
              `Bekreft ${changeType}`
            )}
          </PrimaryButton>
        </div>
      </div>
    </Modal>
  );
};
