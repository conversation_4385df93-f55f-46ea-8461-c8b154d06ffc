import React, { useState } from 'react';
import { Card } from '../ui/Card';
import { Heading2, Heading3 } from '../ui/Typography';
import { PrimaryButton, SecondaryButton } from '../ui/Button';

export interface PlanFeature {
  name: string;
  basic: boolean | string;
  professional: boolean | string;
  enterprise: boolean | string;
}

export interface PlanDetails {
  id: string;
  name: string;
  monthlyPrice: number;
  annualPrice: number;
  description: string;
  popular?: boolean;
  features: string[];
}

export interface PlanComparisonProps {
  currentPlan?: string;
  currentBillingInterval?: 'month' | 'year';
  onPlanSelect: (planId: string, billingInterval: 'month' | 'year') => void;
  isLoading?: boolean;
  className?: string;
}

const PLANS: PlanDetails[] = [
  {
    id: 'basic',
    name: 'Basic',
    monthlyPrice: 299,
    annualPrice: 2390, // 20% discount: 299 * 12 * 0.8
    description: 'Perfekt for små prosjekter og oppstartsbedrifter',
    features: [
      'Opptil 5 aktive prosjekter',
      'Grunnleggende prosjektdokumentasjon',
      'E-poststøtte',
      'Mobilapp tilgang',
      'Grunnleggende rapporter'
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    monthlyPrice: 999,
    annualPrice: 7992, // 20% discount: 999 * 12 * 0.8
    description: 'Ideell for voksende bedrifter med flere prosjekter',
    popular: true,
    features: [
      'Ubegrensede prosjekter',
      'Avansert prosjektdokumentasjon',
      'Teamsamarbeid og tilgangskontroll',
      'Prioritert e-poststøtte',
      'Avanserte rapporter og analyser',
      'API-tilgang',
      'Integrasjoner med tredjepartsverktøy'
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    monthlyPrice: 2999,
    annualPrice: 23992, // 20% discount: 2999 * 12 * 0.8
    description: 'For store organisasjoner med komplekse behov',
    features: [
      'Alt i Professional',
      'Dedikert kundesuksessansvarlig',
      'Tilpassede integrasjoner',
      'Avansert sikkerhet og compliance',
      'Ubegrenset lagringsplass',
      'Prioritert telefonstøtte',
      'Opplæring og onboarding',
      'SLA-garanti'
    ]
  }
];

export const PlanComparison: React.FC<PlanComparisonProps> = ({
  currentPlan,
  currentBillingInterval = 'month',
  onPlanSelect,
  isLoading = false,
  className = ''
}) => {
  const [selectedBillingInterval, setSelectedBillingInterval] = useState<'month' | 'year'>(currentBillingInterval);

  const formatPrice = (monthlyPrice: number, annualPrice: number, interval: 'month' | 'year') => {
    if (interval === 'year') {
      const monthlySavings = Math.round((monthlyPrice * 12 - annualPrice) / 12);
      return {
        price: `${annualPrice.toLocaleString('nb-NO')} NOK`,
        period: 'per år',
        savings: `Spar ${monthlySavings} NOK/måned`
      };
    }
    return {
      price: `${monthlyPrice.toLocaleString('nb-NO')} NOK`,
      period: 'per måned',
      savings: null
    };
  };

  const isCurrentPlan = (planId: string) => currentPlan === planId;
  const isUpgrade = (planId: string) => {
    const planOrder = ['basic', 'professional', 'enterprise'];
    const currentIndex = planOrder.indexOf(currentPlan || '');
    const targetIndex = planOrder.indexOf(planId);
    return targetIndex > currentIndex;
  };

  const getButtonText = (planId: string) => {
    if (isCurrentPlan(planId)) {
      return 'Nåværende plan';
    }
    return isUpgrade(planId) ? 'Oppgrader' : 'Nedgrader';
  };

  const getButtonVariant = (planId: string) => {
    if (isCurrentPlan(planId)) {
      return 'secondary' as const;
    }
    return 'primary' as const;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center space-y-4">
        <Heading2>Velg din plan</Heading2>
        <p className="text-jobblogg-text-muted">
          Oppgrader eller nedgrader abonnementet ditt når som helst
        </p>
        
        {/* Billing Toggle */}
        <div className="inline-flex bg-jobblogg-card-bg rounded-lg p-1">
          <button
            onClick={() => setSelectedBillingInterval('month')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              selectedBillingInterval === 'month'
                ? 'bg-white text-jobblogg-text shadow-sm'
                : 'text-jobblogg-text-muted hover:text-jobblogg-text'
            }`}
          >
            Månedlig
          </button>
          <button
            onClick={() => setSelectedBillingInterval('year')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors relative ${
              selectedBillingInterval === 'year'
                ? 'bg-white text-jobblogg-text shadow-sm'
                : 'text-jobblogg-text-muted hover:text-jobblogg-text'
            }`}
          >
            Årlig
            <span className="absolute -top-2 -right-2 bg-jobblogg-success text-white text-xs px-1.5 py-0.5 rounded-full">
              -20%
            </span>
          </button>
        </div>
      </div>

      {/* Plan Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {PLANS.map((plan) => {
          const pricing = formatPrice(plan.monthlyPrice, plan.annualPrice, selectedBillingInterval);
          const isCurrent = isCurrentPlan(plan.id);
          
          return (
            <Card
              key={plan.id}
              className={`relative p-6 space-y-6 ${
                plan.popular ? 'ring-2 ring-jobblogg-primary' : ''
              } ${isCurrent ? 'bg-jobblogg-success-light border-jobblogg-success' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-jobblogg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                    Mest populær
                  </span>
                </div>
              )}
              
              {isCurrent && (
                <div className="absolute -top-3 right-4">
                  <span className="bg-jobblogg-success text-white px-3 py-1 rounded-full text-sm font-medium">
                    Aktiv plan
                  </span>
                </div>
              )}

              <div className="text-center space-y-2">
                <Heading3>{plan.name}</Heading3>
                <p className="text-jobblogg-text-muted text-sm">{plan.description}</p>
                
                <div className="space-y-1">
                  <div className="text-3xl font-bold text-jobblogg-text">
                    {pricing.price}
                  </div>
                  <div className="text-jobblogg-text-muted text-sm">
                    {pricing.period}
                  </div>
                  {pricing.savings && (
                    <div className="text-jobblogg-success text-sm font-medium">
                      {pricing.savings}
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium text-jobblogg-text">Inkludert:</h4>
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm">
                      <svg
                        className="w-4 h-4 text-jobblogg-success mt-0.5 flex-shrink-0"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span className="text-jobblogg-text-muted">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="pt-4">
                {isCurrent ? (
                  <SecondaryButton
                    className="w-full"
                    disabled
                  >
                    {getButtonText(plan.id)}
                  </SecondaryButton>
                ) : (
                  <PrimaryButton
                    className="w-full"
                    onClick={() => onPlanSelect(plan.id, selectedBillingInterval)}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Behandler...' : getButtonText(plan.id)}
                  </PrimaryButton>
                )}
              </div>
            </Card>
          );
        })}
      </div>

      <div className="text-center text-sm text-jobblogg-text-muted">
        <p>Alle priser er inkludert MVA. Du kan endre eller avbryte abonnementet når som helst.</p>
      </div>
    </div>
  );
};
