import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PlanSelector } from './PlanSelector';
import { PrimaryButton, SecondaryButton, TextMuted } from '../ui';

interface PlanSwitchModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan: 'basic' | 'professional' | 'enterprise';
  currentBilling: 'month' | 'year';
  onPlanChanged: () => void;
}

export const PlanSwitchModal: React.FC<PlanSwitchModalProps> = ({
  isOpen,
  onClose,
  currentPlan,
  currentBilling,
  onPlanChanged,
}) => {
  const { user } = useUser();
  const [selectedPlan, setSelectedPlan] = useState<'basic' | 'professional' | 'enterprise'>(currentPlan);
  const [selectedBilling, setSelectedBilling] = useState<'month' | 'year'>(currentBilling);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateTrialPlan = useMutation(api.subscriptions.updateTrialPlan);

  const handlePlanSelect = (planLevel: string, billingInterval: 'month' | 'year') => {
    setSelectedPlan(planLevel as 'basic' | 'professional' | 'enterprise');
    setSelectedBilling(billingInterval);
    setError(null);
  };

  const handleConfirm = async () => {
    if (!user?.id) return;

    if (selectedPlan === currentPlan && selectedBilling === currentBilling) {
      onClose();
      return;
    }

    setIsUpdating(true);
    setError(null);

    try {
      await updateTrialPlan({
        userId: user.id,
        newPlanLevel: selectedPlan,
        newBillingInterval: selectedBilling,
      });

      onPlanChanged();
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Kunne ikke oppdatere plan');
    } finally {
      setIsUpdating(false);
    }
  };

  const hasChanges = selectedPlan !== currentPlan || selectedBilling !== currentBilling;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-jobblogg-border">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-jobblogg-text-strong">
                Endre plan under prøveperiode
              </h2>
              <TextMuted className="mt-1">
                Velg den planen som passer best for din bedrift
              </TextMuted>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-jobblogg-surface rounded-lg transition-colors duration-200"
            >
              <svg className="w-6 h-6 text-jobblogg-text-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6">
          <PlanSelector
            currentPlan={currentPlan}
            currentBilling={currentBilling}
            onPlanSelect={handlePlanSelect}
            title="Velg ny plan"
            subtitle="Din prøveperiode fortsetter uavbrutt med den nye planen"
            isTrialMode={true}
          />

          {error && (
            <div className="mt-6 p-4 bg-jobblogg-error-soft border border-jobblogg-error rounded-lg">
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium text-jobblogg-error">Feil</span>
              </div>
              <p className="text-sm text-jobblogg-error mt-1">{error}</p>
            </div>
          )}
        </div>

        <div className="p-6 border-t border-jobblogg-border bg-jobblogg-surface">
          <div className="flex items-center justify-between">
            <div>
              {hasChanges && (
                <TextMuted className="text-sm">
                  Endrer fra {currentPlan === 'basic' ? 'Liten bedrift' : 
                              currentPlan === 'professional' ? 'Mellomstor bedrift' : 'Stor bedrift'} til{' '}
                  {selectedPlan === 'basic' ? 'Liten bedrift' : 
                   selectedPlan === 'professional' ? 'Mellomstor bedrift' : 'Stor bedrift'}
                </TextMuted>
              )}
            </div>
            <div className="flex gap-3">
              <SecondaryButton onClick={onClose} disabled={isUpdating}>
                Avbryt
              </SecondaryButton>
              <PrimaryButton
                onClick={handleConfirm}
                disabled={isUpdating}
                className="min-w-[120px]"
              >
                {isUpdating ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Oppdaterer...</span>
                  </div>
                ) : hasChanges ? (
                  'Bekreft endring'
                ) : (
                  'Lukk'
                )}
              </PrimaryButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
