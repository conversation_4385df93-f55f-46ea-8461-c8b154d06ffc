import React from 'react';
import { PrimaryButton, SecondaryButton } from '../ui/Button';

export interface PlanOption {
  id: string;
  name: string;
  monthlyPrice: number;
  annualPrice: number;
  isUpgrade: boolean;
  isDowngrade: boolean;
}

export interface PlanUpgradeDowngradeButtonsProps {
  currentPlan: string;
  currentBillingInterval: 'month' | 'year';
  onUpgrade?: () => void;
  onDowngrade?: () => void;
  onViewPlans?: () => void;
  isLoading?: boolean;
  className?: string;
}

const PLAN_HIERARCHY = ['basic', 'professional', 'enterprise'];

const PLAN_NAMES: Record<string, string> = {
  basic: 'Basic',
  professional: 'Professional',
  enterprise: 'Enterprise'
};

const PLAN_PRICES: Record<string, { monthly: number; annual: number }> = {
  basic: { monthly: 299, annual: 2390 },
  professional: { monthly: 999, annual: 7992 },
  enterprise: { monthly: 2999, annual: 23992 }
};

export const PlanUpgradeDowngradeButtons: React.FC<PlanUpgradeDowngradeButtonsProps> = ({
  currentPlan,
  currentBillingInterval,
  onUpgrade,
  onDowngrade,
  onViewPlans,
  isLoading = false,
  className = ''
}) => {
  const currentPlanIndex = PLAN_HIERARCHY.indexOf(currentPlan);
  
  const getAvailableUpgrades = (): PlanOption[] => {
    return PLAN_HIERARCHY.slice(currentPlanIndex + 1).map(planId => ({
      id: planId,
      name: PLAN_NAMES[planId],
      monthlyPrice: PLAN_PRICES[planId].monthly,
      annualPrice: PLAN_PRICES[planId].annual,
      isUpgrade: true,
      isDowngrade: false
    }));
  };

  const getAvailableDowngrades = (): PlanOption[] => {
    return PLAN_HIERARCHY.slice(0, currentPlanIndex).map(planId => ({
      id: planId,
      name: PLAN_NAMES[planId],
      monthlyPrice: PLAN_PRICES[planId].monthly,
      annualPrice: PLAN_PRICES[planId].annual,
      isUpgrade: false,
      isDowngrade: true
    }));
  };

  const availableUpgrades = getAvailableUpgrades();
  const availableDowngrades = getAvailableDowngrades();
  const hasUpgrades = availableUpgrades.length > 0;
  const hasDowngrades = availableDowngrades.length > 0;

  const formatPrice = (price: number, interval: 'month' | 'year') => {
    const period = interval === 'year' ? 'år' : 'måned';
    return `${price.toLocaleString('nb-NO')} NOK/${period}`;
  };

  const getNextUpgradePlan = () => availableUpgrades[0];
  const getNextDowngradePlan = () => availableDowngrades[availableDowngrades.length - 1];

  if (!hasUpgrades && !hasDowngrades) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <p className="text-jobblogg-text-muted text-sm">
          Du har den høyeste tilgjengelige planen
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Quick Upgrade Section */}
      {hasUpgrades && (
        <div className="bg-gradient-to-r from-jobblogg-primary-light to-jobblogg-primary-lighter rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-jobblogg-text">Oppgrader til {getNextUpgradePlan().name}</h4>
              <p className="text-sm text-jobblogg-text-muted">
                Fra {formatPrice(
                  currentBillingInterval === 'year' 
                    ? getNextUpgradePlan().annualPrice 
                    : getNextUpgradePlan().monthlyPrice, 
                  currentBillingInterval
                )}
              </p>
            </div>
            <PrimaryButton
              onClick={onUpgrade}
              disabled={isLoading}
              size="sm"
            >
              {isLoading ? 'Laster...' : 'Oppgrader'}
            </PrimaryButton>
          </div>
          
          {availableUpgrades.length > 1 && (
            <p className="text-xs text-jobblogg-text-muted">
              +{availableUpgrades.length - 1} andre oppgraderingsalternativer tilgjengelig
            </p>
          )}
        </div>
      )}

      {/* Quick Downgrade Section */}
      {hasDowngrades && (
        <div className="bg-jobblogg-card-bg border border-jobblogg-border rounded-lg p-4 space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-jobblogg-text">Nedgrader til {getNextDowngradePlan().name}</h4>
              <p className="text-sm text-jobblogg-text-muted">
                Fra {formatPrice(
                  currentBillingInterval === 'year' 
                    ? getNextDowngradePlan().annualPrice 
                    : getNextDowngradePlan().monthlyPrice, 
                  currentBillingInterval
                )}
              </p>
            </div>
            <SecondaryButton
              onClick={onDowngrade}
              disabled={isLoading}
              size="sm"
            >
              {isLoading ? 'Laster...' : 'Nedgrader'}
            </SecondaryButton>
          </div>
          
          {availableDowngrades.length > 1 && (
            <p className="text-xs text-jobblogg-text-muted">
              +{availableDowngrades.length - 1} andre nedgraderingsalternativer tilgjengelig
            </p>
          )}
        </div>
      )}

      {/* View All Plans Button */}
      <div className="text-center">
        <SecondaryButton
          onClick={onViewPlans}
          disabled={isLoading}
          className="w-full sm:w-auto"
        >
          Se alle planer og funksjoner
        </SecondaryButton>
      </div>

      {/* Plan Change Benefits */}
      <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-lg p-4">
        <h5 className="font-medium text-jobblogg-text mb-2">Fordeler med planendring</h5>
        <ul className="text-sm text-jobblogg-text-muted space-y-1">
          <li>• Endringer trer i kraft umiddelbart</li>
          <li>• Forholdsmessig fakturering for oppgraderinger</li>
          <li>• Ingen bindingstid - endre når som helst</li>
          <li>• Beholder alle data ved planendringer</li>
        </ul>
      </div>
    </div>
  );
};

// Utility component for inline upgrade prompts
export interface InlineUpgradePromptProps {
  feature: string;
  requiredPlan: string;
  onUpgrade: () => void;
  className?: string;
}

export const InlineUpgradePrompt: React.FC<InlineUpgradePromptProps> = ({
  feature,
  requiredPlan,
  onUpgrade,
  className = ''
}) => {
  return (
    <div className={`bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-4 text-center space-y-3 ${className}`}>
      <div>
        <h4 className="font-medium text-jobblogg-text">Oppgrader for å bruke {feature}</h4>
        <p className="text-sm text-jobblogg-text-muted mt-1">
          Denne funksjonen krever {PLAN_NAMES[requiredPlan]} plan eller høyere
        </p>
      </div>
      <PrimaryButton onClick={onUpgrade} size="sm">
        Oppgrader til {PLAN_NAMES[requiredPlan]}
      </PrimaryButton>
    </div>
  );
};

// Utility component for feature limitation warnings
export interface FeatureLimitationWarningProps {
  currentUsage: number;
  limit: number;
  feature: string;
  onUpgrade: () => void;
  className?: string;
}

export const FeatureLimitationWarning: React.FC<FeatureLimitationWarningProps> = ({
  currentUsage,
  limit,
  feature,
  onUpgrade,
  className = ''
}) => {
  const usagePercentage = (currentUsage / limit) * 100;
  const isNearLimit = usagePercentage >= 80;
  const isAtLimit = currentUsage >= limit;

  if (!isNearLimit) return null;

  return (
    <div className={`${
      isAtLimit 
        ? 'bg-jobblogg-error-light border-jobblogg-error' 
        : 'bg-jobblogg-warning-light border-jobblogg-warning'
    } border rounded-lg p-4 space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium text-jobblogg-text">
            {isAtLimit ? 'Grense nådd' : 'Nærmer seg grense'}
          </h4>
          <p className="text-sm text-jobblogg-text-muted">
            {currentUsage} av {limit} {feature} brukt ({Math.round(usagePercentage)}%)
          </p>
        </div>
        <PrimaryButton onClick={onUpgrade} size="sm">
          Oppgrader
        </PrimaryButton>
      </div>
      
      {/* Progress bar */}
      <div className="w-full bg-white rounded-full h-2">
        <div 
          className={`h-2 rounded-full ${
            isAtLimit ? 'bg-jobblogg-error' : 'bg-jobblogg-warning'
          }`}
          style={{ width: `${Math.min(usagePercentage, 100)}%` }}
        />
      </div>
    </div>
  );
};
