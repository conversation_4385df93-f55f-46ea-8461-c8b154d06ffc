import { useUser } from '@clerk/clerk-react';
import { useMutation } from 'convex/react';
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { api } from '../../../convex/_generated/api';
import { PrimaryButton } from '../ui/Button';
import { Modal } from '../ui/Dialog';
import { Heading3 } from '../ui/Typography';

export interface PortalReturnState {
  isProcessing: boolean;
  success: boolean;
  flow?: string;
  message?: string;
  error?: string;
}

export interface PortalReturnHandlerProps {
  onReturnProcessed?: (success: boolean, flow?: string) => void;
  autoRedirect?: boolean;
  redirectDelay?: number;
}

export const PortalReturnHandler: React.FC<PortalReturnHandlerProps> = ({
  onReturnProcessed,
  autoRedirect = true,
  redirectDelay = 3000
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useUser();
  
  const [returnState, setReturnState] = useState<PortalReturnState>({
    isProcessing: false,
    success: false,
  });

  const handlePortalReturn = useMutation(api.billingPortal?.handlePortalReturn);

  useEffect(() => {
    const portalReturn = searchParams.get('portal_return');
    const flow = searchParams.get('flow');
    const sessionId = searchParams.get('session_id');
    const success = searchParams.get('success');
    const error = searchParams.get('error');

    if (portalReturn === 'true' && user) {
      processPortalReturn(flow, sessionId, success, error);
    }
  }, [user, searchParams]);

  const processPortalReturn = async (
    flow: string | null,
    sessionId: string | null,
    success: string | null,
    error: string | null
  ) => {
    if (!user) return;

    setReturnState({
      isProcessing: true,
      success: false,
      flow: flow || undefined,
    });

    try {
      // Handle portal return through Convex
      await handlePortalReturn({
        userId: user.id,
        sessionId: sessionId || undefined,
        flow: flow || undefined,
      });

      // Determine success based on URL parameters or assume success if no error
      const isSuccess = success === 'true' || (success !== 'false' && !error);
      const message = getReturnMessage(flow, isSuccess, error);

      setReturnState({
        isProcessing: false,
        success: isSuccess,
        flow: flow || undefined,
        message,
        error: error || undefined,
      });

      // Clean up URL parameters
      cleanupUrlParameters();

      // Notify parent component
      onReturnProcessed?.(isSuccess, flow || undefined);

      // Auto-redirect if successful and enabled
      if (isSuccess && autoRedirect) {
        setTimeout(() => {
          navigate('/subscription', { replace: true });
        }, redirectDelay);
      }

    } catch (err) {
      console.error('Failed to process portal return:', err);
      
      setReturnState({
        isProcessing: false,
        success: false,
        flow: flow || undefined,
        error: 'Kunne ikke behandle retur fra faktureringsportal',
      });

      cleanupUrlParameters();
      onReturnProcessed?.(false, flow || undefined);
    }
  };

  const cleanupUrlParameters = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete('portal_return');
    newSearchParams.delete('flow');
    newSearchParams.delete('session_id');
    newSearchParams.delete('success');
    newSearchParams.delete('error');
    
    setSearchParams(newSearchParams, { replace: true });
  };

  const getReturnMessage = (flow: string | null, success: boolean, error: string | null): string => {
    if (error) {
      return `Feil: ${error}`;
    }

    if (!success) {
      return 'Endringene ble ikke lagret. Prøv igjen eller kontakt support.';
    }

    switch (flow) {
      case 'subscription_cancel':
        return 'Abonnementet ditt er avbrutt. Du beholder tilgang til slutten av faktureringsperioden.';
      
      case 'subscription_update':
        return 'Abonnementet ditt er oppdatert. Endringene trer i kraft umiddelbart.';
      
      case 'payment_method_update':
        return 'Betalingsmetoden din er oppdatert. Fremtidige fakturaer vil bruke den nye metoden.';
      
      case 'invoice_history':
        return 'Fakturahistorikken er tilgjengelig. Du kan laste ned fakturaer fra portalen.';
      
      default:
        return 'Endringene er lagret og vil tre i kraft umiddelbart.';
    }
  };

  const getReturnIcon = (success: boolean, flow?: string): string => {
    if (!success) return '❌';
    
    switch (flow) {
      case 'subscription_cancel':
        return '🚫';
      case 'subscription_update':
        return '📊';
      case 'payment_method_update':
        return '💳';
      case 'invoice_history':
        return '📄';
      default:
        return '✅';
    }
  };

  const getReturnTitle = (success: boolean, flow?: string): string => {
    if (!success) return 'Noe gikk galt';
    
    switch (flow) {
      case 'subscription_cancel':
        return 'Abonnement avbrutt';
      case 'subscription_update':
        return 'Abonnement oppdatert';
      case 'payment_method_update':
        return 'Betalingsmetode oppdatert';
      case 'invoice_history':
        return 'Fakturahistorikk tilgjengelig';
      default:
        return 'Endringer lagret';
    }
  };

  // Don't render if not processing a return
  if (!returnState.isProcessing && !returnState.message) {
    return null;
  }

  return (
    <Modal 
      isOpen={true} 
      onClose={() => navigate('/subscription', { replace: true })}
      size="md"
    >
      <div className="space-y-6">
        {returnState.isProcessing ? (
          // Processing state
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-jobblogg-primary-light rounded-full flex items-center justify-center mx-auto">
              <div className="w-8 h-8 border-2 border-jobblogg-primary border-t-transparent rounded-full animate-spin"></div>
            </div>
            
            <div>
              <Heading3 className="mb-2">Behandler endringer...</Heading3>
              <p className="text-jobblogg-text-muted">
                Vi oppdaterer abonnementet ditt basert på endringene fra faktureringsportalen.
              </p>
            </div>
          </div>
        ) : (
          // Result state
          <div className="text-center space-y-4">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto ${
              returnState.success 
                ? 'bg-jobblogg-success-light' 
                : 'bg-jobblogg-error-light'
            }`}>
              <span className="text-3xl">
                {getReturnIcon(returnState.success, returnState.flow)}
              </span>
            </div>
            
            <div>
              <Heading3 className="mb-2">
                {getReturnTitle(returnState.success, returnState.flow)}
              </Heading3>
              <p className="text-jobblogg-text-muted">
                {returnState.message}
              </p>
            </div>
          </div>
        )}

        {/* Additional Information */}
        {!returnState.isProcessing && (
          <div className={`rounded-lg p-4 ${
            returnState.success 
              ? 'bg-jobblogg-success-light border border-jobblogg-success' 
              : 'bg-jobblogg-error-light border border-jobblogg-error'
          }`}>
            <h4 className="font-medium text-jobblogg-text mb-2">
              {returnState.success ? 'Hva skjer nå?' : 'Hva kan du gjøre?'}
            </h4>
            
            {returnState.success ? (
              <ul className="text-sm text-jobblogg-text-muted space-y-1">
                <li>• Endringene er synkronisert med JobbLogg</li>
                <li>• Du vil motta en e-postbekreftelse fra Stripe</li>
                <li>• Abonnementsdetaljene oppdateres automatisk</li>
                {autoRedirect && (
                  <li>• Du blir omdirigert til abonnementssiden om {redirectDelay / 1000} sekunder</li>
                )}
              </ul>
            ) : (
              <ul className="text-sm text-jobblogg-text-muted space-y-1">
                <li>• Prøv å åpne faktureringsportalen igjen</li>
                <li>• Kontakt support hvis problemet vedvarer</li>
                <li>• Sjekk e-posten din for eventuelle bekreftelser</li>
              </ul>
            )}
          </div>
        )}

        {/* Action Button */}
        {!returnState.isProcessing && (
          <div className="text-center">
            <PrimaryButton
              onClick={() => navigate('/subscription', { replace: true })}
              className="w-full sm:w-auto"
            >
              {returnState.success ? 'Gå til abonnement' : 'Prøv igjen'}
            </PrimaryButton>
          </div>
        )}

        {/* Support Information */}
        {!returnState.isProcessing && !returnState.success && (
          <div className="text-center text-xs text-jobblogg-text-muted">
            <p>
              Trenger du hjelp? Kontakt oss på{' '}
              <a href="mailto:<EMAIL>" className="text-jobblogg-primary hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        )}
      </div>
    </Modal>
  );
};

/**
 * Hook for managing portal return state
 */
export function usePortalReturn() {
  const [searchParams] = useSearchParams();
  const { user } = useUser();
  
  const isPortalReturn = searchParams.get('portal_return') === 'true';
  const flow = searchParams.get('flow');
  const sessionId = searchParams.get('session_id');
  const success = searchParams.get('success');
  const error = searchParams.get('error');

  const hasReturnData = isPortalReturn && user;

  return {
    isPortalReturn,
    hasReturnData,
    flow,
    sessionId,
    success: success === 'true',
    error,
    returnData: {
      flow,
      sessionId,
      success: success === 'true',
      error,
    },
  };
}

/**
 * Utility function to generate portal return URLs
 */
export function generatePortalReturnUrl(
  baseUrl: string,
  flow?: string,
  additionalParams?: Record<string, string>
): string {
  const url = new URL(baseUrl);
  url.searchParams.set('portal_return', 'true');
  
  if (flow) {
    url.searchParams.set('flow', flow);
  }
  
  if (additionalParams) {
    Object.entries(additionalParams).forEach(([key, value]) => {
      url.searchParams.set(key, value);
    });
  }
  
  return url.toString();
}
