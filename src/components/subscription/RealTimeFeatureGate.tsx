import React from 'react';
import { useFeatureAccess } from '../../hooks/useRealTimeSubscriptionAccess';
import { DisabledButton } from './DisabledButton';
import { DisabledFeature } from './DisabledFeature';

interface RealTimeFeatureGateProps {
  children: React.ReactNode;
  feature: 'create_project' | 'team_management' | 'project_sharing' | 'file_upload' | 'full_access' | 'view_projects' | 'read_only';
  variant?: 'block' | 'disable' | 'button';
  fallback?: React.ReactNode;
  className?: string;
}

/**
 * Real-time feature gate that immediately restricts access when trial expires
 * This component provides instant feedback without requiring page refresh
 */
export const RealTimeFeatureGate: React.FC<RealTimeFeatureGateProps> = ({
  children,
  feature,
  variant = 'block',
  fallback,
  className = ''
}) => {
  const { hasAccess, isLoading, isRealTimeExpired, restrictionReason } = useFeatureAccess(feature);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-jobblogg-primary"></div>
      </div>
    );
  }

  // If user has access, show the children
  if (hasAccess) {
    return <>{children}</>;
  }

  // Handle restricted access based on variant
  if (variant === 'button') {
    return (
      <DisabledButton
        feature={feature as any}
        reason={restrictionReason}
        className={className}
      >
        {children}
      </DisabledButton>
    );
  }

  if (variant === 'disable') {
    return (
      <DisabledFeature
        feature={feature as any}
        reason={restrictionReason}
        className={className}
      >
        {children}
      </DisabledFeature>
    );
  }

  // Default block variant
  return fallback || (
    <div className="text-center py-8">
      <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
        {isRealTimeExpired ? 'Prøveperioden er utløpt' : 'Funksjon ikke tilgjengelig'}
      </h3>
      <p className="text-jobblogg-text-medium">
        {isRealTimeExpired 
          ? 'Oppgrader til en betalt plan for å fortsette å bruke denne funksjonen.'
          : 'Oppgrader til en betalt plan for å få tilgang til denne funksjonen.'
        }
      </p>
    </div>
  );
};
