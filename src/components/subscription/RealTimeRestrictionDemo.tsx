import React from 'react';
import { useRealTimeSubscriptionAccess } from '../../hooks/useRealTimeSubscriptionAccess';
import { RealTimeFeatureGate } from './RealTimeFeatureGate';
import { PrimaryButton, SecondaryButton } from '../ui';

/**
 * Demo component to showcase real-time feature restrictions
 * This component demonstrates how features are immediately restricted when trial expires
 */
export const RealTimeRestrictionDemo: React.FC = () => {
  const {
    isInTrial,
    isTrialExpired,
    isRealTimeExpired,
    trialEnd,
    currentTime,
    shouldShowExpiredStatus,
    featureAccess
  } = useRealTimeSubscriptionAccess();

  const timeUntilExpiration = trialEnd - currentTime;
  const minutesLeft = Math.max(0, Math.floor(timeUntilExpiration / (60 * 1000)));
  const secondsLeft = Math.max(0, Math.floor((timeUntilExpiration % (60 * 1000)) / 1000));

  return (
    <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">
          🧪 Real-Time Feature Restrictions Demo
        </h3>
        <p className="text-sm text-jobblogg-text-medium">
          This demo shows how features are immediately restricted when the trial expires
        </p>
      </div>

      {/* Trial Status Display */}
      <div className="bg-jobblogg-neutral-soft rounded-lg p-4">
        <h4 className="font-medium text-jobblogg-text-strong mb-2">Current Trial Status:</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-jobblogg-text-medium">Trial Active:</span>
            <span className={`ml-2 font-medium ${isInTrial ? 'text-jobblogg-success' : 'text-jobblogg-error'}`}>
              {isInTrial ? '✅ Yes' : '❌ No'}
            </span>
          </div>
          <div>
            <span className="text-jobblogg-text-medium">Real-Time Expired:</span>
            <span className={`ml-2 font-medium ${isRealTimeExpired ? 'text-jobblogg-error' : 'text-jobblogg-success'}`}>
              {isRealTimeExpired ? '⚠️ Yes' : '✅ No'}
            </span>
          </div>
          <div>
            <span className="text-jobblogg-text-medium">Backend Expired:</span>
            <span className={`ml-2 font-medium ${isTrialExpired ? 'text-jobblogg-error' : 'text-jobblogg-success'}`}>
              {isTrialExpired ? '⚠️ Yes' : '✅ No'}
            </span>
          </div>
          <div>
            <span className="text-jobblogg-text-medium">Show Expired UI:</span>
            <span className={`ml-2 font-medium ${shouldShowExpiredStatus ? 'text-jobblogg-error' : 'text-jobblogg-success'}`}>
              {shouldShowExpiredStatus ? '⚠️ Yes' : '✅ No'}
            </span>
          </div>
        </div>
        
        {isInTrial && !isRealTimeExpired && (
          <div className="mt-3 p-3 bg-jobblogg-primary-soft rounded-lg">
            <p className="text-sm font-medium text-jobblogg-primary">
              ⏰ Time remaining: {minutesLeft}m {secondsLeft}s
            </p>
            <p className="text-xs text-jobblogg-text-medium mt-1">
              Features will be restricted automatically when this reaches zero
            </p>
          </div>
        )}
      </div>

      {/* Feature Access Status */}
      <div className="bg-jobblogg-neutral-soft rounded-lg p-4">
        <h4 className="font-medium text-jobblogg-text-strong mb-2">Feature Access Status:</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          {Object.entries(featureAccess).map(([feature, hasAccess]) => (
            <div key={feature} className="flex items-center justify-between">
              <span className="text-jobblogg-text-medium capitalize">
                {feature.replace(/([A-Z])/g, ' $1').trim()}:
              </span>
              <span className={`font-medium ${hasAccess ? 'text-jobblogg-success' : 'text-jobblogg-error'}`}>
                {hasAccess ? '✅' : '🔒'}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Demo Buttons with Real-Time Restrictions */}
      <div className="space-y-4">
        <h4 className="font-medium text-jobblogg-text-strong">Demo Buttons (Try clicking when trial expires):</h4>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <RealTimeFeatureGate feature="create_project" variant="button">
            <PrimaryButton className="w-full">
              🚀 Create Project
            </PrimaryButton>
          </RealTimeFeatureGate>

          <RealTimeFeatureGate feature="team_management" variant="button">
            <SecondaryButton className="w-full">
              👥 Manage Team
            </SecondaryButton>
          </RealTimeFeatureGate>

          <RealTimeFeatureGate feature="project_sharing" variant="button">
            <SecondaryButton className="w-full">
              🔗 Share Project
            </SecondaryButton>
          </RealTimeFeatureGate>

          <RealTimeFeatureGate feature="file_upload" variant="button">
            <SecondaryButton className="w-full">
              📁 Upload Files
            </SecondaryButton>
          </RealTimeFeatureGate>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-jobblogg-primary-soft rounded-lg p-4">
        <h4 className="font-medium text-jobblogg-primary mb-2">How to Test:</h4>
        <ol className="text-sm text-jobblogg-text-medium space-y-1 list-decimal list-inside">
          <li>Watch the timer count down (if trial is active)</li>
          <li>Notice how feature access status changes in real-time</li>
          <li>Try clicking the demo buttons before and after expiration</li>
          <li>Observe that restrictions apply immediately without page refresh</li>
          <li>Compare "Real-Time Expired" vs "Backend Expired" status</li>
        </ol>
      </div>
    </div>
  );
};
