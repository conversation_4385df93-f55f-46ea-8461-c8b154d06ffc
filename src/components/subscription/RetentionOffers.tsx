import React from 'react';
import { PrimaryButton, SecondaryButton } from '../ui/Button';

export interface RetentionOffer {
  id: string;
  title: string;
  description: string;
  icon: string;
  buttonText: string;
  onAccept: () => void;
}

export interface RetentionOffersProps {
  reason: string;
  onAcceptOffer: (offerId: string) => void;
  onDeclineAll: () => void;
  isProcessing?: boolean;
}

export const RetentionOffers: React.FC<RetentionOffersProps> = ({
  reason,
  onAcceptOffer,
  onDeclineAll,
  isProcessing = false
}) => {
  const getOffersForReason = (reason: string): RetentionOffer[] => {
    switch (reason) {
      case 'too_expensive':
        return [
          {
            id: 'discount_25',
            title: '25% rabatt i 3 måneder',
            description: 'Vi kan tilby deg 25% rabatt på de neste 3 månedene. Dette gir deg mer tid til å se verdien av JobbLogg.',
            icon: '💰',
            buttonText: 'Aktiver rabatt',
            onAccept: () => onAcceptOffer('discount_25'),
          },
          {
            id: 'downgrade_offer',
            title: 'Nedgrader til Basic',
            description: 'Kanskje Basic-planen passer bedre for dine behov? Du kan alltid oppgradere senere.',
            icon: '📉',
            buttonText: 'Bytt til Basic',
            onAccept: () => onAcceptOffer('downgrade_offer'),
          },
        ];

      case 'not_using':
        return [
          {
            id: 'free_training',
            title: 'Gratis personlig opplæring',
            description: 'Vi kan tilby deg en gratis 30-minutters opplæringssesjon for å hjelpe deg å få mest mulig ut av JobbLogg.',
            icon: '📚',
            buttonText: 'Book opplæring',
            onAccept: () => onAcceptOffer('free_training'),
          },
          {
            id: 'pause_subscription',
            title: 'Pause abonnementet',
            description: 'Pause abonnementet i opptil 3 måneder uten å miste dataene dine. Perfekt for sesongbaserte virksomheter.',
            icon: '⏸️',
            buttonText: 'Pause abonnement',
            onAccept: () => onAcceptOffer('pause_subscription'),
          },
        ];

      case 'missing_features':
        return [
          {
            id: 'feature_roadmap',
            title: 'Prioritert funksjonsutvikling',
            description: 'Vi kan prioritere funksjonene du trenger og kontakte deg når de er klare. Få 50% rabatt når de lanseres.',
            icon: '🚀',
            buttonText: 'Prioriter mine funksjoner',
            onAccept: () => onAcceptOffer('feature_roadmap'),
          },
          {
            id: 'beta_access',
            title: 'Beta-tilgang til nye funksjoner',
            description: 'Få tidlig tilgang til nye funksjoner og påvirk utviklingen av JobbLogg.',
            icon: '🧪',
            buttonText: 'Bli beta-tester',
            onAccept: () => onAcceptOffer('beta_access'),
          },
        ];

      case 'technical_issues':
        return [
          {
            id: 'priority_support',
            title: 'Prioritert teknisk støtte',
            description: 'Vi kan gi deg direkte tilgang til vårt tekniske team for å løse problemene dine raskt.',
            icon: '🛠️',
            buttonText: 'Få prioritert støtte',
            onAccept: () => onAcceptOffer('priority_support'),
          },
          {
            id: 'personal_setup',
            title: 'Personlig oppsett og konfigurasjon',
            description: 'La oss hjelpe deg med å sette opp JobbLogg perfekt for din virksomhet.',
            icon: '⚙️',
            buttonText: 'Book personlig oppsett',
            onAccept: () => onAcceptOffer('personal_setup'),
          },
        ];

      case 'switching_competitor':
        return [
          {
            id: 'match_competitor',
            title: 'Vi matcher konkurrentens pris',
            description: 'Send oss tilbudet fra konkurrenten, så matcher vi prisen og gir deg 10% ekstra rabatt.',
            icon: '🤝',
            buttonText: 'Send konkurrenttilbud',
            onAccept: () => onAcceptOffer('match_competitor'),
          },
          {
            id: 'migration_help',
            title: 'Gratis datamigrasjon',
            description: 'Vi hjelper deg å migrere dataene dine fra konkurrenten tilbake til JobbLogg - helt gratis.',
            icon: '📦',
            buttonText: 'Få migrasjonshjelp',
            onAccept: () => onAcceptOffer('migration_help'),
          },
        ];

      case 'temporary_pause':
        return [
          {
            id: 'extended_pause',
            title: 'Utvidet pause-periode',
            description: 'Pause abonnementet i opptil 6 måneder uten å miste dataene dine. Perfekt for lengre pauser.',
            icon: '⏸️',
            buttonText: 'Pause i 6 måneder',
            onAccept: () => onAcceptOffer('extended_pause'),
          },
          {
            id: 'seasonal_discount',
            title: 'Sesongrabatt',
            description: 'Få 40% rabatt når du kommer tilbake, uansett når det er.',
            icon: '🌟',
            buttonText: 'Aktiver sesongrabatt',
            onAccept: () => onAcceptOffer('seasonal_discount'),
          },
        ];

      default:
        return [
          {
            id: 'general_discount',
            title: '20% rabatt i 2 måneder',
            description: 'Vi vil gjerne beholde deg som kunde. Få 20% rabatt på de neste 2 månedene.',
            icon: '💝',
            buttonText: 'Aktiver rabatt',
            onAccept: () => onAcceptOffer('general_discount'),
          },
        ];
    }
  };

  const offers = getOffersForReason(reason);

  if (offers.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-jobblogg-text">Vent litt!</h3>
        <p className="text-jobblogg-text-muted">
          Vi har noen tilbud som kanskje kan hjelpe
        </p>
      </div>

      <div className="space-y-4">
        {offers.map((offer) => (
          <div
            key={offer.id}
            className="bg-gradient-to-r from-jobblogg-primary-light to-jobblogg-primary-lighter rounded-lg p-6 space-y-4"
          >
            <div className="flex items-start space-x-4">
              <div className="text-3xl">{offer.icon}</div>
              <div className="flex-1">
                <h4 className="font-medium text-jobblogg-text mb-2">{offer.title}</h4>
                <p className="text-sm text-jobblogg-text-muted mb-4">
                  {offer.description}
                </p>
                <PrimaryButton
                  onClick={offer.onAccept}
                  disabled={isProcessing}
                  className="w-full sm:w-auto"
                >
                  {isProcessing ? 'Behandler...' : offer.buttonText}
                </PrimaryButton>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="text-center space-y-4">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-jobblogg-border"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-jobblogg-text-muted">eller</span>
          </div>
        </div>

        <button
          onClick={onDeclineAll}
          disabled={isProcessing}
          className="text-jobblogg-text-muted hover:text-jobblogg-text text-sm underline transition-colors"
        >
          Nei takk, jeg vil fortsatt avbryte
        </button>
      </div>

      <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-lg p-4">
        <h4 className="font-medium text-jobblogg-text mb-2">💡 Visste du at?</h4>
        <ul className="text-sm text-jobblogg-text-muted space-y-1">
          <li>• 89% av våre kunder som får personlig opplæring fortsetter å bruke JobbLogg</li>
          <li>• Gjennomsnittlig sparer våre kunder 8 timer per uke på prosjektdokumentasjon</li>
          <li>• Du kan alltid endre eller avbryte abonnementet ditt senere</li>
        </ul>
      </div>
    </div>
  );
};
