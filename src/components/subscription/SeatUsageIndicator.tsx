import React from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { useSeatManagement } from '../../hooks/useSubscriptionAccess';

export const SeatUsageIndicator: React.FC = () => {
  const { user } = useUser();

  // Get accurate team member count
  const teamMemberCount = useQuery(
    api.seatManagement.getTeamMemberCount,
    user?.id ? { userId: user.id } : "skip"
  );

  const {
    isLoading: seatManagementLoading,
    maxSeats: fallbackMaxSeats,
  } = useSeatManagement();

  // Use accurate count if available, fallback to seat management hook
  const isLoading = seatManagementLoading || teamMemberCount === undefined;
  const currentSeats = (teamMemberCount as any)?.actualTeamMemberCount || 0;
  const maxSeats = (teamMemberCount as any)?.maxSeats || fallbackMaxSeats || 0;
  const remainingSeats = Math.max(0, maxSeats - currentSeats);
  const isAtLimit = currentSeats >= maxSeats;
  const isCritical = currentSeats >= Math.floor(maxSeats * 0.9);
  const isNearLimit = currentSeats >= Math.floor(maxSeats * 0.8);

  if (isLoading || maxSeats === 0) return null;

  const usagePercentage = (currentSeats / maxSeats) * 100;

  const getStatusColor = () => {
    if (isAtLimit) return 'text-jobblogg-error';
    if (isCritical) return 'text-jobblogg-warning';
    if (isNearLimit) return 'text-jobblogg-accent';
    return 'text-jobblogg-text-medium';
  };

  const getBackgroundColor = () => {
    if (isAtLimit) return 'bg-jobblogg-error-soft border-jobblogg-error';
    if (isCritical) return 'bg-jobblogg-warning-soft border-jobblogg-warning';
    if (isNearLimit) return 'bg-jobblogg-accent-soft border-jobblogg-accent';
    return 'bg-jobblogg-surface border-jobblogg-border';
  };

  const getStatusMessage = () => {
    if (isAtLimit) return 'Plangrense nådd - oppgrader for å legge til flere';
    if (isCritical) return `Kun ${remainingSeats} ${remainingSeats === 1 ? 'plass' : 'plasser'} igjen`;
    if (isNearLimit) return 'Du nærmer deg plangrensen';
    return `${remainingSeats} ${remainingSeats === 1 ? 'plass' : 'plasser'} tilgjengelig`;
  };

  return (
    <div className={`p-4 rounded-lg border ${getBackgroundColor()}`}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium text-jobblogg-text-strong">
          Team medlemmer
        </span>
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {currentSeats} / {maxSeats}
        </span>
      </div>

      {/* Progress bar */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            isAtLimit ? 'bg-jobblogg-error' :
            isCritical ? 'bg-jobblogg-warning' :
            isNearLimit ? 'bg-jobblogg-accent' : 'bg-jobblogg-primary'
          }`}
          style={{ width: `${Math.min(100, usagePercentage)}%` }}
        />
      </div>

      {/* Status message */}
      <p className={`text-xs ${getStatusColor()}`}>
        {getStatusMessage()}
      </p>

      {/* Upgrade suggestion for critical/at limit */}
      {(isCritical || isAtLimit) && (
        <div className="mt-2 pt-2 border-t border-current border-opacity-20">
          <p className="text-xs text-jobblogg-text-medium">
            Vurder å oppgradere til en større plan for flere team medlemmer.
          </p>
        </div>
      )}
    </div>
  );
};
