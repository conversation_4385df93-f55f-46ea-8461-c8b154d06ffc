import React, { useState } from 'react';
import { PrimaryButton, SecondaryButton } from '../ui/Button';
import { Modal } from '../ui/Dialog';
import { Heading3 } from '../ui/Typography';

export interface CancellationReason {
  id: string;
  label: string;
  requiresDetails?: boolean;
}

export interface CancellationFeedback {
  reason: string;
  details?: string;
  wouldRecommend: boolean;
  improvementSuggestions?: string;
}

export interface SubscriptionCancellationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCancel: (feedback: CancellationFeedback, cancelImmediately: boolean) => void;
  subscriptionDetails: {
    planName: string;
    nextBillingDate: string;
    amount: number;
    billingInterval: 'month' | 'year';
  } | null;
  isProcessing?: boolean;
}

const CANCELLATION_REASONS: CancellationReason[] = [
  { id: 'too_expensive', label: 'For dyrt' },
  { id: 'not_using', label: '<PERSON>ruker det ikke nok' },
  { id: 'missing_features', label: 'Mangler funksjoner jeg trenger', requiresDetails: true },
  { id: 'technical_issues', label: 'Tekniske problemer', requiresDetails: true },
  { id: 'switching_competitor', label: 'Bytter til konkurrent', requiresDetails: true },
  { id: 'business_closing', label: 'Avvikler virksomheten' },
  { id: 'temporary_pause', label: 'Midlertidig pause' },
  { id: 'other', label: 'Annet', requiresDetails: true },
];

export const SubscriptionCancellationModal: React.FC<SubscriptionCancellationModalProps> = ({
  isOpen,
  onClose,
  onCancel,
  subscriptionDetails,
  isProcessing = false
}) => {
  const [step, setStep] = useState<'reason' | 'retention' | 'confirmation'>('reason');
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [reasonDetails, setReasonDetails] = useState<string>('');
  const [wouldRecommend, setWouldRecommend] = useState<boolean>(true);
  const [improvementSuggestions, setImprovementSuggestions] = useState<string>('');
  const [cancelImmediately, setCancelImmediately] = useState<boolean>(false);

  const selectedReasonData = CANCELLATION_REASONS.find(r => r.id === selectedReason);
  const requiresDetails = selectedReasonData?.requiresDetails || false;

  const handleReasonSubmit = () => {
    if (!selectedReason) return;
    if (requiresDetails && !reasonDetails.trim()) return;
    
    // Show retention offers for certain reasons
    if (['too_expensive', 'not_using', 'missing_features'].includes(selectedReason)) {
      setStep('retention');
    } else {
      setStep('confirmation');
    }
  };

  const handleRetentionDecline = () => {
    setStep('confirmation');
  };

  const handleFinalCancel = () => {
    const feedback: CancellationFeedback = {
      reason: selectedReason,
      details: reasonDetails.trim() || undefined,
      wouldRecommend,
      improvementSuggestions: improvementSuggestions.trim() || undefined,
    };
    
    onCancel(feedback, cancelImmediately);
  };

  const handleClose = () => {
    if (!isProcessing) {
      setStep('reason');
      setSelectedReason('');
      setReasonDetails('');
      setWouldRecommend(true);
      setImprovementSuggestions('');
      setCancelImmediately(false);
      onClose();
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const formatPrice = (amount: number, interval: 'month' | 'year') => {
    const period = interval === 'year' ? 'år' : 'måned';
    return `${amount.toLocaleString('nb-NO')} NOK per ${period}`;
  };

  if (!subscriptionDetails) return null;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md">
      <div className="space-y-6">
        {/* Step 1: Reason Selection */}
        {step === 'reason' && (
          <>
            <div className="text-center space-y-2">
              <Heading3>Vi er lei oss for å se deg dra</Heading3>
              <p className="text-jobblogg-text-muted">
                Kan du fortelle oss hvorfor du vil avbryte abonnementet ditt?
              </p>
            </div>

            <div className="space-y-3">
              {CANCELLATION_REASONS.map((reason) => (
                <label
                  key={reason.id}
                  className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedReason === reason.id
                      ? 'border-jobblogg-primary bg-jobblogg-primary-light'
                      : 'border-jobblogg-border hover:border-jobblogg-primary-light'
                  }`}
                >
                  <input
                    type="radio"
                    name="cancellation-reason"
                    value={reason.id}
                    checked={selectedReason === reason.id}
                    onChange={(e) => setSelectedReason(e.target.value)}
                    className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border focus:ring-jobblogg-primary focus:ring-2"
                  />
                  <span className="ml-3 text-jobblogg-text">{reason.label}</span>
                </label>
              ))}
            </div>

            {requiresDetails && selectedReason && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-jobblogg-text">
                  Kan du utdype? (valgfritt)
                </label>
                <textarea
                  value={reasonDetails}
                  onChange={(e) => setReasonDetails(e.target.value)}
                  placeholder="Fortell oss mer om din situasjon..."
                  rows={3}
                  className="w-full px-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary resize-none"
                />
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <SecondaryButton onClick={handleClose} className="flex-1">
                Avbryt
              </SecondaryButton>
              <PrimaryButton
                onClick={handleReasonSubmit}
                disabled={!selectedReason || (requiresDetails && !reasonDetails.trim())}
                className="flex-1"
              >
                Fortsett
              </PrimaryButton>
            </div>
          </>
        )}

        {/* Step 2: Retention Offers */}
        {step === 'retention' && (
          <>
            <div className="text-center space-y-2">
              <Heading3>Vent litt!</Heading3>
              <p className="text-jobblogg-text-muted">
                Vi har noen tilbud som kanskje kan hjelpe
              </p>
            </div>

            <div className="space-y-4">
              {selectedReason === 'too_expensive' && (
                <div className="bg-jobblogg-success-light border border-jobblogg-success rounded-lg p-4">
                  <h4 className="font-medium text-jobblogg-text mb-2">💰 Spesialtilbud</h4>
                  <p className="text-sm text-jobblogg-text-muted mb-3">
                    Vi kan tilby deg 25% rabatt på neste faktura. Dette gir deg mer tid til å se verdien av JobbLogg.
                  </p>
                  <PrimaryButton size="sm" className="w-full">
                    Aktiver 25% rabatt
                  </PrimaryButton>
                </div>
              )}

              {selectedReason === 'not_using' && (
                <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-lg p-4">
                  <h4 className="font-medium text-jobblogg-text mb-2">📚 Gratis opplæring</h4>
                  <p className="text-sm text-jobblogg-text-muted mb-3">
                    Vi kan tilby deg en gratis 30-minutters opplæringssesjon for å hjelpe deg å få mest mulig ut av JobbLogg.
                  </p>
                  <PrimaryButton size="sm" className="w-full">
                    Book gratis opplæring
                  </PrimaryButton>
                </div>
              )}

              {selectedReason === 'missing_features' && (
                <div className="bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-4">
                  <h4 className="font-medium text-jobblogg-text mb-2">🚀 Kommende funksjoner</h4>
                  <p className="text-sm text-jobblogg-text-muted mb-3">
                    Vi jobber aktivt med nye funksjoner. Kan vi kontakte deg når funksjonene du trenger er klare?
                  </p>
                  <PrimaryButton size="sm" className="w-full">
                    Ja, kontakt meg
                  </PrimaryButton>
                </div>
              )}
            </div>

            <div className="text-center">
              <button
                onClick={handleRetentionDecline}
                className="text-jobblogg-text-muted hover:text-jobblogg-text text-sm underline"
              >
                Nei takk, jeg vil fortsatt avbryte
              </button>
            </div>
          </>
        )}

        {/* Step 3: Final Confirmation */}
        {step === 'confirmation' && (
          <>
            <div className="text-center space-y-2">
              <Heading3>Bekreft avbryting</Heading3>
              <p className="text-jobblogg-text-muted">
                Vi trenger bare noen siste detaljer før vi avbryter abonnementet ditt
              </p>
            </div>

            {/* Current Subscription Details */}
            <div className="bg-jobblogg-card-bg rounded-lg p-4 space-y-3">
              <h4 className="font-medium text-jobblogg-text">Ditt abonnement</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-jobblogg-text-muted">Plan:</span>
                  <span className="text-jobblogg-text font-medium">{subscriptionDetails.planName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-jobblogg-text-muted">Pris:</span>
                  <span className="text-jobblogg-text font-medium">
                    {formatPrice(subscriptionDetails.amount, subscriptionDetails.billingInterval)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-jobblogg-text-muted">Neste faktura:</span>
                  <span className="text-jobblogg-text font-medium">
                    {formatDate(subscriptionDetails.nextBillingDate)}
                  </span>
                </div>
              </div>
            </div>

            {/* Cancellation Options */}
            <div className="space-y-3">
              <h4 className="font-medium text-jobblogg-text">Når skal avbryting tre i kraft?</h4>
              
              <label className="flex items-start p-4 border border-jobblogg-border rounded-lg cursor-pointer">
                <input
                  type="radio"
                  name="cancellation-timing"
                  checked={!cancelImmediately}
                  onChange={() => setCancelImmediately(false)}
                  className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border focus:ring-jobblogg-primary focus:ring-2 mt-0.5"
                />
                <div className="ml-3">
                  <div className="font-medium text-jobblogg-text">Ved slutten av faktureringsperioden</div>
                  <div className="text-sm text-jobblogg-text-muted">
                    Du beholder tilgang til {formatDate(subscriptionDetails.nextBillingDate)} og blir ikke belastet igjen
                  </div>
                </div>
              </label>

              <label className="flex items-start p-4 border border-jobblogg-border rounded-lg cursor-pointer">
                <input
                  type="radio"
                  name="cancellation-timing"
                  checked={cancelImmediately}
                  onChange={() => setCancelImmediately(true)}
                  className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border focus:ring-jobblogg-primary focus:ring-2 mt-0.5"
                />
                <div className="ml-3">
                  <div className="font-medium text-jobblogg-text">Umiddelbart</div>
                  <div className="text-sm text-jobblogg-text-muted">
                    Mister tilgang med en gang, men får refundert ubrukt tid
                  </div>
                </div>
              </label>
            </div>

            {/* Feedback Questions */}
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-jobblogg-text">
                  Ville du anbefalt JobbLogg til andre?
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="recommend"
                      checked={wouldRecommend}
                      onChange={() => setWouldRecommend(true)}
                      className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border focus:ring-jobblogg-primary focus:ring-2"
                    />
                    <span className="ml-2 text-jobblogg-text">Ja</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="recommend"
                      checked={!wouldRecommend}
                      onChange={() => setWouldRecommend(false)}
                      className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border focus:ring-jobblogg-primary focus:ring-2"
                    />
                    <span className="ml-2 text-jobblogg-text">Nei</span>
                  </label>
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-jobblogg-text">
                  Hva kunne vi gjort bedre? (valgfritt)
                </label>
                <textarea
                  value={improvementSuggestions}
                  onChange={(e) => setImprovementSuggestions(e.target.value)}
                  placeholder="Dine forbedringsforslag..."
                  rows={3}
                  className="w-full px-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary resize-none"
                />
              </div>
            </div>

            <div className="bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-4">
              <h4 className="font-medium text-jobblogg-text mb-2">⚠️ Viktig informasjon</h4>
              <ul className="text-sm text-jobblogg-text-muted space-y-1">
                <li>• Alle prosjektdata vil bli bevart i 90 dager etter avbryting</li>
                <li>• Du kan reaktivere abonnementet når som helst</li>
                <li>• Teammedlemmer vil miste tilgang til delte prosjekter</li>
                <li>• Eksport av data er tilgjengelig før avbryting</li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <SecondaryButton onClick={handleClose} disabled={isProcessing} className="flex-1">
                Avbryt
              </SecondaryButton>
              <PrimaryButton
                onClick={handleFinalCancel}
                disabled={isProcessing}
                className="flex-1 bg-jobblogg-error hover:bg-jobblogg-error-dark"
              >
                {isProcessing ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Avbryter...</span>
                  </div>
                ) : (
                  'Bekreft avbryting'
                )}
              </PrimaryButton>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};
