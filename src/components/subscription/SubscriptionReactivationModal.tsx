import React, { useState } from 'react';
import { PrimaryButton, SecondaryButton } from '../ui/Button';
import { Modal } from '../ui/Dialog';
import { Heading3 } from '../ui/Typography';

export interface ReactivationPlan {
  id: string;
  name: string;
  monthlyPrice: number;
  annualPrice: number;
  description: string;
  features: string[];
  recommended?: boolean;
}

export interface SubscriptionReactivationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReactivate: (planId: string, billingInterval: 'month' | 'year', paymentMethodId?: string) => void;
  cancelledSubscription: {
    planLevel: string;
    cancelledAt: string;
    reason?: string;
    dataRetentionUntil: string;
  } | null;
  availablePlans: ReactivationPlan[];
  isProcessing?: boolean;
  hasValidPaymentMethod?: boolean;
}

const PLAN_FEATURES: Record<string, string[]> = {
  basic: [
    'Opptil 5 aktive prosjekter',
    'Grunnleggende prosjektdokumentasjon',
    'E-poststøtte',
    'Mobilapp tilgang'
  ],
  professional: [
    'Ubegrensede prosjekter',
    'Avansert prosjektdokumentasjon',
    'Teamsamarbeid og tilgangskontroll',
    'Prioritert e-poststøtte',
    'Avanserte rapporter og analyser',
    'API-tilgang'
  ],
  enterprise: [
    'Alt i Professional',
    'Dedikert kundesuksessansvarlig',
    'Tilpassede integrasjoner',
    'Avansert sikkerhet og compliance',
    'Ubegrenset lagringsplass',
    'Prioritert telefonstøtte'
  ]
};

export const SubscriptionReactivationModal: React.FC<SubscriptionReactivationModalProps> = ({
  isOpen,
  onClose,
  onReactivate,
  cancelledSubscription,
  availablePlans,
  isProcessing = false,
  hasValidPaymentMethod = false
}) => {
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [selectedBillingInterval, setSelectedBillingInterval] = useState<'month' | 'year'>('month');
  const [needsPaymentMethod, setNeedsPaymentMethod] = useState(!hasValidPaymentMethod);

  if (!cancelledSubscription) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const formatPrice = (monthlyPrice: number, annualPrice: number, interval: 'month' | 'year') => {
    if (interval === 'year') {
      const monthlySavings = Math.round((monthlyPrice * 12 - annualPrice) / 12);
      return {
        price: `${annualPrice.toLocaleString('nb-NO')} NOK`,
        period: 'per år',
        savings: `Spar ${monthlySavings} NOK/måned`
      };
    }
    return {
      price: `${monthlyPrice.toLocaleString('nb-NO')} NOK`,
      period: 'per måned',
      savings: null
    };
  };

  const handleReactivate = () => {
    if (!selectedPlan) return;
    onReactivate(selectedPlan, selectedBillingInterval);
  };

  const isDataExpired = new Date(cancelledSubscription.dataRetentionUntil) < new Date();
  const daysUntilDataExpiry = Math.max(0, Math.ceil(
    (new Date(cancelledSubscription.dataRetentionUntil).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
  ));

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="space-y-6">
        <div className="text-center space-y-2">
          <Heading3>Velkommen tilbake til JobbLogg!</Heading3>
          <p className="text-jobblogg-text-muted">
            Vi er glade for å se deg igjen. La oss reaktivere abonnementet ditt.
          </p>
        </div>

        {/* Cancellation Info */}
        <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-lg p-4">
          <h4 className="font-medium text-jobblogg-text mb-2">Din tidligere abonnement</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-jobblogg-text-muted">Plan:</span>
              <span className="text-jobblogg-text font-medium">{cancelledSubscription.planLevel}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-jobblogg-text-muted">Avbrutt:</span>
              <span className="text-jobblogg-text font-medium">{formatDate(cancelledSubscription.cancelledAt)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-jobblogg-text-muted">Data bevares til:</span>
              <span className={`font-medium ${isDataExpired ? 'text-jobblogg-error' : 'text-jobblogg-text'}`}>
                {formatDate(cancelledSubscription.dataRetentionUntil)}
                {!isDataExpired && ` (${daysUntilDataExpiry} dager igjen)`}
              </span>
            </div>
          </div>
        </div>

        {/* Data Retention Warning */}
        {isDataExpired ? (
          <div className="bg-jobblogg-error-light border border-jobblogg-error rounded-lg p-4">
            <h4 className="font-medium text-jobblogg-text mb-2">⚠️ Data er slettet</h4>
            <p className="text-sm text-jobblogg-text-muted">
              Dataene dine er slettet siden det har gått mer enn 90 dager siden avbryting. 
              Du starter med en ren konto når du reaktiverer.
            </p>
          </div>
        ) : daysUntilDataExpiry <= 7 ? (
          <div className="bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-4">
            <h4 className="font-medium text-jobblogg-text mb-2">⏰ Dataene dine slettes snart</h4>
            <p className="text-sm text-jobblogg-text-muted">
              Dataene dine slettes om {daysUntilDataExpiry} dager. Reaktiver nå for å beholde alle prosjektene dine.
            </p>
          </div>
        ) : (
          <div className="bg-jobblogg-success-light border border-jobblogg-success rounded-lg p-4">
            <h4 className="font-medium text-jobblogg-text mb-2">✅ Dataene dine er trygge</h4>
            <p className="text-sm text-jobblogg-text-muted">
              Alle prosjektene og dataene dine er bevart og vil være tilgjengelige umiddelbart etter reaktivering.
            </p>
          </div>
        )}

        {/* Billing Toggle */}
        <div className="text-center space-y-4">
          <h4 className="font-medium text-jobblogg-text">Velg faktureringsperiode</h4>
          <div className="inline-flex bg-jobblogg-card-bg rounded-lg p-1">
            <button
              onClick={() => setSelectedBillingInterval('month')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedBillingInterval === 'month'
                  ? 'bg-white text-jobblogg-text shadow-sm'
                  : 'text-jobblogg-text-muted hover:text-jobblogg-text'
              }`}
            >
              Månedlig
            </button>
            <button
              onClick={() => setSelectedBillingInterval('year')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors relative ${
                selectedBillingInterval === 'year'
                  ? 'bg-white text-jobblogg-text shadow-sm'
                  : 'text-jobblogg-text-muted hover:text-jobblogg-text'
              }`}
            >
              Årlig
              <span className="absolute -top-2 -right-2 bg-jobblogg-success text-white text-xs px-1.5 py-0.5 rounded-full">
                -20%
              </span>
            </button>
          </div>
        </div>

        {/* Plan Selection */}
        <div className="space-y-4">
          <h4 className="font-medium text-jobblogg-text">Velg din plan</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {availablePlans.map((plan) => {
              const pricing = formatPrice(plan.monthlyPrice, plan.annualPrice, selectedBillingInterval);
              const features = PLAN_FEATURES[plan.id] || plan.features;
              
              return (
                <div
                  key={plan.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    selectedPlan === plan.id
                      ? 'border-jobblogg-primary bg-jobblogg-primary-light'
                      : 'border-jobblogg-border hover:border-jobblogg-primary-light'
                  } ${plan.recommended ? 'ring-2 ring-jobblogg-success' : ''}`}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  {plan.recommended && (
                    <div className="text-center mb-2">
                      <span className="bg-jobblogg-success text-white px-2 py-1 rounded-full text-xs font-medium">
                        Anbefalt
                      </span>
                    </div>
                  )}
                  
                  <div className="text-center space-y-2 mb-4">
                    <h5 className="font-medium text-jobblogg-text">{plan.name}</h5>
                    <div className="text-2xl font-bold text-jobblogg-text">
                      {pricing.price}
                    </div>
                    <div className="text-jobblogg-text-muted text-sm">
                      {pricing.period}
                    </div>
                    {pricing.savings && (
                      <div className="text-jobblogg-success text-sm font-medium">
                        {pricing.savings}
                      </div>
                    )}
                  </div>

                  <ul className="space-y-1 text-sm">
                    {features.slice(0, 4).map((feature, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <svg
                          className="w-4 h-4 text-jobblogg-success mt-0.5 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className="text-jobblogg-text-muted">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <div className="mt-4 pt-4 border-t border-jobblogg-border">
                    <div className="flex items-center justify-center">
                      <input
                        type="radio"
                        name="plan-selection"
                        checked={selectedPlan === plan.id}
                        onChange={() => setSelectedPlan(plan.id)}
                        className="w-4 h-4 text-jobblogg-primary bg-white border-jobblogg-border focus:ring-jobblogg-primary focus:ring-2"
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Payment Method Info */}
        {needsPaymentMethod && (
          <div className="bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-4">
            <h4 className="font-medium text-jobblogg-text mb-2">💳 Betalingsmetode påkrevd</h4>
            <p className="text-sm text-jobblogg-text-muted">
              Du vil bli omdirigert til å legge til en betalingsmetode som del av reaktiveringsprosessen.
            </p>
          </div>
        )}

        {/* Welcome Back Offer */}
        <div className="bg-gradient-to-r from-jobblogg-primary-light to-jobblogg-primary-lighter rounded-lg p-4">
          <h4 className="font-medium text-jobblogg-text mb-2">🎉 Velkommen tilbake-tilbud</h4>
          <p className="text-sm text-jobblogg-text-muted mb-3">
            Som takk for at du kommer tilbake, får du 15% rabatt på første måned!
          </p>
          <div className="text-xs text-jobblogg-text-muted">
            * Rabatten gjelder kun første faktureringsperiode
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          <SecondaryButton
            onClick={onClose}
            disabled={isProcessing}
            className="flex-1"
          >
            Avbryt
          </SecondaryButton>
          <PrimaryButton
            onClick={handleReactivate}
            disabled={!selectedPlan || isProcessing}
            className="flex-1"
          >
            {isProcessing ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Reaktiverer...</span>
              </div>
            ) : (
              'Reaktiver abonnement'
            )}
          </PrimaryButton>
        </div>

        <div className="text-center text-xs text-jobblogg-text-muted">
          <p>Ved å reaktivere godtar du våre oppdaterte vilkår og betingelser.</p>
        </div>
      </div>
    </Modal>
  );
};
