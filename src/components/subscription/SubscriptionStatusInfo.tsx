import React, { useState, useCallback } from 'react';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import { useRoleChangeListener } from '../../hooks/useRoleChangeHandler';
import { MinimalTrialIndicator } from './MinimalTrialIndicator';

/**
 * Read-only subscription status information component for non-administrators
 * Shows subscription status without management controls
 */
export const SubscriptionStatusInfo: React.FC = () => {
  const { subscription, isInTrial, isTrialExpired, isLoading } = useSubscriptionAccess();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();
  const [_forceUpdate, setForceUpdate] = useState(0);

  // Listen for role changes and hide component when user becomes administrator
  const handleRoleChange = useCallback((event: CustomEvent) => {
    if (event.detail.becameAdmin || event.detail.lostAdmin) {
      console.log('SubscriptionStatusInfo: Role change detected, updating visibility');
      setForceUpdate(prev => prev + 1);
    }
  }, []);

  useRoleChangeListener(handleRoleChange);

  // Don't show if loading or no subscription
  if (isLoading || roleLoading || !subscription) return null;

  // Only show for non-administrators
  if (isAdministrator) return null;

  // Don't show for active paid subscriptions
  if (!isInTrial && !isTrialExpired) return null;

  // Use MinimalTrialIndicator for both active and expired trial states
  return (
    <MinimalTrialIndicator />
  );
};
