import React from 'react';
import { useRealTimeSubscriptionAccess } from '../../hooks/useRealTimeSubscriptionAccess';

/**
 * Spacer component to prevent content from being hidden behind the sticky trial banner
 * Only renders when the banner is visible
 */
export const TrialBannerSpacer: React.FC = () => {
  const { 
    subscription, 
    isInTrial, 
    shouldShowExpiredStatus 
  } = useRealTimeSubscriptionAccess();

  // Only show spacer when banner would be visible
  const shouldShowSpacer = (isInTrial || shouldShowExpiredStatus) && subscription;

  if (!shouldShowSpacer) return null;

  return (
    <div 
      className="modern-trial-banner-spacer" 
      aria-hidden="true"
      data-testid="trial-banner-spacer"
    />
  );
};
