/**
 * Trial Conversion Flow Component
 * 
 * Handles the complete trial-to-paid conversion process with:
 * - Trial status monitoring and expiration detection
 * - Plan selection for conversion
 * - Direct Stripe checkout integration
 * - Norwegian localization and messaging
 * - Multiple trigger scenarios (expiration, early upgrade, grace period)
 */

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useCheckout, createPlanCheckoutParams } from '../../hooks/useCheckout';
import { PlanSelector } from './PlanSelector';
import { CheckoutButton, CheckoutError, CheckoutLoading } from '../ui/CheckoutButton';
import { Heading2, BodyText, TextMuted, SecondaryButton, Card } from '../ui';
import { formatTimeRemaining } from '../../utils/timeFormatting';

interface TrialConversionFlowProps {
  /** Trigger scenario for the conversion flow */
  trigger: 'expiring_soon' | 'expired' | 'grace_period' | 'early_upgrade';
  /** Whether to show as modal overlay */
  isModal?: boolean;
  /** Callback when conversion is completed */
  onConversionComplete?: () => void;
  /** Callback when flow is cancelled */
  onCancel?: () => void;
  /** Custom CSS classes */
  className?: string;
}

export const TrialConversionFlow: React.FC<TrialConversionFlowProps> = ({
  trigger,
  isModal = false,
  onConversionComplete,
  onCancel,
  className = ''
}) => {
  const { user } = useUser();
  const { subscription, isInTrial, isTrialExpired, isInGracePeriod } = useSubscriptionAccess();
  const checkout = useCheckout();
  
  // State for plan selection
  const [selectedPlan, setSelectedPlan] = useState<'basic' | 'professional' | 'enterprise'>('professional');
  const [selectedBilling, setSelectedBilling] = useState<'month' | 'year'>('month');
  const [isConverting, setIsConverting] = useState(false);

  // Get current subscription details
  const currentPlan = (subscription as any)?.planLevel || 'professional';
  const currentBilling = (subscription as any)?.billingInterval || 'month';
  const trialEnd = (subscription as any)?.trialEnd || 0;

  // Initialize with current plan settings
  useEffect(() => {
    setSelectedPlan(currentPlan);
    setSelectedBilling(currentBilling);
  }, [currentPlan, currentBilling]);

  // Handle plan selection changes
  const handlePlanSelect = (planLevel: string, billingInterval: 'month' | 'year') => {
    setSelectedPlan(planLevel as 'basic' | 'professional' | 'enterprise');
    setSelectedBilling(billingInterval);
  };

  // Handle conversion to paid subscription
  const handleConvertToPaid = async () => {
    if (!user) return;

    setIsConverting(true);

    try {
      // Create checkout parameters for selected plan
      const checkoutParams = createPlanCheckoutParams(selectedPlan, selectedBilling, {
        trialDays: 0, // No additional trial for conversion
      });

      if (checkoutParams.priceId) {
        await checkout.initiateCheckout({
          priceId: checkoutParams.priceId,
          planLevel: checkoutParams.planLevel!,
          billingInterval: checkoutParams.billingInterval!,
          quantity: checkoutParams.quantity,
          successUrl: checkoutParams.successUrl,
          cancelUrl: checkoutParams.cancelUrl,
          allowPromotionCodes: checkoutParams.allowPromotionCodes,
          automaticTax: checkoutParams.automaticTax,
        });

        // If we reach here, checkout was initiated successfully
        onConversionComplete?.();
      }
    } catch (error) {
      console.error('Trial conversion failed:', error);
    } finally {
      setIsConverting(false);
    }
  };

  // Handle retry checkout
  const handleRetryCheckout = async () => {
    await checkout.retryCheckout();
  };

  // Get messaging based on trigger scenario
  const getConversionMessaging = () => {
    const timeRemaining = formatTimeRemaining(trialEnd);
    
    switch (trigger) {
      case 'expiring_soon':
        return {
          title: 'Prøveperioden utløper snart',
          subtitle: `${timeRemaining.text} igjen av prøveperioden`,
          message: 'Fortsett med full tilgang til alle funksjoner ved å velge en plan som passer for din bedrift.',
          urgency: 'medium',
          ctaText: 'Fortsett med betalt plan',
        };
      
      case 'expired':
        return {
          title: 'Prøveperioden er utløpt',
          subtitle: 'Velg en plan for å fortsette å bruke JobbLogg',
          message: 'Din prøveperiode er utløpt. Velg en plan for å få tilbake full tilgang til alle funksjoner.',
          urgency: 'high',
          ctaText: 'Aktiver betalt plan',
        };
      
      case 'grace_period':
        const gracePeriodEnd = trialEnd + (3 * 24 * 60 * 60 * 1000); // 3 days grace
        const graceTimeLeft = formatTimeRemaining(gracePeriodEnd);
        return {
          title: 'Siste sjanse - Kontoen stenges snart',
          subtitle: `${graceTimeLeft.text} til kontoen stenges`,
          message: 'Du er i en 3-dagers nådeperiode. Aktiver en betalt plan nå for å unngå at kontoen stenges.',
          urgency: 'critical',
          ctaText: 'Aktiver plan nå',
        };
      
      case 'early_upgrade':
        return {
          title: 'Oppgrader til betalt plan',
          subtitle: `${timeRemaining.text} igjen av prøveperioden`,
          message: 'Oppgrader til en betalt plan når som helst for å sikre kontinuerlig tilgang til alle funksjoner.',
          urgency: 'low',
          ctaText: 'Oppgrader nå',
        };
      
      default:
        return {
          title: 'Velg din plan',
          subtitle: 'Fortsett med JobbLogg',
          message: 'Velg planen som passer best for din bedrift.',
          urgency: 'medium',
          ctaText: 'Velg plan',
        };
    }
  };

  const messaging = getConversionMessaging();

  // Get urgency styling
  const getUrgencyStyles = () => {
    switch (messaging.urgency) {
      case 'critical':
        return {
          headerBg: 'bg-gradient-to-r from-red-600 to-red-700',
          headerText: 'text-white',
          borderColor: 'border-red-200',
          ctaColor: 'bg-red-600 hover:bg-red-700',
        };
      case 'high':
        return {
          headerBg: 'bg-gradient-to-r from-amber-500 to-orange-600',
          headerText: 'text-white',
          borderColor: 'border-amber-200',
          ctaColor: 'bg-amber-600 hover:bg-amber-700',
        };
      case 'medium':
        return {
          headerBg: 'bg-gradient-to-r from-jobblogg-primary to-jobblogg-primary-dark',
          headerText: 'text-white',
          borderColor: 'border-jobblogg-border',
          ctaColor: 'bg-jobblogg-primary hover:bg-jobblogg-primary-dark',
        };
      default:
        return {
          headerBg: 'bg-gradient-to-r from-jobblogg-accent to-jobblogg-primary',
          headerText: 'text-white',
          borderColor: 'border-jobblogg-border',
          ctaColor: 'bg-jobblogg-primary hover:bg-jobblogg-primary-dark',
        };
    }
  };

  const styles = getUrgencyStyles();

  // Don't render if no subscription or user
  if (!subscription || !user) return null;

  const content = (
    <>
      {/* Checkout Loading Overlay */}
      <CheckoutLoading
        isVisible={checkout.state.isLoading || checkout.state.isRetrying}
        message="Starter betaling..."
        isRetrying={checkout.state.isRetrying}
      />

      <div className={`bg-white rounded-xl border ${styles.borderColor} shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className={`${styles.headerBg} px-6 py-4`}>
        <div className={`${styles.headerText}`}>
          <Heading2 className="text-inherit mb-1">{messaging.title}</Heading2>
          <TextMuted className="text-white/80">{messaging.subtitle}</TextMuted>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-6">
        {/* Message */}
        <BodyText className="text-jobblogg-text-medium">
          {messaging.message}
        </BodyText>

        {/* Plan Selection */}
        <div>
          <PlanSelector
            currentPlan={selectedPlan}
            currentBilling={selectedBilling}
            onPlanSelect={handlePlanSelect}
            title="Velg din plan"
            subtitle="Alle planer inkluderer full tilgang til JobbLogg"
            isTrialMode={false}
          />
        </div>

        {/* Error Display */}
        <CheckoutError
          error={checkout.state.error}
          norwegianError={checkout.state.norwegianError}
          canRetry={checkout.state.canRetry}
          onRetry={handleRetryCheckout}
          onClear={checkout.clearError}
          retryCount={checkout.state.retryCount}
          maxRetries={3}
        />

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          <CheckoutButton
            onClick={handleConvertToPaid}
            isLoading={checkout.state.isLoading}
            isRetrying={checkout.state.isRetrying}
            disabled={isConverting}
            loadingText="Starter betaling..."
            className={`flex-1 ${styles.ctaColor} text-white`}
            fullWidth
          >
            {messaging.ctaText}
          </CheckoutButton>

          {onCancel && trigger !== 'grace_period' && (
            <SecondaryButton
              onClick={onCancel}
              disabled={isConverting || checkout.state.isLoading || checkout.state.isRetrying}
              className="flex-1 sm:flex-none"
            >
              Avbryt
            </SecondaryButton>
          )}
        </div>

        {/* Additional Info */}
        <div className="text-center">
          <TextMuted className="text-xs">
            Sikker betaling via Stripe • Avbryt når som helst • Norsk kundeservice
          </TextMuted>
        </div>
      </div>
      </div>
    </>
  );

  // Render as modal or inline
  if (isModal) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
        <div className="max-w-4xl w-full max-h-[90vh] overflow-y-auto">
          {content}
        </div>
      </div>
    );
  }

  return content;
};
