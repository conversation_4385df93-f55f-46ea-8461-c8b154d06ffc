import React from 'react';
import { EnhancedTrialExpiredBanner } from './EnhancedTrialExpiredBanner';
import { MinimalTrialIndicator } from './MinimalTrialIndicator';
import { useRealTimeSubscriptionAccess } from '../../hooks/useRealTimeSubscriptionAccess';

/**
 * Demo component to showcase the new trial expiration design
 * Shows both active trial countdown and expired trial banner
 */
export const TrialExpirationDemo: React.FC = () => {
  const {
    isInTrial,
    isTrialExpired,
    isRealTimeExpired,
    shouldShowExpiredStatus,
    trialEnd,
    currentTime
  } = useRealTimeSubscriptionAccess();

  const timeUntilExpiration = trialEnd - currentTime;
  const minutesLeft = Math.max(0, Math.floor(timeUntilExpiration / (60 * 1000)));

  return (
    <div className="space-y-6 p-6 bg-jobblogg-neutral-soft rounded-xl">
      <div className="text-center">
        <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
          🎨 Nytt Trial Expiration Design
        </h2>
        <p className="text-jobblogg-text-medium">
          Elegant, motiverende design uten duplikater
        </p>
      </div>

      {/* Current Status */}
      <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
        <h3 className="font-medium text-jobblogg-text-strong mb-3">Nåværende status:</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-jobblogg-text-medium">Aktiv prøveperiode:</span>
            <span className={`ml-2 font-medium ${isInTrial ? 'text-jobblogg-success' : 'text-jobblogg-error'}`}>
              {isInTrial ? '✅ Ja' : '❌ Nei'}
            </span>
          </div>
          <div>
            <span className="text-jobblogg-text-medium">Utløpt (real-time):</span>
            <span className={`ml-2 font-medium ${shouldShowExpiredStatus ? 'text-jobblogg-error' : 'text-jobblogg-success'}`}>
              {shouldShowExpiredStatus ? '⚠️ Ja' : '✅ Nei'}
            </span>
          </div>
        </div>
        
        {isInTrial && !shouldShowExpiredStatus && (
          <div className="mt-3 p-2 bg-jobblogg-primary-soft rounded text-sm text-jobblogg-primary">
            ⏰ {minutesLeft} minutter igjen til utløp
          </div>
        )}
      </div>

      {/* Design Showcase */}
      <div className="space-y-4">
        <h3 className="font-medium text-jobblogg-text-strong">Design showcase:</h3>
        
        {/* Active Trial Indicator */}
        {isInTrial && !shouldShowExpiredStatus && (
          <div>
            <h4 className="text-sm font-medium text-jobblogg-text-medium mb-2">
              📊 Aktiv prøveperiode (MinimalTrialIndicator):
            </h4>
            <MinimalTrialIndicator />
          </div>
        )}

        {/* Expired Trial Banner */}
        {shouldShowExpiredStatus && (
          <div>
            <h4 className="text-sm font-medium text-jobblogg-text-medium mb-2">
              🚨 Utløpt prøveperiode (EnhancedTrialExpiredBanner):
            </h4>
            <EnhancedTrialExpiredBanner showDismiss={true} />
          </div>
        )}

        {/* Design Benefits */}
        <div className="bg-jobblogg-success-soft rounded-lg p-4 border border-jobblogg-success">
          <h4 className="font-medium text-jobblogg-success mb-2">✨ Forbedringer:</h4>
          <ul className="text-sm text-jobblogg-text-medium space-y-1">
            <li>✅ <strong>Ingen duplikater:</strong> Kun én melding om utløpt prøveperiode</li>
            <li>✅ <strong>Penere design:</strong> Gradient bakgrunn og moderne styling</li>
            <li>✅ <strong>Motiverende innhold:</strong> Viser fordeler og oppfordrer til oppgradering</li>
            <li>✅ <strong>Tydelig CTA:</strong> "Oppgrader til Professional" knapp</li>
            <li>✅ <strong>Rollespesifikk:</strong> Forskjellig visning for administratorer vs vanlige brukere</li>
            <li>✅ <strong>Real-time oppdateringer:</strong> Oppdateres automatisk når prøveperioden utløper</li>
          </ul>
        </div>

        {/* Technical Details */}
        <div className="bg-jobblogg-primary-soft rounded-lg p-4 border border-jobblogg-primary">
          <h4 className="font-medium text-jobblogg-primary mb-2">🔧 Tekniske detaljer:</h4>
          <ul className="text-sm text-jobblogg-text-medium space-y-1">
            <li>• <strong>EnhancedTrialExpiredBanner:</strong> Vises kun når prøveperioden er utløpt</li>
            <li>• <strong>MinimalTrialIndicator:</strong> Vises kun under aktiv prøveperiode</li>
            <li>• <strong>AuthenticatedLayout:</strong> Håndterer visning av begge komponenter</li>
            <li>• <strong>Dashboard:</strong> Fjernet duplikat MinimalTrialIndicator</li>
            <li>• <strong>Real-time sync:</strong> Begge komponenter bruker useRealTimeSubscriptionAccess</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
