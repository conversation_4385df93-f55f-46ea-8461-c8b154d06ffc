import React, { useState } from 'react';
import { PrimaryButton, SecondaryButton } from '../ui/Button';
import { Modal } from '../ui/Dialog';
import { BodyText } from '../ui/Typography';

interface PlanTier {
  id: 'basic' | 'professional' | 'enterprise';
  name: string;
  description: string;
  employeeRange: string;
  monthlyPrice: number;
  yearlyPrice: number;
  userLimit: number;
  features: string[];
  isRecommended?: boolean;
}

interface TrialPlanSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onProceedToCheckout: (planLevel: string, billingInterval: 'month' | 'year') => void;
  currentPlan: 'basic' | 'professional' | 'enterprise';
  currentBilling: 'month' | 'year';
  isLoading?: boolean;
}

const pricingTiers: PlanTier[] = [
  {
    id: 'basic',
    name: 'Liten bedrift',
    description: 'Perfekt for enkeltmannsforetak og små team',
    employeeRange: '1–9 ansatte',
    monthlyPrice: 299,
    yearlyPrice: 2870, // 299 * 12 * 0.8 = 2870 (20% rabatt)
    userLimit: 9,
    features: [
      'Ubegrenset prosjekter',
      'Prosjektlogg med bilder',
      'Chat med kunder og team',
      'Prosjektdeling med kunder',
      'Prosjektdeling med underleverandører',
      'Mobil app (iOS/Android)',
    ],
    isRecommended: false
  },
  {
    id: 'professional',
    name: 'Mellomstor bedrift',
    description: 'For voksende entreprenørfirmaer',
    employeeRange: '10–49 ansatte',
    monthlyPrice: 999,
    yearlyPrice: 9590, // 999 * 12 * 0.8 = 9590 (20% rabatt)
    userLimit: 49,
    features: [
      'Alt i Liten bedrift',
      'Avanserte rapporter',
      'Prioritert support',
      'API-tilgang',
      'Tilpassede integrasjoner',
    ],
    isRecommended: true
  },
  {
    id: 'enterprise',
    name: 'Stor bedrift',
    description: 'For etablerte byggefirmaer',
    employeeRange: '50–249 ansatte',
    monthlyPrice: 2999,
    yearlyPrice: 28790, // 2999 * 12 * 0.8 = 28790 (20% rabatt)
    userLimit: 249,
    features: [
      'Alt i Mellomstor bedrift',
      'Dedikert kundesuksess',
      'Avansert sikkerhet',
      'Ubegrenset lagring',
      'Prioritert telefonstøtte',
    ]
  }
];

export const TrialPlanSelectionModal: React.FC<TrialPlanSelectionModalProps> = ({
  isOpen,
  onClose,
  onProceedToCheckout,
  currentPlan,
  currentBilling,
  isLoading = false
}) => {
  const [selectedPlan, setSelectedPlan] = useState<'basic' | 'professional' | 'enterprise'>(currentPlan);
  const [selectedBilling, setSelectedBilling] = useState<'month' | 'year'>(currentBilling);

  const formatPrice = (price: number) => {
    return price.toLocaleString('nb-NO');
  };

  const calculateAnnualSavings = (monthlyPrice: number, yearlyPrice: number) => {
    const annualMonthlyTotal = monthlyPrice * 12;
    const savings = annualMonthlyTotal - yearlyPrice;
    const savingsPercentage = Math.round((savings / annualMonthlyTotal) * 100);
    return { savings, savingsPercentage };
  };

  const handleProceed = () => {
    onProceedToCheckout(selectedPlan, selectedBilling);
  };

  const selectedTier = pricingTiers.find(tier => tier.id === selectedPlan);
  const currentPrice = selectedBilling === 'year' ? selectedTier?.yearlyPrice : selectedTier?.monthlyPrice;
  const annualSavings = selectedTier ? calculateAnnualSavings(selectedTier.monthlyPrice, selectedTier.yearlyPrice) : null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Velg din plan"
      size="lg"
    >
      <div className="space-y-6">
        {/* Introduction */}
        <div className="text-center">
          <BodyText className="text-jobblogg-text-medium">
            Bekreft eller endre planvalget ditt før du går til betaling. Du kan alltid endre planen senere.
          </BodyText>
        </div>

        {/* Billing Toggle */}
        <div className="flex justify-center">
          <div className="bg-jobblogg-neutral rounded-lg p-1 flex">
            <button
              onClick={() => setSelectedBilling('month')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                selectedBilling === 'month'
                  ? 'bg-white text-jobblogg-text-strong shadow-sm'
                  : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
              }`}
            >
              Månedlig
            </button>
            <button
              onClick={() => setSelectedBilling('year')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                selectedBilling === 'year'
                  ? 'bg-white text-jobblogg-text-strong shadow-sm'
                  : 'text-jobblogg-text-medium hover:text-jobblogg-text-strong'
              }`}
            >
              Årlig
              {annualSavings && (
                <span className="ml-1 text-xs text-jobblogg-success font-semibold">
                  (spar {annualSavings.savingsPercentage}%)
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Plan Cards */}
        <div className="grid gap-4">
          {pricingTiers.map((tier) => {
            const isSelected = tier.id === selectedPlan;
            const price = selectedBilling === 'year' ? tier.yearlyPrice : tier.monthlyPrice;
            const priceLabel = selectedBilling === 'year' ? 'per år' : 'per måned';
            const tierAnnualSavings = calculateAnnualSavings(tier.monthlyPrice, tier.yearlyPrice);

            return (
              <div
                key={tier.id}
                className={`relative border-2 rounded-xl p-4 cursor-pointer transition-all duration-200 ${
                  isSelected
                    ? 'border-jobblogg-primary bg-jobblogg-primary-soft'
                    : 'border-jobblogg-border hover:border-jobblogg-primary/50'
                }`}
                onClick={() => setSelectedPlan(tier.id)}
              >
                {tier.isRecommended && (
                  <div className="absolute -top-3 left-4">
                    <span className="bg-jobblogg-success text-white px-3 py-1 rounded-full text-xs font-medium">
                      Anbefalt
                    </span>
                  </div>
                )}

                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-jobblogg-text-strong">{tier.name}</h3>
                      <span className="text-sm text-jobblogg-text-medium">({tier.employeeRange})</span>
                    </div>
                    <p className="text-sm text-jobblogg-text-medium mb-2">{tier.description}</p>
                    <p className="text-xs text-jobblogg-text-muted">Inkluderer {tier.userLimit} brukere</p>
                  </div>

                  <div className="text-right">
                    <div className="text-2xl font-bold text-jobblogg-text-strong">
                      {formatPrice(price)} NOK
                    </div>
                    <div className="text-sm text-jobblogg-text-medium">{priceLabel} ekskl. MVA</div>
                    {selectedBilling === 'year' && (
                      <div className="text-xs text-jobblogg-success font-medium">
                        Spar {formatPrice(tierAnnualSavings.savings)} NOK/år
                      </div>
                    )}
                  </div>
                </div>

                {/* Selection indicator */}
                <div className="mt-3 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                      isSelected 
                        ? 'border-jobblogg-primary bg-jobblogg-primary' 
                        : 'border-jobblogg-border'
                    }`}>
                      {isSelected && (
                        <div className="w-2 h-2 bg-white rounded-full"></div>
                      )}
                    </div>
                    <span className="text-sm font-medium text-jobblogg-text-strong">
                      {isSelected ? 'Valgt' : 'Velg denne planen'}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Selected Plan Summary */}
        {selectedTier && (
          <div className="bg-jobblogg-card-bg rounded-lg p-4">
            <h4 className="font-medium text-jobblogg-text-strong mb-2">Ditt valg:</h4>
            <div className="flex justify-between items-center">
              <div>
                <span className="font-medium">{selectedTier.name}</span>
                <span className="text-jobblogg-text-medium ml-2">
                  ({selectedBilling === 'year' ? 'årlig' : 'månedlig'} fakturering)
                </span>
              </div>
              <div className="text-right">
                <div className="font-bold text-jobblogg-text-strong">
                  {formatPrice(currentPrice || 0)} NOK {selectedBilling === 'year' ? '/år' : '/måned'}
                </div>
                <div className="text-sm text-jobblogg-text-muted">ekskl. MVA</div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          <SecondaryButton
            onClick={onClose}
            disabled={isLoading}
            className="flex-1"
          >
            Avbryt
          </SecondaryButton>
          <PrimaryButton
            onClick={handleProceed}
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Starter betaling...</span>
              </div>
            ) : (
              'Fortsett til betaling'
            )}
          </PrimaryButton>
        </div>
      </div>
    </Modal>
  );
};
