import React from 'react';
import { useAction } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { useUserRole } from '../../hooks/useUserRole';
import { useCheckout, createPlanCheckoutParams } from '../../hooks/useCheckout';
import { CheckoutButton, CheckoutError } from '../ui/CheckoutButton';
import { PrimaryButton, SecondaryButton } from '../ui/Button';

interface UpgradePromptProps {
  feature: 'create_project' | 'full_access' | 'view_projects' | 'read_only' | 'team_management' | 'project_sharing' | 'file_upload';
  needsTrialSetup?: boolean;
}

export const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  feature,
  needsTrialSetup = false
}) => {
  const { user } = useUser();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();
  const createTrialSubscription = useAction(api.subscriptions.createTrialSubscription);
  const createPortalSession = useAction(api.subscriptions.createPortalSession);
  const checkout = useCheckout();
  const handleStartTrial = async () => {
    if (!user) return;

    try {
      await createTrialSubscription({
        userId: user.id,
        email: user.emailAddresses[0]?.emailAddress || '',
        name: user.fullName || '',
      });

      // Refresh the page to update subscription status
      window.location.reload();
    } catch (error) {
      console.error('Failed to start trial:', error);
    }
  };

  const handleUpgrade = async () => {
    if (!user) return;

    try {
      // For new users or trial users, use direct checkout for better UX
      // For existing subscribers, use Customer Portal
      const { url } = await createPortalSession({
        userId: user.id,
        returnUrl: window.location.href
      });
      window.location.href = url;
    } catch (error) {
      console.error('Failed to create portal session:', error);

      // If portal fails (e.g., no subscription), try direct checkout for Professional plan
      if (error instanceof Error && error.message.includes('No subscription found')) {
        console.log('No subscription found, using direct checkout');

        const checkoutParams = createPlanCheckoutParams('professional', 'month', {
          trialDays: 7, // 7-day trial for new users
        });

        if (checkoutParams.priceId) {
          await checkout.initiateCheckout({
            priceId: checkoutParams.priceId,
            planLevel: checkoutParams.planLevel!,
            billingInterval: checkoutParams.billingInterval!,
            quantity: checkoutParams.quantity,
            trialDays: checkoutParams.trialDays,
            successUrl: checkoutParams.successUrl,
            cancelUrl: checkoutParams.cancelUrl,
            allowPromotionCodes: checkoutParams.allowPromotionCodes,
            automaticTax: checkoutParams.automaticTax,
          });
        }
      }
    }
  };

  const getFeatureMessage = () => {
    switch (feature) {
      case 'create_project':
        return {
          title: 'Opprett nye prosjekter',
          description: 'For å opprette nye prosjekter trenger du et aktivt abonnement eller prøveperiode.',
        };
      case 'full_access':
        return {
          title: 'Full tilgang til JobbLogg',
          description: 'For å få full tilgang til alle funksjoner trenger du et aktivt abonnement.',
        };
      case 'view_projects':
        return {
          title: 'Se prosjekter',
          description: 'For å se prosjekter trenger du et aktivt abonnement eller prøveperiode.',
        };
      default:
        return {
          title: 'Oppgrader abonnement',
          description: 'For å bruke denne funksjonen trenger du et aktivt abonnement.',
        };
    }
  };

  const { title, description } = getFeatureMessage();

  // Show loading state while checking role
  if (roleLoading) {
    return (
      <div className="text-center p-8 bg-jobblogg-surface rounded-xl border border-jobblogg-border">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
        <p className="text-jobblogg-text-muted">Sjekker tilganger...</p>
      </div>
    );
  }

  // Non-administrators see informational message only
  if (!isAdministrator) {
    return (
      <div className="text-center p-8 bg-jobblogg-surface rounded-xl border border-jobblogg-border">
        <div className="w-16 h-16 bg-jobblogg-text-muted/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>

        <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
          Tilgang kreves
        </h2>

        <p className="text-jobblogg-text-medium mb-6">
          Bedriften har ikke aktivt abonnement. Kontakt administrator for å oppgradere eller starte prøveperiode.
        </p>

        <SecondaryButton onClick={() => window.history.back()}>
          Gå tilbake
        </SecondaryButton>
      </div>
    );
  }

  if (needsTrialSetup) {
    return (
      <div className="text-center p-8 bg-jobblogg-surface rounded-xl border border-jobblogg-border">
        <div className="w-16 h-16 bg-jobblogg-primary-soft rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-jobblogg-primary" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        </div>

        <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
          Start din gratis prøveperiode
        </h2>

        <p className="text-jobblogg-text-medium mb-6">
          Få 7 dager gratis tilgang til alle funksjoner i JobbLogg. Ingen kredittkort påkrevd.
        </p>

        <div className="space-y-3">
          <PrimaryButton onClick={handleStartTrial} className="w-full">
            Start 7-dagers gratis prøve
          </PrimaryButton>

          <p className="text-xs text-jobblogg-text-muted">
            Ingen forpliktelser • Avbryt når som helst
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="text-center p-8 bg-jobblogg-surface rounded-xl border border-jobblogg-border">
      <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-4">
        <svg className="w-8 h-8 text-jobblogg-warning" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
      </div>

      <h2 className="text-xl font-semibold text-jobblogg-text-strong mb-2">
        {title}
      </h2>

      <p className="text-jobblogg-text-medium mb-6">
        {description}
      </p>

      {/* Error Display */}
      <CheckoutError
        error={checkout.state.error}
        norwegianError={checkout.state.norwegianError}
        canRetry={checkout.state.canRetry}
        onRetry={checkout.retryCheckout}
        onClear={checkout.clearError}
        retryCount={checkout.state.retryCount}
        maxRetries={3}
        className="mb-4"
      />

      <div className="flex gap-3 justify-center">
        <SecondaryButton onClick={() => window.history.back()}>
          Gå tilbake
        </SecondaryButton>
        <CheckoutButton
          onClick={handleUpgrade}
          isLoading={checkout.state.isLoading}
          isRetrying={checkout.state.isRetrying}
          loadingText="Starter oppgradering..."
        >
          Oppgrader abonnement
        </CheckoutButton>
      </div>
    </div>
  );
};
