import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  Modal,
  Heading2,
  BodyText,
  TextMuted,
  DangerButton,
  SecondaryButton,
  TextArea
} from '../ui';

interface TeamMember {
  _id: string;
  clerkUserId: string;
  role: string;
  invitationStatus?: string;
  invitedAt?: number;
  acceptedAt?: number;
  createdAt: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  lastLoginAt?: number;
  lastActivityAt?: number;
  isBlocked?: boolean;
  blockedAt?: number;
  blockedBy?: string;
  blockedReason?: string;
}

interface BlockTeamMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  member: TeamMember;
  onSuccess: () => void;
}

export const BlockTeamMemberModal: React.FC<BlockTeamMemberModalProps> = ({
  isOpen,
  onClose,
  member,
  onSuccess,
}) => {
  const { user } = useUser();
  const [isBlocking, setIsBlocking] = useState(false);
  const [reason, setReason] = useState('');const blockTeamMember = useMutation(api.teamManagement.blockTeamMember);
  // Get display name
  const getDisplayName = () => {
    if (member.firstName && member.lastName) {
      return `${member.firstName} ${member.lastName}`;
    }
    if (member.firstName) {
      return member.firstName;
    }
    if (member.email) {
      return member.email.split('@')[0];
    }
    return `Teammedlem ${member.clerkUserId.slice(-4)}`;
  };

  const displayName = getDisplayName();
  const roleLabel = member.role === 'administrator' ? 'Administrator' : 'Utførende';

  const handleBlock = async () => {
    if (!user?.id) return;

    setIsBlocking(true);
    try {
      await blockTeamMember({
        teamMemberId: member._id as any,
        blockedBy: user.id,
        reason: reason.trim() || undefined,
      });

      onSuccess();
      onClose();
      setReason(''); // Reset form
    } catch (error) {
      console.error('Failed to block team member:', error);
    } finally {
      setIsBlocking(false);
    }
  };

  const handleClose = () => {
    setReason(''); // Reset form on close
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md" title="Blokker teammedlem">
      <div className="space-y-6">
        {/* Header with warning icon */}
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 rounded-full bg-jobblogg-warning/10 flex items-center justify-center flex-shrink-0">
            <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m4-6V9a4 4 0 10-8 0v2m0 0v2a2 2 0 002 2h4a2 2 0 002-2v-2" />
            </svg>
          </div>

          <div className="flex-1">
            <Heading2 className="text-jobblogg-warning">
              Sperr teammedlem
            </Heading2>
            <TextMuted>
              Dette vil midlertidig blokkere {displayName}s tilgang til teamet
            </TextMuted>
          </div>
        </div>

        {/* Member info */}
        <div className="bg-jobblogg-neutral/30 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-8 h-8 rounded-full bg-jobblogg-primary/10 flex items-center justify-center">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <BodyText className="font-medium">{displayName}</BodyText>
              <TextMuted className="text-sm">{roleLabel}</TextMuted>
            </div>
          </div>

          {member.email && (
            <TextMuted className="text-sm">
              {member.email}
            </TextMuted>
          )}
        </div>

        {/* Warning message */}
        <div className="bg-jobblogg-warning/10 border border-jobblogg-warning/20 rounded-lg p-4">
          <div className="flex gap-3">
            <svg className="w-5 h-5 text-jobblogg-warning flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div className="space-y-2">
              <BodyText className="font-medium text-jobblogg-warning">
                Hva skjer når du sperrer teammedlemmet:
              </BodyText>
              <TextMuted className="text-sm">
                Dette vil midlertidig blokkere brukerens tilgang til teamet. Brukeren vil ikke kunne logge inn eller få tilgang til prosjekter, men alle data bevares og kan gjenåpnes senere.
              </TextMuted>
            </div>
          </div>
        </div>

        {/* Consequences breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* What will be blocked */}
          <div className="bg-jobblogg-error/5 border border-jobblogg-error/10 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <svg className="w-4 h-4 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
              </svg>
              <BodyText className="font-medium text-jobblogg-error text-sm">
                Vil sperres midlertidig
              </BodyText>
            </div>
            <ul className="space-y-1 text-sm text-jobblogg-text-medium">
              <li>• Innloggingstilgang</li>
              <li>• Tilgang til tildelte prosjekter</li>
              <li>• Mulighet til å sende meldinger</li>
              <li>• Alle team-funksjoner</li>
            </ul>
          </div>

          {/* What will be preserved */}
          <div className="bg-jobblogg-success/5 border border-jobblogg-success/10 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <BodyText className="font-medium text-jobblogg-success text-sm">
                Vil bevares
              </BodyText>
            </div>
            <ul className="space-y-1 text-sm text-jobblogg-text-medium">
              <li>• Alle historiske data</li>
              <li>• Prosjekttildelinger (gjenåpnes)</li>
              <li>• Meldinger og loggføringer</li>
              <li>• Mulighet for gjenåpning</li>
            </ul>
          </div>
        </div>

        {/* Optional reason */}
        <div className="space-y-2">
          <BodyText className="font-medium text-sm">
            Valgfri grunn for sperring (kun for administratorer):
          </BodyText>
          <TextArea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="F.eks. 'Brudd på retningslinjer', 'Midlertidig suspensjon', etc."
            rows={3}
            maxLength={500}
          />
          <div className="flex items-start justify-between">
            <div className="bg-jobblogg-primary/5 border border-jobblogg-primary/10 rounded-lg p-3 flex-1 mr-2">
              <div className="flex items-start gap-2">
                <svg className="w-4 h-4 text-jobblogg-primary flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <TextMuted className="text-xs">
                  <strong>Viktig:</strong> Denne grunnen er kun synlig for administratorer og vises ikke til den sperrede brukeren.
                </TextMuted>
              </div>
            </div>
            <TextMuted className="text-xs text-right">
              {reason.length}/500
            </TextMuted>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
          <SecondaryButton onClick={handleClose} disabled={isBlocking}>
            Avbryt
          </SecondaryButton>

          <DangerButton
            onClick={handleBlock}
            loading={isBlocking}
          >
            Sperr bruker
          </DangerButton>
        </div>
      </div>
    </Modal>
  );
};
