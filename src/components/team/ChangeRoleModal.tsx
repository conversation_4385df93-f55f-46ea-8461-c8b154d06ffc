import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  Modal,
  Heading2,
  BodyText,
  TextMuted,
  PrimaryButton,
  SecondaryButton,
  SelectInput,
  FormError
} from '../ui';

interface TeamMember {
  _id: string;
  clerkUserId: string;
  role: string;
  firstName?: string;
  lastName?: string;
  email?: string;
}

interface ChangeRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  member: TeamMember;
  onSuccess: () => void;
}

export const ChangeRoleModal: React.FC<ChangeRoleModalProps> = ({
  isOpen,
  onClose,
  member,
  onSuccess,
}) => {
  const { user } = useUser();
  const [newRole, setNewRole] = useState<'administrator' | 'prosjektleder' | 'utfoerende'>(
    member.role as 'administrator' | 'prosjektleder' | 'utfoerende' || 'utfoerende'
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');const changeRole = useMutation(api.teamManagement.changeTeamMemberRole);
  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'administrator': return 'Administrator';
      case 'prosjektleder': return 'Prosjektleder';
      case 'utfoerende': return 'Utførende';
      default: return role;
    }
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case 'administrator':
        return 'Full tilgang til teamadministrasjon, bedriftsprofil og abonnement.';
      case 'prosjektleder':
        return 'Kan administrere prosjekter og invitere utførende til teamet.';
      case 'utfoerende':
        return 'Kan opprette og administrere prosjekter, men ikke invitere andre brukere.';
      default:
        return '';
    }
  };

  const getDisplayName = () => {
    if (member.firstName && member.lastName) {
      return `${member.firstName} ${member.lastName}`;
    }
    if (member.firstName) {
      return member.firstName;
    }
    if (member.email) {
      return member.email.split('@')[0];
    }
    return `Teammedlem ${member.clerkUserId.slice(-4)}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      setError('Du må være logget inn for å endre roller');
      return;
    }

    if (newRole === member.role) {
      setError('Velg en annen rolle enn den nåværende');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      await changeRole({
        teamMemberId: member._id as any,
        newRole,
        changedBy: user.id,
      });

      onSuccess();
      onClose();
    } catch (error: any) {
      setError(error.message || 'Kunne ikke endre rolle');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setNewRole(member.role as 'administrator' | 'prosjektleder' | 'utfoerende' || 'utfoerende');
      setError('');
      onClose();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md" title="Endre rolle">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="w-16 h-16 rounded-full bg-jobblogg-accent/10 flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <Heading2 className="mb-2">Endre rolle</Heading2>
          <TextMuted>
            Endre rollen til {getDisplayName()}
          </TextMuted>
        </div>

        {/* Current Role Info */}
        <div className="bg-jobblogg-neutral/30 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <BodyText className="font-medium">Nåværende rolle</BodyText>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              member.role === 'administrator'
                ? 'bg-jobblogg-primary/10 text-jobblogg-primary border border-jobblogg-primary/20'
                : member.role === 'prosjektleder'
                ? 'bg-jobblogg-success/10 text-jobblogg-success border border-jobblogg-success/20'
                : 'bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20'
            }`}>
              {getRoleLabel(member.role)}
            </span>
          </div>
          <TextMuted className="text-sm">
            {getRoleDescription(member.role)}
          </TextMuted>
        </div>

        {/* New Role Selection */}
        <div className="space-y-2">
          <label htmlFor="newRole" className="block text-sm font-medium text-jobblogg-text-strong">
            Ny rolle <span className="text-jobblogg-error">*</span>
          </label>
          <SelectInput
            id="newRole"
            value={newRole}
            onChange={(e) => setNewRole(e.target.value as 'administrator' | 'prosjektleder' | 'utfoerende')}
            disabled={isSubmitting}
            required
            options={[
              { value: 'utfoerende', label: 'Utførende' },
              { value: 'prosjektleder', label: 'Prosjektleder' },
              { value: 'administrator', label: 'Administrator' }
            ]}
          />
          <TextMuted className="text-sm">
            {getRoleDescription(newRole)}
          </TextMuted>
        </div>

        {/* Error Message */}
        {error && <FormError message={error} />}

        {/* Warning for Administrator Role */}
        {newRole === 'administrator' && member.role !== 'administrator' && (
          <div className="bg-jobblogg-warning/10 border border-jobblogg-warning/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-jobblogg-warning flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div>
                <BodyText className="font-medium text-jobblogg-warning mb-1">
                  Administrator-rolle
                </BodyText>
                <TextMuted className="text-sm">
                  Administratorer har full tilgang til teamadministrasjon, bedriftsprofil og abonnement.
                  Vær sikker på at du stoler på denne personen med disse tilgangene.
                </TextMuted>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
          <SecondaryButton
            type="button"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Avbryt
          </SecondaryButton>
          <PrimaryButton
            type="submit"
            disabled={isSubmitting || newRole === member.role}
            loading={isSubmitting}
          >
            {isSubmitting ? 'Endrer rolle...' : 'Endre rolle'}
          </PrimaryButton>
        </div>
      </form>
    </Modal>
  );
};
