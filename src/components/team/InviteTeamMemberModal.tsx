import React, { useState } from 'react';
import { useQuery, useAction } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { useSeatManagement } from '../../hooks/useSubscriptionAccess';
import { useUserRole } from '../../hooks/useUserRole';
import {
  Modal,
  PrimaryButton,
  SecondaryButton,
  TextInput,
  SelectInput,
  FormError,
  Heading2,
  BodyText,
  PhoneInput
} from '../ui';
import { validateNorwegianPhone } from '../ui/Form/PhoneInput';

interface InviteTeamMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export const InviteTeamMemberModal: React.FC<InviteTeamMemberModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
}) => {
  const { user } = useUser();
  const { isAdministrator, isProsjektleder } = useUserRole();
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');
  const [phoneError, setPhoneError] = useState('');
  const [role, setRole] = useState<'administrator' | 'prosjektleder' | 'utfoerende'>('utfoerende');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [invitationResult, setInvitationResult] = useState<any>(null);

  // Get accurate team member count
  const teamMemberCount = useQuery(
    api.seatManagement.getTeamMemberCount,
    user?.id ? { userId: user.id } : "skip"
  );

  // Seat management (fallback)
  const {
    canInvite: fallbackCanInvite,
    currentSeats: fallbackCurrentSeats,
    maxSeats: fallbackMaxSeats,
    warning,
    warningMessage,
    blockedReason,
    blockedMessage,
    isLoading: seatLoading
  } = useSeatManagement();

  // Use accurate count if available, fallback to seat management hook
  const currentSeats = (teamMemberCount as any)?.actualTeamMemberCount || fallbackCurrentSeats || 0;
  const maxSeats = (teamMemberCount as any)?.maxSeats || fallbackMaxSeats || 0;
  const remainingSeats = Math.max(0, maxSeats - currentSeats);
  const isAtLimit = currentSeats >= maxSeats;
  const isCritical = currentSeats >= Math.floor(maxSeats * 0.9);
  const isNearLimit = currentSeats >= Math.floor(maxSeats * 0.8);
  const canInvite = !isAtLimit && (fallbackCanInvite !== false);

  const inviteTeamMember = useAction(api.teamManagement.inviteTeamMemberMagicLink);

  // Handle phone validation
  const handlePhoneValidation = (_isValid: boolean, error?: string) => {
    setPhoneError(error || '');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      setError('Du må være logget inn for å invitere teammedlemmer');
      return;
    }

    // Check seat limits
    if (!canInvite) {
      if (blockedReason === 'hard_limit_reached') {
        setError(blockedMessage || 'Plangrense nådd. Oppgrader for å legge til flere teammedlemmer.');
      } else {
        setError('Kan ikke invitere flere teammedlemmer for øyeblikket');
      }
      return;
    }

    // Validate required fields
    if (!email.trim()) {
      setError('E-postadresse er påkrevd');
      return;
    }

    if (!firstName.trim()) {
      setError('Fornavn er påkrevd');
      return;
    }

    if (!lastName.trim()) {
      setError('Etternavn er påkrevd');
      return;
    }

    if (!phone.trim()) {
      setError('Mobilnummer er påkrevd');
      return;
    }

    // Validate phone number format
    const phoneValidation = validateNorwegianPhone(phone, 'mobile');
    if (!phoneValidation.isValid) {
      setError(phoneValidation.error || 'Ugyldig mobilnummer');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      setError('Ugyldig e-postadresse');
      return;
    }

    // Phone validation is now handled by the PhoneInput component

    setIsSubmitting(true);
    setError('');

    try {
      // Format phone number for backend (add +47 prefix and spaces)
      const formattedPhone = `+47 ${phone.slice(0, 3)} ${phone.slice(3, 5)} ${phone.slice(5)}`;

      const result = await inviteTeamMember({
        email: email.trim(),
        firstName: firstName.trim(),
        lastName: lastName.trim(),
        phone: formattedPhone,
        role,
        invitedBy: user.id,
      });

      setInvitationResult({
        success: true,
        email: email,
        firstName: firstName,
        lastName: lastName,
        message: 'Magic link invitasjon sendt via e-post!',
        invitationLink: result.invitationLink,
        invitationToken: result.invitationToken,
        expiresAt: result.expiresAt,
      });

    } catch (error: any) {
      setError(error.message || 'Kunne ikke opprette invitasjon');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setEmail('');
      setFirstName('');
      setLastName('');
      setPhone('');
      setRole('utfoerende');
      setError('');
      setInvitationResult(null);
      onClose();
    }
  };

  const handleFinishInvitation = () => {
    // Reset form and close modal
    setEmail('');
    setFirstName('');
    setLastName('');
    setPhone('');
    setRole('utfoerende');
    setError('');
    setInvitationResult(null);
    onSuccess();
  };

  // TODO: Use for copying invitation links to clipboard
  // const copyToClipboard = async (text: string) => {
  //   try {
  //     await navigator.clipboard.writeText(text);
  //     // You could add a toast notification here
  //   } catch (err) {
  //     console.error('Failed to copy to clipboard:', err);
  //   }
  // };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md" title="Inviter teammedlem">
      <div className="p-6">
        {!invitationResult ? (
          // Show invitation form
          <>
            <div className="mb-6">
              <Heading2 className="mb-2">Inviter teammedlem</Heading2>
              <BodyText className="text-jobblogg-text-medium">
                Send en magic link invitasjon til en kollega. De kan bli med i teamet med bare ett klikk.
              </BodyText>
            </div>

            {/* Seat Usage Information */}
            {!seatLoading && (
              <div className={`p-4 rounded-lg border mb-6 ${
                isAtLimit ? 'bg-jobblogg-error-soft border-jobblogg-error' :
                isCritical ? 'bg-jobblogg-warning-soft border-jobblogg-warning' :
                isNearLimit ? 'bg-jobblogg-accent-soft border-jobblogg-accent' :
                'bg-jobblogg-surface border-jobblogg-border'
              }`}>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-jobblogg-text-strong">
                    Team medlemmer
                  </span>
                  <span className={`text-sm font-medium ${
                    isAtLimit ? 'text-jobblogg-error' :
                    isCritical ? 'text-jobblogg-warning' :
                    isNearLimit ? 'text-jobblogg-accent' :
                    'text-jobblogg-text-medium'
                  }`}>
                    {currentSeats} / {maxSeats}
                  </span>
                </div>

                {warning && warningMessage && (
                  <p className={`text-xs ${
                    isAtLimit ? 'text-jobblogg-error' :
                    isCritical ? 'text-jobblogg-warning' :
                    'text-jobblogg-accent'
                  }`}>
                    {warningMessage}
                  </p>
                )}

                {!isAtLimit && (
                  <p className="text-xs text-jobblogg-text-muted mt-1">
                    {remainingSeats} {remainingSeats === 1 ? 'plass' : 'plasser'} igjen etter denne invitasjonen
                  </p>
                )}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium text-jobblogg-text-strong">
                  E-postadresse <span className="text-jobblogg-error">*</span>
                </label>
                <TextInput
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  disabled={isSubmitting}
                  required
                />
              </div>

              {/* Name Fields */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="block text-sm font-medium text-jobblogg-text-strong">
                    Fornavn <span className="text-jobblogg-error">*</span>
                  </label>
                  <TextInput
                    id="firstName"
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="Ola"
                    disabled={isSubmitting}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="lastName" className="block text-sm font-medium text-jobblogg-text-strong">
                    Etternavn <span className="text-jobblogg-error">*</span>
                  </label>
                  <TextInput
                    id="lastName"
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Nordmann"
                    disabled={isSubmitting}
                    required
                  />
                </div>
              </div>

              {/* Phone Field */}
              <div className="space-y-2">
                <label htmlFor="phone" className="block text-sm font-medium text-jobblogg-text-strong">
                  Mobilnummer <span className="text-jobblogg-error">*</span>
                </label>
                <PhoneInput
                  id="phone"
                  value={phone}
                  onChange={setPhone}
                  placeholder="XXX XX XXX"
                  disabled={isSubmitting}
                  required
                  phoneType="mobile"
                  enableValidation={true}
                  onValidation={handlePhoneValidation}
                  error={phoneError}
                />
              </div>

              {/* Role Field */}
              <div className="space-y-2">
                <label htmlFor="role" className="block text-sm font-medium text-jobblogg-text-strong">
                  Rolle <span className="text-jobblogg-error">*</span>
                </label>
                <SelectInput
                  id="role"
                  value={role}
                  onChange={(e) => setRole(e.target.value as 'administrator' | 'prosjektleder' | 'utfoerende')}
                  disabled={isSubmitting}
                  required
                  options={[
                    { value: 'utfoerende', label: 'Utførende' },
                    ...(isAdministrator ? [
                      { value: 'prosjektleder', label: 'Prosjektleder' },
                      { value: 'administrator', label: 'Administrator' }
                    ] : [])
                  ]}
                />
                <BodyText className="text-sm text-jobblogg-text-medium mt-1">
                  {role === 'administrator'
                    ? 'Administrator kan invitere andre brukere og administrere teamet.'
                    : role === 'prosjektleder'
                    ? 'Prosjektleder kan administrere prosjekter og invitere utførende til spesifikke prosjekter.'
                    : isProsjektleder
                    ? 'Utførende kan opprette og administrere prosjekter.'
                    : 'Utførende kan opprette og administrere prosjekter, men ikke invitere andre brukere.'
                  }
                </BodyText>
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-4">
                  <FormError>{error}</FormError>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
                <SecondaryButton
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                >
                  Avbryt
                </SecondaryButton>

                <PrimaryButton
                  type="submit"
                  disabled={isSubmitting || !email.trim() || !canInvite}
                  icon={isSubmitting ? (
                    <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  )}
                >
                  {isSubmitting ? 'Sender magic link...' :
                   !canInvite ? 'Plangrense nådd' :
                   'Send magic link invitasjon'}
                </PrimaryButton>
              </div>
            </form>
          </>
        ) : (
          // Show invitation result with link
          <>
            <div className="mb-6">
              <Heading2 className="mb-2">Magic link sendt!</Heading2>
              <BodyText className="text-jobblogg-text-medium">
                <strong>{invitationResult.firstName} {invitationResult.lastName}</strong> ({invitationResult.email}) har mottatt en magic link invitasjon til teamet.
              </BodyText>
            </div>

            {/* Success message for email sent */}
            <div className="mb-6 p-4 bg-jobblogg-success-soft border border-jobblogg-success rounded-lg">
              <div className="flex items-start gap-3">
                <svg className="w-5 h-5 text-jobblogg-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <BodyText className="text-jobblogg-success font-medium mb-1">
                    E-post sendt automatisk
                  </BodyText>
                  <BodyText className="text-sm text-jobblogg-text-medium">
                    Invitasjonslink og instruksjoner er sendt. Ingen ytterligere handling kreves.
                  </BodyText>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
              <PrimaryButton
                type="button"
                onClick={handleFinishInvitation}
              >
                Ferdig
              </PrimaryButton>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};
