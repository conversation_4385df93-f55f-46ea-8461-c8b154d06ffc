import React, { useState, useEffect } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { useTeamMembers } from '../../hooks/useUserRole';
import { ALL_SPECIALIZATIONS } from '../../utils/specializations';
import {
  Modal,
  PrimaryButton,
  SecondaryButton,
  SelectInput,
  TextArea,
  FormError,
  Heading2,
  BodyText,
  TextMuted,
  TextInput
} from '../ui';
import { SubcontractorSearchModal } from './SubcontractorSearchModal';

interface ProjectAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  projectId: string;
  projectName: string;
  userAccessLevel?: string; // User's access level for permission checking
  currentAssignments?: Array<{
    assignedUserId: string;
    accessLevel: string;
    assignedUserRole: string;
  }>;
}

export const ProjectAssignmentModal: React.FC<ProjectAssignmentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  projectId,
  projectName,
  userAccessLevel,
  currentAssignments = [],
}) => {
  const { user } = useUser();
  const { teamMembers } = useTeamMembers();

  const [selectedUserId, setSelectedUserId] = useState('');
  const [accessLevel, setAccessLevel] = useState<'owner' | 'collaborator' | 'subcontractor' | 'viewer'>('collaborator');
  const [notes, setNotes] = useState('');

  // Enhanced invitation fields for subcontractors
  const [sendInvitation, setSendInvitation] = useState(true);
  const [invitationMessage, setInvitationMessage] = useState('');
  const [estimatedDuration, setEstimatedDuration] = useState('1 uke'); // Default to "1 uke"
  const [urgency, setUrgency] = useState<'low' | 'medium' | 'high'>('medium');
  const [startDate, setStartDate] = useState(() => {
    // Default to current date in YYYY-MM-DD format
    return new Date().toISOString().split('T')[0];
  });
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Enhanced state for subcontractor support
  const [assignmentType, setAssignmentType] = useState<'team_member' | 'subcontractor'>('team_member');
  const [selectedSubcontractorCompany, setSelectedSubcontractorCompany] = useState<any>(null);
  const [subcontractorSpecialization, setSubcontractorSpecialization] = useState('');
  const [isSubcontractorSearchOpen, setIsSubcontractorSearchOpen] = useState(false);const assignProject = useMutation(api.teamManagement.assignProjectToUser);
  // Determine if user can assign subcontractors
  // Only main contractor (owner/administrator) can assign subcontractors
  // Subcontractors can only assign their own team members
  const canAssignSubcontractors = userAccessLevel !== 'subcontractor';

  // Reset access level when assignment type changes
  useEffect(() => {
    if (assignmentType === 'team_member') {
      setAccessLevel('collaborator');
    } else if (assignmentType === 'subcontractor') {
      // Subcontractors can only have 'subcontractor' access level
      setAccessLevel('subcontractor');
    }
    // Clear selected user and specialization when switching types
    setSelectedUserId('');
    setSelectedSubcontractorCompany(null);
    setSubcontractorSpecialization('');
  }, [assignmentType]);

  // Filter out already assigned users and current user
  const availableUsers = teamMembers.filter((member: any) =>
    member.clerkUserId !== user?.id &&
    !currentAssignments.some(assignment => assignment.assignedUserId === member.clerkUserId)
  );

  // Handle subcontractor selection
  const handleSubcontractorSelect = (company: any, selectedUser: any) => {
    setSelectedSubcontractorCompany(company);
    setSelectedUserId(selectedUser.clerkUserId);
    setIsSubcontractorSearchOpen(false);

    // Auto-populate specialization field with company's primary specialization
    // Only set if no specialization is currently selected (first time selecting this company)
    if (!subcontractorSpecialization && company.primarySpecialization) {
      setSubcontractorSpecialization(company.primarySpecialization);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id) {
      setError('Bruker ikke autentisert');
      return;
    }

    // Validation based on assignment type
    if (assignmentType === 'team_member') {
      if (!selectedUserId) {
        setError('Velg en teammedlem å tildele prosjektet til');
        return;
      }
    } else if (assignmentType === 'subcontractor') {
      if (!selectedSubcontractorCompany) {
        setError('Velg en underleverandør å tildele prosjektet til');
        return;
      }
      if (!selectedUserId) {
        setError('Velg en bruker fra underleverandør-firmaet');
        return;
      }
    }

    setIsSubmitting(true);
    setError('');

    try {
      await assignProject({
        projectId: projectId as any,
        assignedUserId: selectedUserId,
        assignedBy: user.id,
        accessLevel,
        notes: notes.trim() || undefined,
        // Enhanced subcontractor support
        isSubcontractor: assignmentType === 'subcontractor',
        subcontractorSpecialization: assignmentType === 'subcontractor' ? subcontractorSpecialization : undefined,
        // Enhanced invitation fields for subcontractors
        sendInvitation: assignmentType === 'subcontractor' ? sendInvitation : undefined,
        invitationMessage: assignmentType === 'subcontractor' && sendInvitation ? invitationMessage.trim() || undefined : undefined,
        estimatedDuration: assignmentType === 'subcontractor' && sendInvitation ? estimatedDuration.trim() || undefined : undefined,
        urgency: assignmentType === 'subcontractor' && sendInvitation ? urgency : undefined,
        startDate: assignmentType === 'subcontractor' && sendInvitation && startDate ? new Date(startDate).getTime() : undefined,
      });

      // Reset form
      setSelectedUserId('');
      setAccessLevel('collaborator');
      setNotes('');
      setAssignmentType('team_member');
      setSelectedSubcontractorCompany(null);
      setSubcontractorSpecialization('');
      // Reset invitation fields to default values
      setSendInvitation(true);
      setInvitationMessage('');
      setEstimatedDuration('1 uke'); // Reset to default
      setUrgency('medium');
      setStartDate(new Date().toISOString().split('T')[0]); // Reset to current date

      onSuccess();
    } catch (err) {
      console.error('Failed to assign project:', err);
      setError(err instanceof Error ? err.message : 'Kunne ikke tildele prosjekt');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setSelectedUserId('');
      setAccessLevel('collaborator');
      setNotes('');
      setError('');
      setAssignmentType('team_member');
      setSelectedSubcontractorCompany(null);
      setSubcontractorSpecialization('');
      // Reset invitation fields to default values
      setSendInvitation(true);
      setInvitationMessage('');
      setEstimatedDuration('1 uke'); // Reset to default
      setUrgency('medium');
      setStartDate(new Date().toISOString().split('T')[0]); // Reset to current date
      onClose();
    }
  };

  const getAccessLevelDescription = (level: string) => {
    switch (level) {
      case 'owner':
        return 'Full prosjektkontroll - kan redigere, slette og tildele til andre teammedlemmer';
      case 'collaborator':
        return 'Kan utføre arbeid, redigere prosjekt og kommunisere med kunde';
      case 'subcontractor':
        return 'Underleverandør - kan dokumentere sitt arbeid og kommunisere med kunde';
      case 'viewer':
        return 'Kan følge med på prosjektets fremdrift uten å gjøre endringer';
      default:
        return '';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md" title="Tildel prosjekt">
      <div className="p-6">
        <div className="mb-6">
          <Heading2 className="mb-2">Tildel prosjekt</Heading2>
          <BodyText className="text-jobblogg-text-medium">
            Tildel "{projectName}" til en teammedlem
          </BodyText>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
            {/* Assignment Type Selection */}
            <div className="space-y-3">
              <label className="block text-sm font-medium text-jobblogg-text-strong">
                Type tildeling <span className="text-jobblogg-error">*</span>
              </label>
              <div className={`grid gap-3 ${canAssignSubcontractors ? 'grid-cols-1 sm:grid-cols-2' : 'grid-cols-1'}`}>
                <button
                  type="button"
                  onClick={() => setAssignmentType('team_member')}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    assignmentType === 'team_member'
                      ? 'border-jobblogg-primary bg-jobblogg-primary/5 text-jobblogg-primary'
                      : 'border-jobblogg-border bg-white text-jobblogg-text-medium hover:border-jobblogg-primary/50'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <div className="text-left">
                      <div className="font-medium">Teammedlem</div>
                      <div className="text-sm opacity-75">Fra samme firma</div>
                    </div>
                  </div>
                </button>

                {canAssignSubcontractors && (
                  <button
                    type="button"
                    onClick={() => setAssignmentType('subcontractor')}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      assignmentType === 'subcontractor'
                        ? 'border-jobblogg-accent bg-jobblogg-accent/5 text-jobblogg-accent'
                        : 'border-jobblogg-border bg-white text-jobblogg-text-medium hover:border-jobblogg-accent/50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      <div className="text-left">
                        <div className="font-medium">Underleverandør</div>
                        <div className="text-sm opacity-75">Fra annet firma</div>
                      </div>
                    </div>
                  </button>
                )}
              </div>

              {/* Warning for subcontractors */}
              {!canAssignSubcontractors && (
                <div className="p-4 bg-jobblogg-warning/5 border border-jobblogg-warning/20 rounded-lg">
                  <div className="flex items-center gap-3">
                    <svg className="w-5 h-5 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <TextMuted className="text-sm">
                      Som underleverandør kan du kun tildele medlemmer fra ditt eget firma
                    </TextMuted>
                  </div>
                </div>
              )}
            </div>

            {/* Team Member Selection - Only show for team_member type */}
            {assignmentType === 'team_member' && (
              <div className="space-y-2">
                <label htmlFor="teamMember" className="block text-sm font-medium text-jobblogg-text-strong">
                  Teammedlem <span className="text-jobblogg-error">*</span>
                </label>
                {availableUsers.length === 0 ? (
                  <div className="p-4 bg-jobblogg-warning/5 border border-jobblogg-warning/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <svg className="w-5 h-5 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <div>
                        <div className="font-medium text-jobblogg-text-strong">
                          Alle teammedlemmer har allerede tilgang
                        </div>
                        <div className="text-sm text-jobblogg-text-muted">
                          Prøv å velge "Underleverandør" for å tildele til en ekstern bedrift
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <SelectInput
                    id="teamMember"
                    value={selectedUserId}
                    onChange={(e) => setSelectedUserId(e.target.value)}
                    disabled={isSubmitting}
                    required
                    placeholder="Velg teammedlem..."
                    options={[
                      ...availableUsers.map((member: any) => {
                        // Get display name with improved fallback logic
                        const getDisplayName = () => {
                          if (member.firstName && member.lastName) {
                            return `${member.firstName} ${member.lastName}`;
                          }
                          if (member.firstName) {
                            return member.firstName;
                          }
                          if (member.email) {
                            return member.email.split('@')[0];
                          }
                          return `Teammedlem ${member.clerkUserId.slice(-4)}`;
                        };

                        return {
                          value: member.clerkUserId,
                          label: `${member.role === 'administrator' ? 'Administrator' : 'Utførende'} - ${getDisplayName()}`
                        };
                      })
                    ]}
                  />
                )}
              </div>
            )}

            {/* Subcontractor Selection - Only show for subcontractor type */}
            {assignmentType === 'subcontractor' && (
              <div className="space-y-4">
                {/* Subcontractor Company Selection */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-jobblogg-text-strong">
                    Underleverandør <span className="text-jobblogg-error">*</span>
                  </label>
                  {selectedSubcontractorCompany ? (
                    <div className="p-4 bg-jobblogg-accent/5 border border-jobblogg-accent/20 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-jobblogg-text-strong">
                            {selectedSubcontractorCompany.name}
                          </div>
                          <div className="text-sm text-jobblogg-text-muted">
                            {selectedSubcontractorCompany.contactPerson && (
                              <>Daglig leder: {selectedSubcontractorCompany.contactPerson}</>
                            )}
                          </div>
                        </div>
                        <SecondaryButton
                          type="button"
                          onClick={() => setSelectedSubcontractorCompany(null)}
                          size="sm"
                        >
                          Endre
                        </SecondaryButton>
                      </div>
                    </div>
                  ) : (
                    <SecondaryButton
                      type="button"
                      onClick={() => setIsSubcontractorSearchOpen(true)}
                      className="w-full justify-center"
                    >
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      Søk etter underleverandør
                    </SecondaryButton>
                  )}
                </div>

                {/* Assignment Specialization - Only show after subcontractor is selected */}
                {selectedSubcontractorCompany && (
                  <div className="space-y-2">
                    <label htmlFor="specialization" className="block text-sm font-medium text-jobblogg-text-strong">
                      Fagområde for dette oppdraget
                    </label>
                    <SelectInput
                      id="specialization"
                      value={subcontractorSpecialization}
                      onChange={(e) => setSubcontractorSpecialization(e.target.value)}
                      placeholder="Velg fagområde (valgfritt)"
                      disabled={isSubmitting}
                      options={[
                        { value: '', label: 'Ikke spesifisert' },
                        ...ALL_SPECIALIZATIONS.map(spec => ({
                          value: spec.id,
                          label: spec.name
                        }))
                      ]}
                    />
                    <TextMuted className="text-xs">
                      Valgfritt: Spesifiser hvilket fagområde denne underleverandøren skal jobbe med på dette prosjektet
                    </TextMuted>
                  </div>
                )}
              </div>
            )}

            {/* Access Level Selection - Only show after person/company is selected */}
            {((assignmentType === 'team_member' && selectedUserId) ||
              (assignmentType === 'subcontractor' && selectedSubcontractorCompany)) && (
              <div className="space-y-2">
                <label className="block text-sm font-medium text-jobblogg-text-strong">
                  Tilgangsnivå <span className="text-jobblogg-error">*</span>
                </label>

                {assignmentType === 'team_member' ? (
                  // Team members get dropdown with multiple options
                  <>
                    <SelectInput
                      id="accessLevel"
                      value={accessLevel}
                      onChange={(e) => setAccessLevel(e.target.value as 'owner' | 'collaborator' | 'subcontractor' | 'viewer')}
                      disabled={isSubmitting}
                      required
                      options={[
                        { value: 'collaborator', label: 'Utførende' },
                        { value: 'viewer', label: 'Observatør' },
                        { value: 'owner', label: 'Prosjektleder' }
                      ]}
                    />
                    <TextMuted className="text-sm mt-1">
                      {getAccessLevelDescription(accessLevel)}
                    </TextMuted>
                  </>
                ) : (
                  // Subcontractors get static badge - no choice needed
                  <>
                    <div className="flex items-center gap-3">
                      <span className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20">
                        Underleverandør
                      </span>
                      <TextMuted className="text-sm">
                        Automatisk tildelt for underleverandører
                      </TextMuted>
                    </div>
                    <TextMuted className="text-sm mt-1">
                      {getAccessLevelDescription('subcontractor')}
                    </TextMuted>
                  </>
                )}
              </div>
            )}

            {/* Notes Field - Only show after person/company is selected */}
            {((assignmentType === 'team_member' && selectedUserId) ||
              (assignmentType === 'subcontractor' && selectedSubcontractorCompany)) && (
              <div className="space-y-2">
                <label htmlFor="notes" className="block text-sm font-medium text-jobblogg-text-strong">
                  Notater (valgfritt)
                </label>
                <TextArea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Legg til notater om tildelingen..."
                  disabled={isSubmitting}
                  rows={3}
                />
              </div>
            )}

            {/* Enhanced Invitation Fields for Subcontractors */}
            {assignmentType === 'subcontractor' && selectedSubcontractorCompany && (
              <div className="space-y-6 pt-6 border-t border-jobblogg-border">
                <div className="flex items-center gap-3">
                  <svg className="w-5 h-5 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <Heading2 className="text-lg">Invitasjonsdetaljer</Heading2>
                </div>

                {/* Send Invitation Toggle */}
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="sendInvitation"
                    checked={sendInvitation}
                    onChange={(e) => setSendInvitation(e.target.checked)}
                    disabled={isSubmitting}
                    className="w-4 h-4 text-jobblogg-primary border-jobblogg-border rounded focus:ring-jobblogg-primary focus:ring-2"
                  />
                  <label htmlFor="sendInvitation" className="text-sm font-medium text-jobblogg-text-strong">
                    Send profesjonell invitasjon med e-post
                  </label>
                </div>

                {sendInvitation && (
                  <div className="space-y-4 pl-7">
                    {/* Personal Message */}
                    <div className="space-y-2">
                      <label htmlFor="invitationMessage" className="block text-sm font-medium text-jobblogg-text-strong">
                        Personlig melding (valgfritt)
                      </label>
                      <TextArea
                        id="invitationMessage"
                        value={invitationMessage}
                        onChange={(e) => setInvitationMessage(e.target.value)}
                        placeholder="F.eks. 'Vi ser frem til å samarbeide med dere på dette prosjektet...'"
                        disabled={isSubmitting}
                        rows={3}
                      />
                    </div>

                    {/* Duration and Urgency Row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Estimated Duration */}
                      <div className="space-y-2">
                        <label htmlFor="estimatedDuration" className="block text-sm font-medium text-jobblogg-text-strong">
                          Estimert varighet
                        </label>
                        <TextInput
                          id="estimatedDuration"
                          value={estimatedDuration}
                          onChange={(e) => setEstimatedDuration(e.target.value)}
                          placeholder="F.eks. '2-3 uker'"
                          disabled={isSubmitting}
                        />
                      </div>

                      {/* Urgency */}
                      <div className="space-y-2">
                        <label htmlFor="urgency" className="block text-sm font-medium text-jobblogg-text-strong">
                          Prioritet
                        </label>
                        <SelectInput
                          id="urgency"
                          value={urgency}
                          onChange={(e) => setUrgency(e.target.value as 'low' | 'medium' | 'high')}
                          disabled={isSubmitting}
                          options={[
                            { value: 'low', label: 'Lav prioritet' },
                            { value: 'medium', label: 'Middels prioritet' },
                            { value: 'high', label: 'Høy prioritet' }
                          ]}
                        />
                      </div>
                    </div>

                    {/* Start Date */}
                    <div className="space-y-2">
                      <label htmlFor="startDate" className="block text-sm font-medium text-jobblogg-text-strong">
                        Ønsket oppstart (valgfritt)
                      </label>
                      <TextInput
                        id="startDate"
                        type="date"
                        value={startDate}
                        onChange={(e) => setStartDate(e.target.value)}
                        disabled={isSubmitting}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>

                    {/* Info Box */}
                    <div className="bg-jobblogg-accent/10 border border-jobblogg-accent/20 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <svg className="w-5 h-5 text-jobblogg-accent mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <div>
                          <p className="text-sm font-medium text-jobblogg-accent mb-1">
                            Profesjonell invitasjon
                          </p>
                          <p className="text-sm text-jobblogg-text-medium">
                            Underleverandøren vil motta en detaljert e-post med prosjektinformasjon og kan godta eller avslå invitasjonen.
                            Prosjektet blir først tilgjengelig når invitasjonen godtas.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Current Assignments Info */}
            {currentAssignments.length > 0 && (
              <div className="bg-jobblogg-neutral/50 rounded-lg p-4">
                <BodyText className="font-medium mb-2">
                  Nåværende tildelinger:
                </BodyText>
                <div className="space-y-1">
                  {currentAssignments.map((assignment, index) => (
                    <TextMuted key={index} className="text-sm">
                      • {assignment.assignedUserRole === 'administrator' ? 'Administrator' : 'Utførende'} ({assignment.assignedUserId.slice(-4)}) - {
                        assignment.accessLevel === 'owner' ? 'Prosjektleder' :
                        assignment.accessLevel === 'collaborator' ? 'Utførende' : 'Observatør'
                      }
                    </TextMuted>
                  ))}
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <FormError>{error}</FormError>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
              <SecondaryButton
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Avbryt
              </SecondaryButton>

              <PrimaryButton
                type="submit"
                disabled={
                  isSubmitting ||
                  (assignmentType === 'team_member' && (availableUsers.length === 0 || !selectedUserId)) ||
                  (assignmentType === 'subcontractor' && (!selectedSubcontractorCompany || !selectedUserId))
                }
                icon={isSubmitting ? (
                  <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                )}
              >
                {isSubmitting ? 'Tildeler...' : 'Tildel prosjekt'}
              </PrimaryButton>
            </div>
          </form>
      </div>

      {/* Subcontractor Search Modal */}
      <SubcontractorSearchModal
        isOpen={isSubcontractorSearchOpen}
        onClose={() => setIsSubcontractorSearchOpen(false)}
        onSelect={handleSubcontractorSelect}
        currentProjectId={projectId}
      />
    </Modal>
  );
};
