import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  Modal,
  Heading2,
  BodyText,
  TextMuted,
  DangerButton,
  SecondaryButton
} from '../ui';

interface TeamMember {
  _id: string;
  clerkUserId: string;
  role: string;
  invitationStatus?: string;
  invitedAt?: number;
  acceptedAt?: number;
  createdAt: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  lastLoginAt?: number;
  lastActivityAt?: number;
}

interface RemoveTeamMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  member: TeamMember;
  onSuccess: () => void;
}

export const RemoveTeamMemberModal: React.FC<RemoveTeamMemberModalProps> = ({
  isOpen,
  onClose,
  member,
  onSuccess,
}) => {
  const { user } = useUser();
  const [isRemoving, setIsRemoving] = useState(false);const revokeInvitation = useMutation(api.teamManagement.revokeInvitation);const deleteTeamMember = useMutation(api.teamManagement.deleteTeamMember);
  const isPendingInvitation = member.invitationStatus === 'pending' ||
    (!member.invitationStatus && !member.acceptedAt);

  // Get display name
  const getDisplayName = () => {
    if (member.firstName && member.lastName) {
      return `${member.firstName} ${member.lastName}`;
    }
    if (member.firstName) {
      return member.firstName;
    }
    if (member.email) {
      return member.email.split('@')[0];
    }
    return `Teammedlem ${member.clerkUserId.slice(-4)}`;
  };

  const displayName = getDisplayName();
  const roleLabel = member.role === 'administrator' ? 'Administrator' : 'Utførende';

  const handleRemove = async () => {
    if (!user?.id) return;

    setIsRemoving(true);
    try {
      if (isPendingInvitation) {
        await revokeInvitation({
          invitationId: member._id as any,
          revokedBy: user.id,
        });
      } else {
        await deleteTeamMember({
          teamMemberId: member._id as any,
          deletedBy: user.id,
        });
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Failed to remove team member:', error);
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="md" title="Fjern teammedlem">
      <div className="space-y-6">
        {/* Header with warning icon */}
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 rounded-full bg-jobblogg-error/10 flex items-center justify-center flex-shrink-0">
            <svg className="w-6 h-6 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>

          <div className="flex-1">
            <Heading2 className="text-jobblogg-error">
              {isPendingInvitation ? 'Kanseller invitasjon' : 'Fjern teammedlem'}
            </Heading2>
            <TextMuted>
              Bekreft at du vil {isPendingInvitation ? 'kansellere invitasjonen til' : 'fjerne'} {displayName}
            </TextMuted>
          </div>
        </div>

        {/* Member info */}
        <div className="bg-jobblogg-neutral/30 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-8 h-8 rounded-full bg-jobblogg-primary/10 flex items-center justify-center">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <BodyText className="font-medium">{displayName}</BodyText>
              <TextMuted className="text-sm">{roleLabel}</TextMuted>
            </div>
          </div>

          {member.email && (
            <TextMuted className="text-sm">
              {member.email}
            </TextMuted>
          )}
        </div>

        {/* Warning message */}
        <div className="bg-jobblogg-warning/10 border border-jobblogg-warning/20 rounded-lg p-4">
          <div className="flex gap-3">
            <svg className="w-5 h-5 text-jobblogg-warning flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div className="space-y-2">
              <BodyText className="font-medium text-jobblogg-warning">
                {isPendingInvitation ? 'Hva skjer når du kansellerer invitasjonen:' : 'Hva skjer når du fjerner teammedlemmet:'}
              </BodyText>

              {isPendingInvitation ? (
                <TextMuted className="text-sm">
                  Dette vil kansellere invitasjonen. Brukeren vil ikke lenger kunne bli med i teamet med denne invitasjonen.
                </TextMuted>
              ) : (
                <TextMuted className="text-sm">
                  Dette vil fjerne brukeren fra teamet. Brukeren vil miste tilgang til alle tildelte prosjekter, men historiske data (meldinger, loggføringer) vil bevares.
                </TextMuted>
              )}
            </div>
          </div>
        </div>

        {/* Consequences breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* What will be removed */}
          <div className="bg-jobblogg-error/5 border border-jobblogg-error/10 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <svg className="w-4 h-4 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <BodyText className="font-medium text-jobblogg-error text-sm">
                Vil fjernes
              </BodyText>
            </div>
            <ul className="space-y-1 text-sm text-jobblogg-text-medium">
              <li>• Tilgang til teamet</li>
              {!isPendingInvitation && (
                <>
                  <li>• Alle prosjekttildelinger</li>
                  <li>• Tilgang til tildelte prosjekter</li>
                </>
              )}
            </ul>
          </div>

          {/* What will be preserved */}
          {!isPendingInvitation && (
            <div className="bg-jobblogg-success/5 border border-jobblogg-success/10 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <BodyText className="font-medium text-jobblogg-success text-sm">
                  Vil bevares
                </BodyText>
              </div>
              <ul className="space-y-1 text-sm text-jobblogg-text-medium">
                <li>• Historiske meldinger</li>
                <li>• Loggføringer og bidrag</li>
                <li>• Prosjekter brukeren eier</li>
                <li>• Audit trail og historikk</li>
              </ul>
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
          <SecondaryButton onClick={onClose} disabled={isRemoving}>
            Avbryt
          </SecondaryButton>

          <DangerButton
            onClick={handleRemove}
            loading={isRemoving}
          >
            {isPendingInvitation ? 'Kanseller invitasjon' : 'Fjern bruker'}
          </DangerButton>
        </div>
      </div>
    </Modal>
  );
};
