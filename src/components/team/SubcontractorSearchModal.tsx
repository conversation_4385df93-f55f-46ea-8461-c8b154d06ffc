import React, { useState, useEffect } from 'react';
import { useMemo } from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { useDebouncedCompanyLookup } from '../../hooks/useCompanyLookup';
import { CompanyInfo } from '../../services/companyLookup';
import {
  Modal,
  PrimaryButton,
  SecondaryButton,
  TextInput,
  Heading2,
  BodyText,
  TextMuted
} from '../ui';

interface SubcontractorSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (company: any, selectedUser: any) => void;
  currentProjectId?: string; // For context-aware collaboration display
}

interface BrregCompanyWithStatus extends CompanyInfo {
  isRegistered: boolean;
  registrationType: 'contractor' | 'customer' | null;
  jobbLoggCompany?: {
    id: string;
    name: string;
    contactPerson?: string;
    phone?: string;
    email?: string;
    specializations: string[];
  } | null;
  contractorDetails?: {
    userId: string;
    clerkUserId: string;
    role: string;
    specializations: string[];
  } | null;
}

interface BrregCompanyCardProps {
  company: BrregCompanyWithStatus;
  onSelect: () => void;
  onSendInvitation: () => void;
  isSelected: boolean;
}

const BrregCompanyCard: React.FC<BrregCompanyCardProps> = ({
  company,
  onSelect,
  onSendInvitation,
  isSelected
}) => {
  const canSelect = company.isRegistered;

  return (
    <div
      className={`p-4 sm:p-6 border rounded-lg transition-all duration-200 ${
        isSelected
          ? 'border-jobblogg-primary bg-jobblogg-primary/5'
          : canSelect
            ? 'border-jobblogg-success/30 bg-jobblogg-success/5 hover:border-jobblogg-success/50 hover:bg-jobblogg-success/10 cursor-pointer'
            : 'border-jobblogg-border bg-gray-50 hover:bg-gray-100'
      }`}
      onClick={canSelect ? onSelect : undefined}
    >
      <div className="space-y-3 sm:space-y-4">
        {/* Company Header */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-4">
          <div className="flex-1 min-w-0">
            <BodyText className="font-semibold text-jobblogg-text-strong text-base sm:text-lg leading-tight">
              {company.name}
            </BodyText>
          </div>

          {/* Status Badge - Mobile optimized */}
          <div className="flex-shrink-0">
            {company.isRegistered ? (
              <span className="inline-flex items-center gap-1 px-2 py-1 sm:px-3 sm:py-1.5 rounded-full text-xs font-semibold bg-jobblogg-accent/12 text-jobblogg-accent border border-jobblogg-accent/25 shadow-sm">
                <span className="hidden sm:inline">✓ Registrert i</span>
                <span className="sm:hidden">✓</span>
                <span className="ml-1">
                  <span className="font-bold tracking-tight">Jobb</span><span className="font-bold tracking-tight text-jobblogg-primary">Logg</span>
                </span>
              </span>
            ) : (
              <span className="inline-flex items-center gap-1 px-2 py-1 sm:px-3 sm:py-1.5 rounded-full text-xs font-semibold bg-jobblogg-text-muted/8 text-jobblogg-text-muted border border-jobblogg-text-muted/20 shadow-sm">
                <span className="hidden sm:inline">Ikke registrert i</span>
                <span className="sm:hidden">Ikke i</span>
                <span className="ml-1">
                  <span className="font-bold tracking-tight">Jobb</span><span className="font-bold tracking-tight text-jobblogg-primary">Logg</span>
                </span>
              </span>
            )}
          </div>
        </div>

        {/* Company Details */}
        <div className="space-y-1.5">
          <TextMuted className="text-sm">
            Org.nr: {company.organizationNumber}
          </TextMuted>
          {company.organizationForm && (
            <TextMuted className="text-sm">
              {company.organizationForm}
            </TextMuted>
          )}
          {company.visitingAddress && (
            <TextMuted className="text-sm break-words">
              📍 {company.visitingAddress.street}, {company.visitingAddress.postalCode} {company.visitingAddress.city}
            </TextMuted>
          )}
          {company.industryDescription && (
            <TextMuted className="text-sm flex items-start gap-1">
              <span className="flex-shrink-0">🏢</span>
              <span className="break-words">{company.industryDescription}</span>
            </TextMuted>
          )}
        </div>

        {/* JobbLogg Details (if registered) */}
        {company.isRegistered && company.jobbLoggCompany && (
          <div className="bg-jobblogg-accent/8 rounded-lg p-3 sm:p-4 border border-jobblogg-accent/20 shadow-sm">
            <div className="space-y-2 sm:space-y-3">
              <div className="flex items-center gap-2">
                <span className="text-xs sm:text-sm font-semibold text-jobblogg-accent">
                  <span className="font-bold tracking-tight">Jobb</span><span className="font-bold tracking-tight text-jobblogg-primary">Logg</span>
                  <span className="hidden sm:inline ml-1">detaljer</span>
                </span>
              </div>
              {company.jobbLoggCompany.contactPerson && (
                <TextMuted className="text-sm break-words">
                  👤 {company.jobbLoggCompany.contactPerson}
                </TextMuted>
              )}
              {Array.isArray(company.jobbLoggCompany.specializations) && company.jobbLoggCompany.specializations.length > 0 && (
                <div className="flex flex-wrap gap-1.5 mt-2">
                  {company.jobbLoggCompany.specializations.slice(0, 2).map((spec, index) => (
                    <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-primary/15 text-jobblogg-primary border border-jobblogg-primary/20 break-words max-w-full">
                      {spec}
                    </span>
                  ))}
                  {company.jobbLoggCompany.specializations.length > 2 && (
                    <span className="text-xs text-jobblogg-text-muted font-medium flex items-center">
                      +{company.jobbLoggCompany.specializations.length - 2} flere
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {!company.isRegistered && (
          <div className="flex gap-2 pt-2 sm:pt-3">
            <SecondaryButton
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onSendInvitation();
              }}
              className="flex-1 min-h-[44px] text-xs sm:text-sm"
            >
              📧 Send registreringsinvitasjon
            </SecondaryButton>
          </div>
        )}
      </div>
    </div>
  );
};

export const SubcontractorSearchModal: React.FC<SubcontractorSearchModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  currentProjectId: _currentProjectId
}) => {
  const { user } = useUser();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCompany, setSelectedCompany] = useState<BrregCompanyWithStatus | null>(null);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [companiesWithStatus, setCompaniesWithStatus] = useState<BrregCompanyWithStatus[]>([]);

  // Use BRREG company lookup hook
  const {
    results: brregResults,
    isLoading: isBrregLoading,
    error: brregError,
    hasSearched,
    debouncedSearchByName,
    clearResults
  } = useDebouncedCompanyLookup(500);

  // Get previous collaboration partners when search is empty
    // const previousPartners = useQuery(
  //   api.collaborationHistory.searchSubcontractorsWithCollaboration,
  //   user?.id && !searchTerm.trim() ? {
  //     onlyPreviouslyWorkedWith: true,
  //     requestedBy: user.id,
  //     currentProjectId: currentProjectId as any
  //   } : 'skip'
  // );
  // Check JobbLogg registration status for BRREG results
  // const registrationStatusArgs = useMemo(() => {
  //   if (!brregResults.length || !user?.id) {
  //     return 'skip' as const;
  //   }
  //   return {
  //     companies: brregResults.map(company => ({
  //       organizationNumber: company.organizationNumber,
  //       name: company.name,
  //     })),
  //     requestedBy: user.id,
  //   };
  // }, [brregResults, user?.id]); // TODO: Re-enable when needed

  const registrationStatus = useQuery(
    api.collaborationHistory.checkJobbLoggRegistrationStatus,
    'skip' // registrationStatusArgs
  );

  // Get company details when a registered company is selected
  const companyDetailsArgs = useMemo(() => {
    if (!selectedCompany?.isRegistered || !selectedCompany.jobbLoggCompany?.id || !user?.id) {
      return 'skip' as const;
    }
    return {
      companyId: selectedCompany.jobbLoggCompany.id as any,
      requestedBy: user.id
    };
  }, [selectedCompany?.jobbLoggCompany?.id, user?.id]);

  const companyDetails = useQuery(
    api.teamManagement.getSubcontractorCompanyDetails,
    companyDetailsArgs
  );

  // Combine BRREG results with JobbLogg registration status
  useEffect(() => {
    if (brregResults.length && registrationStatus) {
      const combined: BrregCompanyWithStatus[] = brregResults.map((brregCompany: any) => {
        const statusInfo = registrationStatus.find(
          (status: any) => status.organizationNumber === brregCompany.organizationNumber
        );

        return {
          ...brregCompany,
          isRegistered: statusInfo?.isRegistered || false,
          registrationType: statusInfo?.registrationType || null,
          jobbLoggCompany: statusInfo?.jobbLoggCompany || null,
          contractorDetails: statusInfo?.contractorDetails || null,
        };
      });

      setCompaniesWithStatus(combined);
    } else if (!brregResults.length) {
      setCompaniesWithStatus([]);
    }
  }, [brregResults, registrationStatus]);

  // Handle search input changes
  useEffect(() => {
    if (searchTerm.trim()) {
      debouncedSearchByName(searchTerm.trim());
    } else {
      clearResults();
      setCompaniesWithStatus([]);
    }
  }, [searchTerm, debouncedSearchByName, clearResults]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSearchTerm('');
      setSelectedCompany(null);
      setSelectedUser(null);
      setCompaniesWithStatus([]);
      clearResults();
    }
  }, [isOpen, clearResults]);

  const handleCompanySelect = (company: BrregCompanyWithStatus) => {
    // Only allow selection of registered companies
    if (company.isRegistered) {
      setSelectedCompany(company);
      setSelectedUser(null); // Reset user selection when company changes
    }
  };

  const handleUserSelect = (user: any) => {
    setSelectedUser(user);
  };

  const handleConfirm = () => {
    if (selectedCompany?.isRegistered && selectedUser && companyDetails) {
      // Pass the detailed company information that includes primarySpecialization
      onSelect(companyDetails.company, selectedUser);
      onClose();
    }
  };

  const handleSendRegistrationInvitation = (company: BrregCompanyWithStatus) => {
    // TODO: Implement registration invitation flow
    console.log('Send registration invitation to:', company);
    alert(`Registreringsinvitasjon vil bli sendt til "${company.name}". Denne funksjonen kommer snart!`);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg" title="Søk underentreprenør">
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <svg className="w-6 h-6 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <Heading2>Søk etter underleverandør</Heading2>
          </div>

          {/* Information Box */}
          <div className="bg-jobblogg-primary/5 rounded-lg p-4 border border-jobblogg-primary/20">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-jobblogg-primary mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div className="space-y-1">
                <BodyText className="text-sm font-medium text-jobblogg-primary">
                  Viktig å vite
                </BodyText>
                <TextMuted className="text-sm">
                  Søk blant alle norske bedrifter i Brønnøysundregisteret. Kun bedrifter som allerede er registrert i JobbLogg kan legges til direkte.
                  For andre bedrifter kan du sende registreringsinvitasjon.
                </TextMuted>
              </div>
            </div>
          </div>
        </div>

        {!selectedCompany ? (
          // Company Search View
          <div className="space-y-4">
            {/* Primary Company Search */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-jobblogg-text-strong">
                Søk i Brønnøysundregisteret
              </label>
              <div className="relative">
                <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <TextInput
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Firmanavn eller organisasjonsnummer..."
                  className="pl-12 py-3 text-base"
                />
              </div>
            </div>

            {/* Show previous partners when no search */}
            {!searchTerm.trim() && previousPartners && previousPartners.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm text-jobblogg-text-medium">
                  <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Tidligere samarbeidspartnere</span>
                </div>
                <div className="space-y-2 max-h-48 overflow-y-auto">
                  {previousPartners.slice(0, 5).map((partner: any) => (
                    <div
                      key={partner.id}
                      onClick={() => handleCompanySelect({
                        ...partner,
                        isRegistered: true,
                        registrationType: 'contractor' as const,
                        jobbLoggCompany: {
                          id: partner.id,
                          name: partner.name,
                          contactPerson: partner.contactPerson,
                          phone: partner.phone,
                          email: partner.email,
                          specializations: partner.specializations || [],
                        }
                      })}
                      className="p-3 bg-jobblogg-success/5 border border-jobblogg-success/20 rounded-lg cursor-pointer hover:bg-jobblogg-success/10 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <BodyText className="font-medium text-jobblogg-text-strong">{partner.name}</BodyText>
                          <TextMuted className="text-sm">{partner.contactPerson}</TextMuted>
                        </div>
                        <div className="text-xs text-jobblogg-success font-medium">
                          {partner.collaboration?.projectCount || 0} prosjekt{(partner.collaboration?.projectCount || 0) !== 1 ? 'er' : ''}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* BRREG Search Results */}
            {searchTerm.trim() && (
              <div className="space-y-3">
                {/* Loading State */}
                {isBrregLoading && (
                  <div className="text-center py-8">
                    <div className="animate-spin w-6 h-6 border-2 border-jobblogg-primary border-t-transparent rounded-full mx-auto mb-2"></div>
                    <TextMuted>Søker i Brønnøysundregisteret...</TextMuted>
                  </div>
                )}

                {/* Error State */}
                {brregError && (
                  <div className="text-center py-8">
                    <svg className="w-12 h-12 text-jobblogg-error mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <BodyText className="text-jobblogg-error">Feil ved søk</BodyText>
                    <TextMuted className="text-sm">{brregError.message}</TextMuted>
                  </div>
                )}

                {/* No Results */}
                {hasSearched && !isBrregLoading && !brregError && companiesWithStatus.length === 0 && (
                  <div className="text-center py-8 space-y-6">
                    <div>
                      <svg className="w-12 h-12 text-jobblogg-text-muted mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                      </svg>
                      <BodyText className="text-jobblogg-text-muted">
                        Fant ikke "{searchTerm}"
                      </BodyText>
                      <TextMuted className="text-sm">
                        Ingen bedrifter funnet i Brønnøysundregisteret
                      </TextMuted>
                    </div>
                  </div>
                )}

                {/* Results List */}
                {companiesWithStatus.length > 0 && (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    <div className="text-sm text-jobblogg-text-medium mb-3">
                      {companiesWithStatus.length} bedrift{companiesWithStatus.length !== 1 ? 'er' : ''} funnet i Brønnøysundregisteret
                    </div>

                    {(companiesWithStatus as any[]).map((company: any) => {
                      const companyOrgNumber = company?.organizationNumber || '';
                      const selectedOrgNumber = (selectedCompany as any)?.organizationNumber || '';
                      return (
                        <BrregCompanyCard
                          key={companyOrgNumber}
                          company={company}
                          onSelect={() => handleCompanySelect(company)}
                          onSendInvitation={() => handleSendRegistrationInvitation(company)}
                          isSelected={selectedOrgNumber === companyOrgNumber}
                        />
                      );
                    })}
                  </div>
                )}
              </div>
            )}

          </div>
        ) : (
          // User Selection View
          <div className="space-y-4">
            {/* Selected Company Header */}
            <div className="p-4 bg-jobblogg-accent/5 border border-jobblogg-accent/20 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <BodyText className="font-medium text-jobblogg-text-strong">
                    {selectedCompany.name}
                  </BodyText>
                  <TextMuted className="text-sm">
                    Velg daglig leder fra dette firmaet
                  </TextMuted>
                </div>
                <SecondaryButton
                  onClick={() => setSelectedCompany(null)}
                  size="sm"
                >
                  Tilbake
                </SecondaryButton>
              </div>
            </div>

            {/* Team Members */}
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {companyDetails === undefined ? (
                <div className="text-center py-8">
                  <div className="animate-spin w-6 h-6 border-2 border-jobblogg-primary border-t-transparent rounded-full mx-auto mb-2"></div>
                  <TextMuted>Laster teammedlemmer...</TextMuted>
                </div>
              ) : companyDetails.teamMembers.length === 0 ? (
                <div className="text-center py-8">
                  <svg className="w-12 h-12 text-jobblogg-text-muted mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <BodyText className="text-jobblogg-text-muted">
                    Ingen daglig leder funnet
                  </BodyText>
                </div>
              ) : (
                companyDetails.teamMembers.map((member: any) => (
                  <div
                    key={member._id}
                    onClick={() => handleUserSelect(member)}
                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                      selectedUser?._id === member._id
                        ? 'border-jobblogg-primary bg-jobblogg-primary/5'
                        : 'border-jobblogg-border hover:border-jobblogg-primary/50 hover:bg-jobblogg-primary/5'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <BodyText className="font-medium">
                          {member.displayName}
                        </BodyText>
                        <div className="flex items-center gap-2 mt-1">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            member.role === 'daglig_leder' || member.role === 'administrator'
                              ? 'bg-jobblogg-primary/10 text-jobblogg-primary'
                              : 'bg-jobblogg-success/10 text-jobblogg-success'
                          }`}>
                            {member.role === 'daglig_leder' || member.role === 'administrator' ? 'Daglig leder' : 'Utførende'}
                          </span>
                          {member.specialization && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-accent/10 text-jobblogg-accent">
                              {member.specialization}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t border-jobblogg-border">
          <SecondaryButton onClick={onClose}>
            Avbryt
          </SecondaryButton>
          {selectedCompany && selectedUser && companyDetails && (
            <PrimaryButton onClick={handleConfirm}>
              Velg {selectedUser.displayName}
            </PrimaryButton>
          )}
        </div>
      </div>
    </Modal>
  );
};
