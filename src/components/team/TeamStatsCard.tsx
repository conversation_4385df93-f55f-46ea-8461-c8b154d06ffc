import React, { useState, useCallback } from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { useUserRole } from '../../hooks/useUserRole';
import { useRoleChangeListener } from '../../hooks/useRoleChangeHandler';
import { StatsCard } from '../ui';

interface TeamStatsCardProps {
  animationDelay?: string;
  onClick?: () => void;
  className?: string;
}

/**
 * Team statistics card component for dashboard
 * Shows team member count and seat usage for administrators
 *
 * @example
 * ```tsx
 * <TeamStatsCard
 *   animationDelay="0.2s"
 *   onClick={() => navigate('/team')}
 * />
 * ```
 */
export const TeamStatsCard: React.FC<TeamStatsCardProps> = ({
  animationDelay = '0s',
  onClick,
  className = ''
}) => {
  const { user } = useUser();
  const { isAdministrator, isLoading: roleLoading } = useUserRole();


  const [_forceUpdate, setForceUpdate] = useState(0);

  // Listen for role changes and force re-render when user becomes administrator
  const handleRoleChange = useCallback((event: CustomEvent) => {
    if (event.detail.becameAdmin || event.detail.lostAdmin) {
      console.log('TeamStatsCard: Role change detected, updating visibility');
      setForceUpdate(prev => prev + 1);
    }
  }, []);

  useRoleChangeListener(handleRoleChange);

  // Get accurate team member count with proper query
  const teamMemberCount = useQuery(
    api.seatManagement.getTeamMemberCount,
    user?.id && isAdministrator ? { userId: user.id } : "skip"
  );



  // Don't show for non-administrators or while loading
  if (roleLoading || !isAdministrator || !user?.id) {
    return null;
  }

  // Show loading state while data is being fetched
  if (teamMemberCount === undefined) {
    return (
      <div className={`animate-pulse ${className}`} style={{ animationDelay }}>
        <div className="bg-white rounded-xl p-6 shadow-lg border border-jobblogg-border">
          <div className="skeleton h-4 w-20 mb-3"></div>
          <div className="skeleton h-8 w-16 mb-2"></div>
          <div className="skeleton h-3 w-24"></div>
        </div>
      </div>
    );
  }

  const currentSeats = teamMemberCount?.actualTeamMemberCount || 0;
  const maxSeats = teamMemberCount?.maxSeats || 0;
  const remainingSeats = Math.max(0, maxSeats - currentSeats);

  // Format the display value
  const displayValue = maxSeats > 0 ? `${currentSeats} / ${maxSeats}` : currentSeats.toString();

  // Format the subtitle
  const subtitle = maxSeats > 0
    ? `${remainingSeats} ${remainingSeats === 1 ? 'plass' : 'plasser'} tilgjengelig`
    : 'Teammedlemmer';

  return (
    <StatsCard
      title="Team medlemmer"
      value={displayValue}
      subtitle={subtitle}
      variant="primary"
      animationDelay={animationDelay}
      onClick={onClick}
      className={className}
      icon={
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 015.196-2.121m0 0a5.002 5.002 0 019.608 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      }
    />
  );
};
