import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  Modal,
  Heading2,
  BodyText,
  TextMuted,
  PrimaryButton,
  SecondaryButton
} from '../ui';

interface TeamMember {
  _id: string;
  clerkUserId: string;
  role: string;
  invitationStatus?: string;
  invitedAt?: number;
  acceptedAt?: number;
  createdAt: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  lastLoginAt?: number;
  lastActivityAt?: number;
  isBlocked?: boolean;
  blockedAt?: number;
  blockedBy?: string;
  blockedReason?: string;
}

interface UnblockTeamMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  member: TeamMember;
  onSuccess: () => void;
}

export const UnblockTeamMemberModal: React.FC<UnblockTeamMemberModalProps> = ({
  isOpen,
  onClose,
  member,
  onSuccess,
}) => {
  const { user } = useUser();
  const [isUnblocking, setIsUnblocking] = useState(false);
  const [error, setError] = useState<string>('');const unblockTeamMember = useMutation(api.teamManagement.unblockTeamMember);
  // Get display name
  const getDisplayName = () => {
    if (member.firstName && member.lastName) {
      return `${member.firstName} ${member.lastName}`;
    }
    if (member.firstName) {
      return member.firstName;
    }
    if (member.email) {
      return member.email.split('@')[0];
    }
    return `Teammedlem ${member.clerkUserId.slice(-4)}`;
  };

  const displayName = getDisplayName();
  const roleLabel = member.role === 'administrator' ? 'Administrator' : 'Utførende';

  // Format blocked date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleUnblock = async () => {
    if (!user?.id) return;

    setIsUnblocking(true);
    setError('');

    try {
      await unblockTeamMember({
        teamMemberId: member._id as any,
        unblockedBy: user.id,
      });

      onSuccess();
      onClose();
    } catch (error: any) {
      console.error('Failed to unblock team member:', error);
      setError(error.message || 'Kunne ikke åpne teammedlem');
    } finally {
      setIsUnblocking(false);
    }
  };

  const handleClose = () => {
    setError('');
    onClose();
  };

  const isUpgradeError = error.includes('Oppgrader til');

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md" title="Opphev blokkering">
      <div className="space-y-6">
        {/* Header with success icon */}
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 rounded-full bg-jobblogg-success/10 flex items-center justify-center flex-shrink-0">
            <svg className="w-6 h-6 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
            </svg>
          </div>

          <div className="flex-1">
            <Heading2 className="text-jobblogg-success">
              Åpne teammedlem
            </Heading2>
            <TextMuted>
              Dette vil gjenåpne {displayName}s tilgang til teamet
            </TextMuted>
          </div>
        </div>

        {/* Member info */}
        <div className="bg-jobblogg-neutral/30 rounded-lg p-4">
          <div className="flex items-center gap-3 mb-2">
            <div className="w-8 h-8 rounded-full bg-jobblogg-primary/10 flex items-center justify-center">
              <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <div>
              <BodyText className="font-medium">{displayName}</BodyText>
              <TextMuted className="text-sm">{roleLabel}</TextMuted>
            </div>
          </div>

          {member.email && (
            <TextMuted className="text-sm">
              {member.email}
            </TextMuted>
          )}
        </div>

        {/* Current blocking info */}
        {member.blockedAt && (
          <div className="bg-jobblogg-error/5 border border-jobblogg-error/10 rounded-lg p-4">
            <div className="flex gap-3">
              <svg className="w-5 h-5 text-jobblogg-error flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m4-6V9a4 4 0 10-8 0v2m0 0v2a2 2 0 002 2h4a2 2 0 002-2v-2" />
              </svg>
              <div className="space-y-1">
                <BodyText className="font-medium text-jobblogg-error text-sm">
                  Sperret siden {formatDate(member.blockedAt)}
                </BodyText>
                {member.blockedReason && (
                  <div className="bg-jobblogg-neutral/20 rounded p-2 mt-2">
                    <TextMuted className="text-xs font-medium mb-1">
                      Administratornotat:
                    </TextMuted>
                    <TextMuted className="text-sm">
                      {member.blockedReason}
                    </TextMuted>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Success message */}
        <div className="bg-jobblogg-success/10 border border-jobblogg-success/20 rounded-lg p-4">
          <div className="flex gap-3">
            <svg className="w-5 h-5 text-jobblogg-success flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div className="space-y-2">
              <BodyText className="font-medium text-jobblogg-success">
                Brukeren vil få tilbake:
              </BodyText>
              <ul className="space-y-1 text-sm text-jobblogg-text-medium">
                <li>• Full innloggingstilgang</li>
                <li>• Tilgang til alle tildelte prosjekter</li>
                <li>• Mulighet til å sende meldinger</li>
                <li>• Alle team-funksjoner</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Error display */}
        {error && (
          <div className={`rounded-lg p-4 ${
            isUpgradeError
              ? 'bg-jobblogg-warning/10 border border-jobblogg-warning/20'
              : 'bg-jobblogg-error/10 border border-jobblogg-error/20'
          }`}>
            <div className="flex items-start gap-3">
              <svg
                className={`w-5 h-5 flex-shrink-0 mt-0.5 ${
                  isUpgradeError ? 'text-jobblogg-warning' : 'text-jobblogg-error'
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isUpgradeError ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                )}
              </svg>
              <div className="flex-1">
                <BodyText className={`font-medium text-sm ${
                  isUpgradeError ? 'text-jobblogg-warning' : 'text-jobblogg-error'
                }`}>
                  {isUpgradeError ? 'Oppgradering kreves' : 'Feil'}
                </BodyText>
                <TextMuted className="text-sm mt-1">
                  {error}
                </TextMuted>
                {isUpgradeError && (
                  <div className="mt-3">
                    <SecondaryButton
                      onClick={() => window.open('/subscription', '_blank')}
                      className="text-xs px-3 py-1.5"
                    >
                      Se abonnementsplaner
                    </SecondaryButton>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
          <SecondaryButton onClick={handleClose} disabled={isUnblocking}>
            Avbryt
          </SecondaryButton>

          <PrimaryButton
            onClick={handleUnblock}
            loading={isUnblocking}
            disabled={isUnblocking}
          >
            Åpne bruker
          </PrimaryButton>
        </div>
      </div>
    </Modal>
  );
};
