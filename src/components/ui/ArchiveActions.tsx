import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { ConfirmDialog } from './Dialog/ConfirmDialog';

interface ArchiveActionsProps {
  projectId: string;
  isArchived?: boolean;
  onArchiveComplete?: () => void;
  onRestoreComplete?: () => void;
  className?: string;
  variant?: 'button' | 'menu-item';
}

export const ArchiveActions: React.FC<ArchiveActionsProps> = ({
  projectId,
  isArchived = false,
  onArchiveComplete,
  onRestoreComplete,
  className = '',
  variant = 'button'
}) => {
  const { user } = useUser();
  const [showArchiveDialog, setShowArchiveDialog] = useState(false);
  const [showRestoreDialog, setShowRestoreDialog] = useState(false);
  const [isArchiving, setIsArchiving] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  const archiveProject = useMutation(api.projects.archiveProject);
  const restoreProject = useMutation(api.projects.restoreProject);

  const handleArchive = async () => {
    if (!user?.id) return;

    console.log('🗂️ ArchiveActions - handleArchive called with:', { projectId, userId: user.id });

    if (!projectId) {
      console.error('❌ ArchiveActions - No projectId provided!');
      return;
    }

    setIsArchiving(true);
    try {
      console.log('📤 ArchiveActions - Calling archiveProject mutation...');
      await archiveProject({
        projectId: projectId as any,
        userId: user.id
      });

      setShowArchiveDialog(false);
      setIsArchiving(false);
      onArchiveComplete?.();
    } catch (error) {
      console.error('Feil ved arkivering av prosjekt:', error);
      setIsArchiving(false);
      setShowArchiveDialog(false);
    }
  };

  const handleRestore = async () => {
    if (!user?.id) return;

    console.log('🔄 ArchiveActions - handleRestore called with:', { projectId, userId: user.id });

    if (!projectId) {
      console.error('❌ ArchiveActions - No projectId provided for restore!');
      return;
    }

    setIsRestoring(true);
    try {
      console.log('📤 ArchiveActions - Calling restoreProject mutation...');
      await restoreProject({
        projectId: projectId as any,
        userId: user.id
      });

      setShowRestoreDialog(false);
      setIsRestoring(false);
      onRestoreComplete?.();
    } catch (error) {
      console.error('Feil ved gjenåpning av prosjekt:', error);
      setIsRestoring(false);
      setShowRestoreDialog(false);
    }
  };

  const buttonBaseClasses = "flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2";
  const menuItemBaseClasses = "flex items-center gap-2 px-3 py-2 text-sm hover:bg-jobblogg-neutral/50 transition-colors duration-200";

  if (isArchived) {
    // Show restore button for archived projects
    return (
      <>
        <button
          onClick={() => setShowRestoreDialog(true)}
          className={`
            ${variant === 'button' ? buttonBaseClasses : menuItemBaseClasses}
            ${variant === 'button' ? 'text-jobblogg-success hover:text-white border border-jobblogg-success hover:bg-jobblogg-success focus:ring-jobblogg-success/30' : 'text-jobblogg-success hover:text-jobblogg-success'}
            ${className}
          `}
          aria-label="Gjenåpne prosjekt"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Gjenåpne prosjekt
        </button>

        <ConfirmDialog
          isOpen={showRestoreDialog}
          title="Gjenåpne prosjekt"
          message="Er du sikker på at du vil gjenåpne dette prosjektet? Prosjektet vil bli flyttet tilbake til aktive prosjekter og vises i hovedoversikten."
          confirmText="Gjenåpne prosjekt"
          cancelText="Avbryt"
          isDestructive={false}
          isLoading={isRestoring}
          onConfirm={handleRestore}
          onCancel={() => setShowRestoreDialog(false)}
        />
      </>
    );
  }

  // Show archive button for active projects
  return (
    <>
      <button
        onClick={() => setShowArchiveDialog(true)}
        className={`
          ${variant === 'button' ? buttonBaseClasses : menuItemBaseClasses}
          ${variant === 'button' ? 'text-jobblogg-warning hover:text-white border border-jobblogg-warning hover:bg-jobblogg-warning focus:ring-jobblogg-warning/30' : 'text-jobblogg-warning hover:text-jobblogg-warning'}
          ${className}
        `}
        aria-label="Arkiver prosjekt"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H7a2 2 0 01-2-2V8z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6" />
        </svg>
        Arkiver prosjekt
      </button>

      <ConfirmDialog
        isOpen={showArchiveDialog}
        title="Arkiver prosjekt"
        message="Er du sikker på at du vil arkivere dette prosjektet? Prosjektet vil bli flyttet til arkivet og ikke lenger vises i aktive prosjekter. Alle data bevares og prosjektet kan gjenåpnes senere."
        confirmText="Arkiver prosjekt"
        cancelText="Avbryt"
        isDestructive={false}
        isLoading={isArchiving}
        onConfirm={handleArchive}
        onCancel={() => setShowArchiveDialog(false)}
      />
    </>
  );
};
