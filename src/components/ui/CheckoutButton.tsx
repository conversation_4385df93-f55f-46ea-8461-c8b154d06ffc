/**
 * Enhanced Checkout Button Component
 * 
 * A specialized button component for checkout flows with:
 * - Loading states with spinners
 * - Error handling and retry functionality
 * - Norwegian localization
 * - JobbLogg design system integration
 * - Accessibility features
 */

import React from 'react';
import { PrimaryButton, SecondaryButton } from './Button';

export interface CheckoutButtonProps {
  /** Button text when not loading */
  children: React.ReactNode;
  /** Loading text override */
  loadingText?: string;
  /** Whether the button is in loading state */
  isLoading?: boolean;
  /** Whether the button is in retry state */
  isRetrying?: boolean;
  /** Whether the button is disabled */
  disabled?: boolean;
  /** Click handler */
  onClick?: () => void;
  /** Button variant */
  variant?: 'primary' | 'secondary';
  /** Custom CSS classes */
  className?: string;
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Show loading spinner */
  showSpinner?: boolean;
  /** Full width button */
  fullWidth?: boolean;
}

/**
 * Loading spinner component
 */
const LoadingSpinner: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'md' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  return (
    <svg
      className={`animate-spin ${sizeClasses[size]}`}
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

/**
 * Enhanced checkout button with loading states and error handling
 */
export const CheckoutButton: React.FC<CheckoutButtonProps> = ({
  children,
  loadingText,
  isLoading = false,
  isRetrying = false,
  disabled = false,
  onClick,
  variant = 'primary',
  className = '',
  size = 'md',
  showSpinner = true,
  fullWidth = false,
}) => {
  // Determine button state
  const isProcessing = isLoading || isRetrying;
  const isDisabled = disabled || isProcessing;

  // Get appropriate text
  const getButtonText = () => {
    if (isRetrying) return 'Prøver igjen...';
    if (isLoading && loadingText) return loadingText;
    if (isLoading) return 'Laster...';
    return children;
  };

  // Get button component
  const ButtonComponent = variant === 'primary' ? PrimaryButton : SecondaryButton;

  // Additional classes for loading state
  const loadingClasses = isProcessing ? 'cursor-not-allowed opacity-90' : '';
  const widthClasses = fullWidth ? 'w-full' : '';

  return (
    <ButtonComponent
      onClick={onClick}
      disabled={isDisabled}
      className={`
        ${loadingClasses}
        ${widthClasses}
        ${className}
        transition-all duration-200
        flex items-center justify-center gap-2
      `.trim()}
      size={size}
    >
      {/* Loading Spinner */}
      {isProcessing && showSpinner && (
        <LoadingSpinner size={size} />
      )}
      
      {/* Button Text */}
      <span>{getButtonText()}</span>
    </ButtonComponent>
  );
};

/**
 * Checkout Error Display Component
 */
export interface CheckoutErrorProps {
  /** Error message */
  error?: string | null;
  /** Norwegian error message */
  norwegianError?: string | null;
  /** Whether retry is available */
  canRetry?: boolean;
  /** Retry handler */
  onRetry?: () => void;
  /** Clear error handler */
  onClear?: () => void;
  /** Show retry count */
  retryCount?: number;
  /** Maximum retry attempts */
  maxRetries?: number;
  /** Custom CSS classes */
  className?: string;
}

export const CheckoutError: React.FC<CheckoutErrorProps> = ({
  error,
  norwegianError,
  canRetry = false,
  onRetry,
  onClear,
  retryCount = 0,
  maxRetries = 3,
  className = '',
}) => {
  if (!error && !norwegianError) return null;

  const displayError = norwegianError || error;

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start gap-3">
        {/* Error Icon */}
        <div className="flex-shrink-0">
          <svg className="w-5 h-5 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>

        {/* Error Content */}
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium text-red-800 mb-1">
            Betalingsfeil
          </h3>
          <p className="text-sm text-red-700 mb-3">
            {displayError}
          </p>

          {/* Retry Information */}
          {retryCount > 0 && (
            <p className="text-xs text-red-600 mb-3">
              Forsøk {retryCount} av {maxRetries}
            </p>
          )}

          {/* Actions */}
          <div className="flex flex-wrap gap-2">
            {canRetry && onRetry && (
              <button
                onClick={onRetry}
                className="inline-flex items-center gap-1 px-3 py-1.5 bg-red-100 text-red-700 text-sm font-medium rounded-md hover:bg-red-200 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Prøv igjen
              </button>
            )}
            
            {onClear && (
              <button
                onClick={onClear}
                className="inline-flex items-center gap-1 px-3 py-1.5 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Lukk
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

/**
 * Checkout Loading Overlay Component
 */
export interface CheckoutLoadingProps {
  /** Whether to show the loading overlay */
  isVisible?: boolean;
  /** Loading message */
  message?: string;
  /** Whether this is a retry attempt */
  isRetrying?: boolean;
  /** Custom CSS classes */
  className?: string;
}

export const CheckoutLoading: React.FC<CheckoutLoadingProps> = ({
  isVisible = false,
  message = 'Starter betaling...',
  isRetrying = false,
  className = '',
}) => {
  if (!isVisible) return null;

  const displayMessage = isRetrying ? 'Prøver igjen...' : message;

  return (
    <div className={`fixed inset-0 bg-black/50 flex items-center justify-center z-50 ${className}`}>
      <div className="bg-white rounded-xl p-6 shadow-xl max-w-sm mx-4">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <h3 className="mt-4 text-lg font-semibold text-jobblogg-text-primary">
            {displayMessage}
          </h3>
          <p className="mt-2 text-sm text-jobblogg-text-muted">
            Du blir omdirigert til Stripe for sikker betaling...
          </p>
        </div>
      </div>
    </div>
  );
};
