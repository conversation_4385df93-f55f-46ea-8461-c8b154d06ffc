import React, { createContext, useContext, useState, useCallback } from 'react';

interface SubscriptionRefreshContextType {
  refreshKey: number;
  forceRefresh: () => void;
}

const SubscriptionRefreshContext = createContext<SubscriptionRefreshContextType | undefined>(undefined);

export const SubscriptionRefreshProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [refreshKey, setRefreshKey] = useState(0);

  const forceRefresh = useCallback(() => {
    console.log('🔄 Forcing subscription data refresh');
    setRefreshKey(prev => prev + 1);
  }, []);

  return (
    <SubscriptionRefreshContext.Provider value={{ refreshKey, forceRefresh }}>
      {children}
    </SubscriptionRefreshContext.Provider>
  );
};

export const useSubscriptionRefresh = () => {
  const context = useContext(SubscriptionRefreshContext);
  if (context === undefined) {
    throw new Error('useSubscriptionRefresh must be used within a SubscriptionRefreshProvider');
  }
  return context;
};
