/**
 * Checkout Cancel Test Component
 * 
 * Test component for simulating different checkout cancellation scenarios
 * and testing the contextual messaging and user experience.
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const CheckoutCancelTest: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState<string>('');

  const testScenarios = [
    {
      id: 'trial-setup-cancel',
      name: 'Trial Setup Cancellation',
      description: 'User cancels during initial trial setup',
      params: '?plan=basic&billing=month&context=trial_setup',
      expectedBehavior: 'Should show trial setup cancellation messaging with option to retry',
      urgency: 'low'
    },
    {
      id: 'trial-conversion-cancel',
      name: 'Trial Conversion Cancellation',
      description: 'User cancels trial-to-paid conversion',
      params: '?plan=professional&billing=year&context=trial_conversion',
      expectedBehavior: 'Should show trial conversion messaging with urgency based on days left',
      urgency: 'medium'
    },
    {
      id: 'expired-trial-cancel',
      name: 'Expired Trial Cancellation',
      description: 'User cancels after trial has expired',
      params: '?plan=basic&billing=month&context=expired_trial',
      expectedBehavior: 'Should show high urgency messaging requiring immediate action',
      urgency: 'high'
    },
    {
      id: 'plan-upgrade-cancel',
      name: 'Plan Upgrade Cancellation',
      description: 'Existing customer cancels plan upgrade',
      params: '?plan=enterprise&billing=year&context=plan_upgrade',
      expectedBehavior: 'Should show plan upgrade cancellation with current plan continuation',
      urgency: 'low'
    },
    {
      id: 'no-context-cancel',
      name: 'Generic Cancellation',
      description: 'Cancellation without specific context',
      params: '',
      expectedBehavior: 'Should show generic cancellation messaging with basic options',
      urgency: 'medium'
    },
  ];

  const generateTestUrl = (params: string) => {
    return `/checkout/cancel${params}`;
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
        <h1 className="text-2xl font-bold text-jobblogg-text-primary mb-6">
          🚫 Checkout Cancel Test
        </h1>

        <div className="mb-6">
          <p className="text-jobblogg-text-medium mb-4">
            Test different checkout cancellation scenarios to verify contextual messaging,
            user experience, and recovery options.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 mb-2">Testing Notes</h3>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• The cancel page detects context based on subscription status and URL parameters</li>
              <li>• Different scenarios show different messaging and urgency levels</li>
              <li>• Retry options are contextual based on the cancellation type</li>
              <li>• All messaging is localized in Norwegian</li>
            </ul>
          </div>
        </div>

        {/* Test Scenarios */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-jobblogg-text-primary mb-3">
            Test Scenarios
          </h2>

          <div className="grid gap-4">
            {testScenarios.map((scenario) => (
              <div
                key={scenario.id}
                className={`border rounded-lg p-4 transition-colors ${
                  selectedScenario === scenario.id
                    ? 'border-jobblogg-primary bg-jobblogg-primary-soft'
                    : 'border-jobblogg-border hover:border-jobblogg-primary'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-jobblogg-text-primary">
                        {scenario.name}
                      </h3>
                      <span className={`px-2 py-1 rounded text-xs font-medium border ${getUrgencyColor(scenario.urgency)}`}>
                        {scenario.urgency} urgency
                      </span>
                    </div>
                    <p className="text-jobblogg-text-medium text-sm mb-2">
                      {scenario.description}
                    </p>
                    <p className="text-jobblogg-text-muted text-xs mb-3">
                      <strong>Expected:</strong> {scenario.expectedBehavior}
                    </p>
                    {scenario.params && (
                      <div className="bg-gray-50 rounded p-2 mb-3">
                        <span className="text-xs text-gray-600">URL Parameters: </span>
                        <code className="text-xs font-mono text-gray-800">
                          {scenario.params}
                        </code>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col gap-2 ml-4">
                    <button
                      onClick={() => setSelectedScenario(
                        selectedScenario === scenario.id ? '' : scenario.id
                      )}
                      className="px-3 py-1 bg-jobblogg-primary text-white text-sm rounded hover:bg-jobblogg-primary-dark transition-colors"
                    >
                      {selectedScenario === scenario.id ? 'Deselect' : 'Select'}
                    </button>
                    <Link
                      to={generateTestUrl(scenario.params)}
                      className="px-3 py-1 bg-jobblogg-success text-white text-sm rounded hover:bg-jobblogg-success-dark transition-colors text-center"
                    >
                      Test
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Selected Scenario Details */}
        {selectedScenario && (
          <div className="mt-6 p-4 bg-jobblogg-surface rounded-lg">
            <h3 className="font-semibold text-jobblogg-text-primary mb-2">
              Selected Scenario Details
            </h3>
            {(() => {
              const scenario = testScenarios.find(s => s.id === selectedScenario);
              if (!scenario) return null;

              return (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-jobblogg-text-medium">Name:</span>
                    <span className="font-medium">{scenario.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-jobblogg-text-medium">Description:</span>
                    <span className="font-medium max-w-xs text-right">{scenario.description}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-jobblogg-text-medium">Test URL:</span>
                    <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                      {generateTestUrl(scenario.params)}
                    </code>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-jobblogg-text-medium">Urgency:</span>
                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getUrgencyColor(scenario.urgency)}`}>
                      {scenario.urgency}
                    </span>
                  </div>
                  <div className="flex justify-between items-start">
                    <span className="text-jobblogg-text-medium">Expected Behavior:</span>
                    <span className="text-sm text-jobblogg-text-muted max-w-xs text-right">
                      {scenario.expectedBehavior}
                    </span>
                  </div>
                </div>
              );
            })()}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-6 pt-6 border-t border-jobblogg-border">
          <h3 className="font-semibold text-jobblogg-text-primary mb-3">
            Quick Actions
          </h3>
          <div className="flex flex-wrap gap-3">
            <Link
              to="/checkout/cancel"
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Basic Cancel
            </Link>
            <Link
              to="/checkout/cancel?plan=basic&billing=month"
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Basic Monthly Cancel
            </Link>
            <Link
              to="/checkout/cancel?plan=professional&billing=year"
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
            >
              Professional Yearly Cancel
            </Link>
            <Link
              to="/checkout/success?session_id=cs_test_example"
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
            >
              Test Success Page
            </Link>
          </div>
        </div>

        {/* Feature Testing Checklist */}
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">Feature Testing Checklist</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <label className="flex items-center gap-2 text-yellow-700 text-sm">
              <input type="checkbox" className="rounded" />
              Context detection works correctly
            </label>
            <label className="flex items-center gap-2 text-yellow-700 text-sm">
              <input type="checkbox" className="rounded" />
              Urgency levels display properly
            </label>
            <label className="flex items-center gap-2 text-yellow-700 text-sm">
              <input type="checkbox" className="rounded" />
              Retry payment functionality
            </label>
            <label className="flex items-center gap-2 text-yellow-700 text-sm">
              <input type="checkbox" className="rounded" />
              Plan selection works
            </label>
            <label className="flex items-center gap-2 text-yellow-700 text-sm">
              <input type="checkbox" className="rounded" />
              Norwegian localization
            </label>
            <label className="flex items-center gap-2 text-yellow-700 text-sm">
              <input type="checkbox" className="rounded" />
              Navigation options work
            </label>
            <label className="flex items-center gap-2 text-yellow-700 text-sm">
              <input type="checkbox" className="rounded" />
              FAQ section expands
            </label>
            <label className="flex items-center gap-2 text-yellow-700 text-sm">
              <input type="checkbox" className="rounded" />
              Support links work
            </label>
          </div>
        </div>

        {/* Back to Main */}
        <div className="mt-6 text-center">
          <Link
            to="/"
            className="inline-flex items-center gap-2 px-4 py-2 bg-jobblogg-primary text-white rounded hover:bg-jobblogg-primary-dark transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Tilbake til dashbordet
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CheckoutCancelTest;
