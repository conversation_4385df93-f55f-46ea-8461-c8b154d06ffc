/**
 * Checkout Success Test Component
 * 
 * Test component for simulating different checkout success scenarios
 * without requiring actual Stripe sessions.
 */

import React, { useState } from 'react';
import { Link } from 'react-router-dom';

const CheckoutSuccessTest: React.FC = () => {
  const [selectedScenario, setSelectedScenario] = useState<string>('');

  const testScenarios = [
    {
      id: 'valid-trial-conversion',
      name: 'Valid Trial Conversion',
      description: 'Successful trial-to-paid conversion',
      sessionId: 'cs_test_trial_conversion_123',
      note: 'This will show an error since we don\'t have real session data'
    },
    {
      id: 'valid-new-subscription',
      name: 'Valid New Subscription',
      description: 'New subscription purchase',
      sessionId: 'cs_test_new_subscription_456',
      note: 'This will show an error since we don\'t have real session data'
    },
    {
      id: 'invalid-session-id',
      name: 'Invalid Session ID',
      description: 'Invalid session ID format',
      sessionId: 'invalid_session_123',
      note: 'Should show format validation error'
    },
    {
      id: 'missing-session-id',
      name: 'Missing Session ID',
      description: 'No session ID in URL',
      sessionId: '',
      note: 'Should show missing session ID error'
    },
  ];

  const generateTestUrl = (sessionId: string) => {
    const baseUrl = '/checkout/success';
    return sessionId ? `${baseUrl}?session_id=${sessionId}` : baseUrl;
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
        <h1 className="text-2xl font-bold text-jobblogg-text-primary mb-6">
          🧪 Checkout Success Test
        </h1>

        <div className="mb-6">
          <p className="text-jobblogg-text-medium mb-4">
            Test different checkout success scenarios to verify error handling and UI behavior.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 mb-2">Note</h3>
            <p className="text-blue-700 text-sm">
              Since we don't have real Stripe session data in development, most scenarios will show 
              appropriate error messages. This is expected behavior and demonstrates proper error handling.
            </p>
          </div>
        </div>

        {/* Test Scenarios */}
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-jobblogg-text-primary mb-3">
            Test Scenarios
          </h2>

          <div className="grid gap-4">
            {testScenarios.map((scenario) => (
              <div
                key={scenario.id}
                className={`border rounded-lg p-4 transition-colors ${
                  selectedScenario === scenario.id
                    ? 'border-jobblogg-primary bg-jobblogg-primary-soft'
                    : 'border-jobblogg-border hover:border-jobblogg-primary'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold text-jobblogg-text-primary mb-1">
                      {scenario.name}
                    </h3>
                    <p className="text-jobblogg-text-medium text-sm mb-2">
                      {scenario.description}
                    </p>
                    <p className="text-jobblogg-text-muted text-xs mb-3">
                      {scenario.note}
                    </p>
                    {scenario.sessionId && (
                      <div className="bg-gray-50 rounded p-2 mb-3">
                        <span className="text-xs text-gray-600">Session ID: </span>
                        <code className="text-xs font-mono text-gray-800">
                          {scenario.sessionId}
                        </code>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col gap-2 ml-4">
                    <button
                      onClick={() => setSelectedScenario(
                        selectedScenario === scenario.id ? '' : scenario.id
                      )}
                      className="px-3 py-1 bg-jobblogg-primary text-white text-sm rounded hover:bg-jobblogg-primary-dark transition-colors"
                    >
                      {selectedScenario === scenario.id ? 'Deselect' : 'Select'}
                    </button>
                    <Link
                      to={generateTestUrl(scenario.sessionId)}
                      className="px-3 py-1 bg-jobblogg-success text-white text-sm rounded hover:bg-jobblogg-success-dark transition-colors text-center"
                    >
                      Test
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Selected Scenario Details */}
        {selectedScenario && (
          <div className="mt-6 p-4 bg-jobblogg-surface rounded-lg">
            <h3 className="font-semibold text-jobblogg-text-primary mb-2">
              Selected Scenario Details
            </h3>
            {(() => {
              const scenario = testScenarios.find(s => s.id === selectedScenario);
              if (!scenario) return null;

              return (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-jobblogg-text-medium">Name:</span>
                    <span className="font-medium">{scenario.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-jobblogg-text-medium">Description:</span>
                    <span className="font-medium">{scenario.description}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-jobblogg-text-medium">Test URL:</span>
                    <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                      {generateTestUrl(scenario.sessionId)}
                    </code>
                  </div>
                  <div className="flex justify-between items-start">
                    <span className="text-jobblogg-text-medium">Expected Result:</span>
                    <span className="text-sm text-jobblogg-text-muted max-w-xs text-right">
                      {scenario.note}
                    </span>
                  </div>
                </div>
              );
            })()}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-6 pt-6 border-t border-jobblogg-border">
          <h3 className="font-semibold text-jobblogg-text-primary mb-3">
            Quick Actions
          </h3>
          <div className="flex flex-wrap gap-3">
            <Link
              to="/checkout/success"
              className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
            >
              Test Missing Session ID
            </Link>
            <Link
              to="/checkout/success?session_id=invalid_format"
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
            >
              Test Invalid Format
            </Link>
            <Link
              to="/checkout/success?session_id=cs_test_valid_format_123"
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Test Valid Format (No Data)
            </Link>
            <Link
              to="/checkout/cancel"
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Test Cancel Page
            </Link>
          </div>
        </div>

        {/* Development Notes */}
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">Development Notes</h3>
          <ul className="text-yellow-700 text-sm space-y-1">
            <li>• The checkout success page requires authentication</li>
            <li>• Session validation happens on both client and server side</li>
            <li>• Error handling covers missing, invalid, and unauthorized sessions</li>
            <li>• Success scenarios require real Stripe session data from webhooks</li>
            <li>• Norwegian localization is applied to all messages and formatting</li>
          </ul>
        </div>

        {/* Back to Main */}
        <div className="mt-6 text-center">
          <Link
            to="/"
            className="inline-flex items-center gap-2 px-4 py-2 bg-jobblogg-primary text-white rounded hover:bg-jobblogg-primary-dark transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Tilbake til dashbordet
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CheckoutSuccessTest;
