/**
 * Stripe Integration Test Component
 * 
 * This component tests the Stripe.js integration and displays
 * configuration information for debugging purposes.
 */

import React, { useState, useEffect } from 'react';
import { getStripe, StripeDevUtils, StripeError, StripeErrorType } from '../lib/stripe';
import { useCheckout, createPlanCheckoutParams, getPriceId } from '../hooks/useCheckout';
import { CheckoutButton, CheckoutError, CheckoutLoading } from '../components/ui/CheckoutButton';

const StripeTest: React.FC = () => {
  const [stripeStatus, setStripeStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [stripeInstance, setStripeInstance] = useState<any>(null);
  const [configInfo, setConfigInfo] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Checkout hook for testing
  const checkout = useCheckout();

  useEffect(() => {
    testStripeIntegration();
  }, []);

  const testStripeIntegration = async () => {
    try {
      setStripeStatus('loading');
      setError(null);

      // Get configuration info
      const config = StripeDevUtils.getConfigInfo();
      setConfigInfo(config);

      // Test Stripe initialization
      const stripe = await getStripe();
      
      if (stripe) {
        setStripeInstance(stripe);
        setStripeStatus('success');
        console.log('✅ Stripe.js loaded successfully:', stripe);
      } else {
        throw new Error('Stripe.js failed to load');
      }

    } catch (err) {
      console.error('❌ Stripe integration test failed:', err);
      setStripeStatus('error');
      
      if (err instanceof StripeError) {
        setError(`${err.norwegianMessage} (${err.message})`);
      } else {
        setError(err instanceof Error ? err.message : 'Unknown error');
      }
    }
  };

  const testConfigurationCheck = async () => {
    const isConfigured = await StripeDevUtils.checkConfiguration();
    alert(`Stripe Configuration Check: ${isConfigured ? 'PASSED ✅' : 'FAILED ❌'}`);
  };

  const testCheckout = async (planLevel: 'basic' | 'professional' | 'enterprise', billingInterval: 'month' | 'year') => {
    const params = createPlanCheckoutParams(planLevel, billingInterval, {
      trialDays: 7,
    });

    if (params.priceId) {
      await checkout.initiateCheckout({
        priceId: params.priceId,
        planLevel: params.planLevel!,
        billingInterval: params.billingInterval!,
        quantity: params.quantity,
        trialDays: params.trialDays,
        successUrl: params.successUrl,
        cancelUrl: params.cancelUrl,
        allowPromotionCodes: params.allowPromotionCodes,
        automaticTax: params.automaticTax,
      });
    }
  };

  return (
    <>
      {/* Checkout Loading Overlay */}
      <CheckoutLoading
        isVisible={checkout.state.isLoading || checkout.state.isRetrying}
        message="Testing checkout..."
        isRetrying={checkout.state.isRetrying}
      />

      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
        <h1 className="text-2xl font-bold text-jobblogg-text-primary mb-6">
          🔧 Stripe Integration Test
        </h1>

        {/* Status Indicator */}
        <div className="mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-4 h-4 rounded-full ${
              stripeStatus === 'loading' ? 'bg-yellow-500 animate-pulse' :
              stripeStatus === 'success' ? 'bg-green-500' :
              'bg-red-500'
            }`} />
            <span className="font-medium">
              Status: {
                stripeStatus === 'loading' ? 'Loading...' :
                stripeStatus === 'success' ? 'Connected ✅' :
                'Failed ❌'
              }
            </span>
          </div>
          
          {error && (
            <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}
        </div>

        {/* Configuration Info */}
        {configInfo && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-jobblogg-text-primary mb-3">
              Configuration
            </h2>
            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Environment:</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  configInfo.environment === 'development' 
                    ? 'bg-yellow-100 text-yellow-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {configInfo.environment}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Publishable Key:</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${
                  configInfo.hasPublishableKey 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {configInfo.hasPublishableKey ? 'Configured ✅' : 'Missing ❌'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Locale:</span>
                <span className="text-gray-600">{configInfo.locale}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Currency:</span>
                <span className="text-gray-600">{configInfo.currency.toUpperCase()}</span>
              </div>
            </div>
          </div>
        )}

        {/* Stripe Instance Info */}
        {stripeInstance && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-jobblogg-text-primary mb-3">
              Stripe Instance
            </h2>
            <div className="bg-gray-50 rounded-lg p-4 space-y-2">
              <div className="flex justify-between">
                <span className="font-medium">Version:</span>
                <span className="text-gray-600">{stripeInstance._version || 'Unknown'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Key Type:</span>
                <span className="text-gray-600">
                  {stripeInstance._keyMode === 'test' ? 'Test Key' : 'Live Key'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">API Version:</span>
                <span className="text-gray-600">{stripeInstance._apiVersion || 'Default'}</span>
              </div>
            </div>
          </div>
        )}

        {/* Checkout Error Display */}
        <CheckoutError
          error={checkout.state.error}
          norwegianError={checkout.state.norwegianError}
          canRetry={checkout.state.canRetry}
          onRetry={checkout.retryCheckout}
          onClear={checkout.clearError}
          retryCount={checkout.state.retryCount}
          maxRetries={3}
          className="mb-6"
        />

        {/* Test Actions */}
        <div className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold text-jobblogg-text-primary mb-3">
              Stripe Integration Tests
            </h2>

            <div className="flex flex-wrap gap-3">
              <button
                onClick={testStripeIntegration}
                className="px-4 py-2 bg-jobblogg-primary text-white rounded-lg hover:bg-jobblogg-primary-dark transition-colors"
              >
                🔄 Reload Stripe
              </button>

              <button
                onClick={testConfigurationCheck}
                className="px-4 py-2 bg-jobblogg-success text-white rounded-lg hover:bg-jobblogg-success-dark transition-colors"
              >
                ✅ Check Configuration
              </button>
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold text-jobblogg-text-primary mb-3">
              Checkout Tests
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Basic Plan */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">Basic Plan</h3>
                <p className="text-sm text-gray-600 mb-3">Price ID: {getPriceId('basic', 'month').slice(-8)}...</p>
                <div className="space-y-2">
                  <CheckoutButton
                    onClick={() => testCheckout('basic', 'month')}
                    isLoading={checkout.state.isLoading}
                    isRetrying={checkout.state.isRetrying}
                    loadingText="Testing..."
                    className="w-full px-3 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                    size="sm"
                    fullWidth
                  >
                    Test Monthly
                  </CheckoutButton>
                  <CheckoutButton
                    onClick={() => testCheckout('basic', 'year')}
                    isLoading={checkout.state.isLoading}
                    isRetrying={checkout.state.isRetrying}
                    loadingText="Testing..."
                    className="w-full px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
                    size="sm"
                    fullWidth
                  >
                    Test Yearly
                  </CheckoutButton>
                </div>
              </div>

              {/* Professional Plan */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">Professional Plan</h3>
                <p className="text-sm text-gray-600 mb-3">Price ID: {getPriceId('professional', 'month').slice(-8)}...</p>
                <div className="space-y-2">
                  <CheckoutButton
                    onClick={() => testCheckout('professional', 'month')}
                    isLoading={checkout.state.isLoading}
                    isRetrying={checkout.state.isRetrying}
                    loadingText="Testing..."
                    className="w-full px-3 py-2 bg-green-500 text-white rounded text-sm hover:bg-green-600 transition-colors"
                    size="sm"
                    fullWidth
                  >
                    Test Monthly
                  </CheckoutButton>
                  <CheckoutButton
                    onClick={() => testCheckout('professional', 'year')}
                    isLoading={checkout.state.isLoading}
                    isRetrying={checkout.state.isRetrying}
                    loadingText="Testing..."
                    className="w-full px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors"
                    size="sm"
                    fullWidth
                  >
                    Test Yearly
                  </CheckoutButton>
                </div>
              </div>

              {/* Enterprise Plan */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">Enterprise Plan</h3>
                <p className="text-sm text-gray-600 mb-3">Price ID: {getPriceId('enterprise', 'month').slice(-8)}...</p>
                <div className="space-y-2">
                  <CheckoutButton
                    onClick={() => testCheckout('enterprise', 'month')}
                    isLoading={checkout.state.isLoading}
                    isRetrying={checkout.state.isRetrying}
                    loadingText="Testing..."
                    className="w-full px-3 py-2 bg-purple-500 text-white rounded text-sm hover:bg-purple-600 transition-colors"
                    size="sm"
                    fullWidth
                  >
                    Test Monthly
                  </CheckoutButton>
                  <CheckoutButton
                    onClick={() => testCheckout('enterprise', 'year')}
                    isLoading={checkout.state.isLoading}
                    isRetrying={checkout.state.isRetrying}
                    loadingText="Testing..."
                    className="w-full px-3 py-2 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 transition-colors"
                    size="sm"
                    fullWidth
                  >
                    Test Yearly
                  </CheckoutButton>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Environment Variables Info */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">Environment Variables</h3>
          <p className="text-blue-700 text-sm">
            Make sure these environment variables are set in your <code>.env.local</code> file:
          </p>
          <ul className="mt-2 text-blue-700 text-sm space-y-1">
            <li><code>VITE_STRIPE_PUBLISHABLE_KEY</code> - Your Stripe publishable key</li>
            <li><code>VITE_STRIPE_PUBLISHABLE_KEY_LIVE</code> - Your live publishable key (production)</li>
          </ul>
        </div>
      </div>
    </div>
    </>
  );
};

export default StripeTest;
