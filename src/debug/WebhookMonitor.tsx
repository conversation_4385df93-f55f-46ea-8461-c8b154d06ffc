/**
 * Webhook Monitor Component
 * 
 * Development tool for monitoring and testing Stripe webhook processing.
 * Provides real-time insights into subscription events and webhook handling.
 */

import React, { useState, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Link } from 'react-router-dom';

interface WebhookEvent {
  _id: string;
  subscriptionId: string;
  eventType: string;
  eventData: any;
  timestamp: number;
  source?: string;
  metadata?: any;
}

interface AnalyticsData {
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsByDay: Record<string, number>;
  subscriptionMetrics: {
    created: number;
    canceled: number;
    reactivated: number;
    trialConversions: number;
    paymentSuccesses: number;
    paymentFailures: number;
  };
  recentEvents: Array<{
    eventType: string;
    timestamp: number;
    subscriptionId: string;
  }>;
}

const WebhookMonitor: React.FC = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<number>(7); // days
  const [selectedEventType, setSelectedEventType] = useState<string>('');
  const [selectedSubscription, setSelectedSubscription] = useState<string>('');

  // Query subscription events
  const events = useQuery(api.subscriptions.getSubscriptionEvents, {
    eventType: selectedEventType || undefined,
    subscriptionId: selectedSubscription || undefined,
    limit: 50,
    startTime: Date.now() - (selectedTimeRange * 24 * 60 * 60 * 1000),
  }) as WebhookEvent[] | undefined;

  // Query analytics
  const analytics = useQuery(api.subscriptions.getSubscriptionAnalytics, {
    days: selectedTimeRange,
  }) as AnalyticsData | undefined;

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('nb-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // Get event type color
  const getEventTypeColor = (eventType: string) => {
    const colors = {
      subscription_created: 'text-green-600 bg-green-50',
      subscription_canceled: 'text-red-600 bg-red-50',
      subscription_updated: 'text-blue-600 bg-blue-50',
      trial_converted: 'text-purple-600 bg-purple-50',
      payment_succeeded: 'text-green-600 bg-green-50',
      payment_failed: 'text-red-600 bg-red-50',
      webhook_processed: 'text-gray-600 bg-gray-50',
    };
    return colors[eventType as keyof typeof colors] || 'text-gray-600 bg-gray-50';
  };

  // Get source icon
  const getSourceIcon = (source?: string) => {
    switch (source) {
      case 'stripe_webhook':
        return '🔗';
      case 'user_action':
        return '👤';
      case 'system_action':
        return '⚙️';
      case 'admin_action':
        return '👨‍💼';
      default:
        return '❓';
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
        <h1 className="text-2xl font-bold text-jobblogg-text-primary mb-6">
          🔍 Webhook Monitor
        </h1>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-jobblogg-text-primary mb-2">
              Tidsperiode
            </label>
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(Number(e.target.value))}
              className="w-full px-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-transparent"
            >
              <option value={1}>Siste 24 timer</option>
              <option value={7}>Siste 7 dager</option>
              <option value={30}>Siste 30 dager</option>
              <option value={90}>Siste 90 dager</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-jobblogg-text-primary mb-2">
              Event Type
            </label>
            <select
              value={selectedEventType}
              onChange={(e) => setSelectedEventType(e.target.value)}
              className="w-full px-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-transparent"
            >
              <option value="">Alle typer</option>
              <option value="subscription_created">Subscription Created</option>
              <option value="subscription_updated">Subscription Updated</option>
              <option value="subscription_canceled">Subscription Canceled</option>
              <option value="trial_converted">Trial Converted</option>
              <option value="payment_succeeded">Payment Succeeded</option>
              <option value="payment_failed">Payment Failed</option>
              <option value="webhook_processed">Webhook Processed</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-jobblogg-text-primary mb-2">
              Subscription ID
            </label>
            <input
              type="text"
              value={selectedSubscription}
              onChange={(e) => setSelectedSubscription(e.target.value)}
              placeholder="sub_xxx eller la stå tom"
              className="w-full px-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Analytics Summary */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-jobblogg-surface rounded-lg p-4">
              <h3 className="text-sm font-medium text-jobblogg-text-medium mb-1">Total Events</h3>
              <p className="text-2xl font-bold text-jobblogg-text-primary">{analytics.totalEvents}</p>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-green-700 mb-1">Successful Payments</h3>
              <p className="text-2xl font-bold text-green-800">{analytics.subscriptionMetrics.paymentSuccesses}</p>
            </div>
            <div className="bg-red-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-red-700 mb-1">Failed Payments</h3>
              <p className="text-2xl font-bold text-red-800">{analytics.subscriptionMetrics.paymentFailures}</p>
            </div>
            <div className="bg-purple-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-purple-700 mb-1">Trial Conversions</h3>
              <p className="text-2xl font-bold text-purple-800">{analytics.subscriptionMetrics.trialConversions}</p>
            </div>
          </div>
        )}

        {/* Event Types Breakdown */}
        {analytics && Object.keys(analytics.eventsByType).length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-jobblogg-text-primary mb-3">
              Events by Type
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {Object.entries(analytics.eventsByType).map(([eventType, count]) => (
                <div
                  key={eventType}
                  className={`px-3 py-2 rounded-lg text-sm font-medium ${getEventTypeColor(eventType)}`}
                >
                  <div className="font-semibold">{eventType.replace(/_/g, ' ')}</div>
                  <div className="text-lg font-bold">{count}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recent Events */}
        <div>
          <h3 className="text-lg font-semibold text-jobblogg-text-primary mb-3">
            Recent Events ({events?.length || 0})
          </h3>
          
          {events && events.length > 0 ? (
            <div className="space-y-2">
              {events.map((event) => (
                <div
                  key={event._id}
                  className="border border-jobblogg-border rounded-lg p-4 hover:bg-jobblogg-surface transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-lg">{getSourceIcon(event.source)}</span>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getEventTypeColor(event.eventType)}`}>
                          {event.eventType.replace(/_/g, ' ')}
                        </span>
                        <span className="text-xs text-jobblogg-text-muted">
                          {formatTimestamp(event.timestamp)}
                        </span>
                      </div>
                      
                      <div className="text-sm text-jobblogg-text-medium mb-2">
                        <strong>Subscription:</strong> {event.subscriptionId}
                      </div>
                      
                      {event.metadata && (
                        <div className="text-xs text-jobblogg-text-muted">
                          <strong>Metadata:</strong> {JSON.stringify(event.metadata, null, 2).slice(0, 100)}...
                        </div>
                      )}
                    </div>
                    
                    <details className="ml-4">
                      <summary className="cursor-pointer text-xs text-jobblogg-primary hover:underline">
                        View Data
                      </summary>
                      <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-w-md">
                        {JSON.stringify(event.eventData, null, 2)}
                      </pre>
                    </details>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-jobblogg-text-muted">
              <p>Ingen events funnet for valgte filtre</p>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="mt-6 pt-6 border-t border-jobblogg-border">
          <h3 className="font-semibold text-jobblogg-text-primary mb-3">
            Quick Actions
          </h3>
          <div className="flex flex-wrap gap-3">
            <Link
              to="/test-stripe"
              className="px-4 py-2 bg-jobblogg-primary text-white rounded hover:bg-jobblogg-primary-dark transition-colors"
            >
              Test Stripe Integration
            </Link>
            <Link
              to="/subscription"
              className="px-4 py-2 bg-jobblogg-success text-white rounded hover:bg-jobblogg-success-dark transition-colors"
            >
              Subscription Management
            </Link>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Refresh Data
            </button>
          </div>
        </div>

        {/* Back to Main */}
        <div className="mt-6 text-center">
          <Link
            to="/"
            className="inline-flex items-center gap-2 px-4 py-2 bg-jobblogg-primary text-white rounded hover:bg-jobblogg-primary-dark transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Tilbake til dashbordet
          </Link>
        </div>
      </div>
    </div>
  );
};

export default WebhookMonitor;
