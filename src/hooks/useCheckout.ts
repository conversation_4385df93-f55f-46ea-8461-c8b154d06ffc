/**
 * useCheckout Hook for JobbLogg Stripe Integration
 * 
 * This hook provides functionality for initiating Stripe checkout sessions
 * with proper error handling, loading states, and Norwegian localization.
 * 
 * Features:
 * - Stripe checkout session creation
 * - Automatic redirection to Stripe Checkout
 * - Loading states and error handling
 * - Norwegian error messages
 * - Trial-to-paid conversion support
 * - Plan upgrade/downgrade flows
 */

import { useAction } from 'convex/react';
import { useCallback, useState } from 'react';
import { api } from '../../convex/_generated/api';
import { determineCheckoutContext } from '../lib/checkout-utils';
import { StripeError, StripeErrorType, createStripeError, getStripe, validateStripe } from '../lib/stripe';

/**
 * Checkout session parameters
 */
export interface CheckoutParams {
  priceId: string;
  planLevel: 'basic' | 'professional' | 'enterprise';
  billingInterval: 'month' | 'year';
  quantity?: number;
  successUrl?: string;
  cancelUrl?: string;
  trialDays?: number;
  allowPromotionCodes?: boolean;
  automaticTax?: boolean;
}

/**
 * Checkout hook state
 */
export interface CheckoutState {
  isLoading: boolean;
  error: string | null;
  norwegianError: string | null;
  sessionId: string | null;
  retryCount: number;
  canRetry: boolean;
  isRetrying: boolean;
}

/**
 * Checkout hook return type
 */
export interface UseCheckoutReturn {
  state: CheckoutState;
  initiateCheckout: (params: CheckoutParams) => Promise<void>;
  retryCheckout: () => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

/**
 * Custom hook for Stripe checkout functionality
 */
export function useCheckout(): UseCheckoutReturn {
  const [state, setState] = useState<CheckoutState>({
    isLoading: false,
    error: null,
    norwegianError: null,
    sessionId: null,
    retryCount: 0,
    canRetry: false,
    isRetrying: false,
  });

  // Store last checkout params for retry functionality
  const [lastCheckoutParams, setLastCheckoutParams] = useState<CheckoutParams | null>(null);

  // Convex action for creating checkout sessions
  const createCheckoutSession = useAction(api.subscriptions.createEnhancedCheckoutSession);

  // Maximum retry attempts
  const MAX_RETRY_ATTEMPTS = 3;

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
      norwegianError: null,
    }));
  }, []);

  /**
   * Reset hook state
   */
  const reset = useCallback(() => {
    setState({
      isLoading: false,
      error: null,
      norwegianError: null,
      sessionId: null,
      retryCount: 0,
      canRetry: false,
      isRetrying: false,
    });
    setLastCheckoutParams(null);
  }, []);

  /**
   * Set error state with Norwegian translation and retry logic
   */
  const setError = useCallback((error: StripeError | Error | string, isRetryable: boolean = true) => {
    const canRetry = isRetryable && state.retryCount < MAX_RETRY_ATTEMPTS;

    if (error instanceof StripeError) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        isRetrying: false,
        error: error.message,
        norwegianError: error.norwegianMessage,
        canRetry,
      }));
    } else if (error instanceof Error) {
      // Determine if error is retryable based on error message
      const isNetworkError = error.message.toLowerCase().includes('network') ||
                            error.message.toLowerCase().includes('fetch') ||
                            error.message.toLowerCase().includes('timeout');

      const norwegianMessage = isNetworkError
        ? 'Nettverksfeil. Sjekk internettforbindelsen og prøv igjen.'
        : 'En feil oppstod under betalingsprosessen. Prøv igjen eller kontakt support.';

      setState(prev => ({
        ...prev,
        isLoading: false,
        isRetrying: false,
        error: error.message,
        norwegianError: norwegianMessage,
        canRetry: canRetry && isNetworkError, // Only retry network errors automatically
      }));
    } else {
      setState(prev => ({
        ...prev,
        isLoading: false,
        isRetrying: false,
        error: error,
        norwegianError: 'En ukjent feil oppstod. Prøv igjen eller kontakt support.',
        canRetry,
      }));
    }
  }, [state.retryCount, MAX_RETRY_ATTEMPTS]);

  /**
   * Retry last checkout attempt
   */
  const retryCheckout = useCallback(async () => {
    if (!lastCheckoutParams) {
      console.error('No previous checkout params to retry');
      return;
    }

    if (state.retryCount >= MAX_RETRY_ATTEMPTS) {
      console.error('Maximum retry attempts reached');
      return;
    }

    setState(prev => ({
      ...prev,
      isRetrying: true,
      error: null,
      norwegianError: null,
      retryCount: prev.retryCount + 1,
    }));

    console.log(`🔄 Retrying checkout (attempt ${state.retryCount + 1}/${MAX_RETRY_ATTEMPTS})`);

    // Add a small delay before retry
    await new Promise(resolve => setTimeout(resolve, 1000));

    await performCheckout(lastCheckoutParams, true);
  }, [lastCheckoutParams, state.retryCount, MAX_RETRY_ATTEMPTS]);

  /**
   * Perform the actual checkout process
   */
  const performCheckout = useCallback(async (params: CheckoutParams, isRetry: boolean = false) => {
    try {
      // Clear previous errors and set loading state
      setState(prev => ({
        ...prev,
        isLoading: true,
        isRetrying: isRetry,
        error: null,
        norwegianError: null,
        canRetry: false,
      }));

      const actionText = isRetry ? 'Retrying checkout' : 'Initiating checkout';
      console.log(`🔄 ${actionText} with params:`, params);

      // Validate required parameters
      if (!params.priceId) {
        throw createStripeError(StripeErrorType.CONFIGURATION_ERROR, new Error('Price ID is required'));
      }

      if (!params.planLevel) {
        throw createStripeError(StripeErrorType.CONFIGURATION_ERROR, new Error('Plan level is required'));
      }

      if (!params.billingInterval) {
        throw createStripeError(StripeErrorType.CONFIGURATION_ERROR, new Error('Billing interval is required'));
      }

      // Initialize Stripe
      const stripe = await getStripe();
      validateStripe(stripe);

      // Prepare checkout session parameters
      const checkoutParams = {
        priceId: params.priceId,
        planLevel: params.planLevel,
        billingInterval: params.billingInterval,
        quantity: params.quantity || 1,
        successUrl: params.successUrl || `${window.location.origin}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: params.cancelUrl || `${window.location.origin}/checkout/cancel`,
        trialDays: params.trialDays,
        allowPromotionCodes: params.allowPromotionCodes ?? true,
        automaticTax: params.automaticTax ?? true,
      };

      console.log('🔄 Creating checkout session with params:', checkoutParams);

      // Create checkout session via Convex
      const session = await createCheckoutSession(checkoutParams);

      if (!session || !session.sessionId) {
        throw createStripeError(
          StripeErrorType.CHECKOUT_FAILED,
          new Error('Failed to create checkout session')
        );
      }

      console.log('✅ Checkout session created:', session.sessionId);

      // Update state with session ID
      setState(prev => ({
        ...prev,
        sessionId: session.sessionId,
      }));

      // Redirect to Stripe Checkout
      console.log('🔄 Redirecting to Stripe Checkout...');
      const { error: redirectError } = await stripe.redirectToCheckout({
        sessionId: session.sessionId,
      });

      if (redirectError) {
        console.error('❌ Stripe redirect error:', redirectError);
        throw createStripeError(
          StripeErrorType.CHECKOUT_FAILED,
          new Error(`Checkout redirect failed: ${redirectError.message}`)
        );
      }

      // If we reach here, the redirect failed silently
      console.log('✅ Redirected to Stripe Checkout successfully');

    } catch (error) {
      const errorText = isRetry ? 'Checkout retry failed' : 'Checkout initiation failed';
      console.error(`❌ ${errorText}:`, error);

      // Handle different error types with enhanced Norwegian messages
      if (error instanceof StripeError) {
        setError(error, true);
      } else if (error instanceof Error) {
        // Check for specific error types and determine if retryable
        if (error.message.includes('network') || error.message.includes('fetch') || error.message.includes('timeout')) {
          setError(createStripeError(StripeErrorType.NETWORK_ERROR, error), true);
        } else if (error.message.includes('configuration') || error.message.includes('key')) {
          setError(createStripeError(StripeErrorType.CONFIGURATION_ERROR, error), false);
        } else if (error.message.includes('No subscription found')) {
          setError(createStripeError(
            StripeErrorType.CONFIGURATION_ERROR,
            new Error('Ingen aktiv abonnement funnet. Kontakt support for hjelp.')
          ), false);
        } else if (error.message.includes('Failed to create checkout session')) {
          setError(createStripeError(StripeErrorType.CHECKOUT_FAILED, error), true);
        } else {
          setError(createStripeError(StripeErrorType.CHECKOUT_FAILED, error), true);
        }
      } else {
        setError(createStripeError(
          StripeErrorType.CHECKOUT_FAILED,
          new Error('En ukjent feil oppstod under betalingsprosessen')
        ), true);
      }
    }
  }, [createCheckoutSession, setError]);

  /**
   * Initiate Stripe checkout process
   */
  const initiateCheckout = useCallback(async (params: CheckoutParams) => {
    // Store params for potential retry
    setLastCheckoutParams(params);

    // Reset retry count for new checkout attempt
    setState(prev => ({
      ...prev,
      retryCount: 0,
    }));

    await performCheckout(params, false);
  }, [createCheckoutSession, setError]);

  return {
    state,
    initiateCheckout,
    retryCheckout,
    clearError,
    reset,
  };
}

/**
 * Utility function to get default checkout URLs
 */
export function getDefaultCheckoutUrls() {
  const baseUrl = window.location.origin;
  return {
    successUrl: `${baseUrl}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
    cancelUrl: `${baseUrl}/checkout/cancel`,
  };
}

/**
 * Environment-aware Stripe price IDs
 * Automatically selects test or live mode prices based on environment
 */
function getStripePriceIds() {
  // Determine if we're in test mode based on the publishable key
  const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
  const isTestMode = !publishableKey || publishableKey.startsWith('pk_test_') || publishableKey.includes('your_publishable_key_here');

  if (isTestMode) {
    // Test mode price IDs - tax-exclusive pricing (VAT added on top)
    return {
      basic: {
        month: 'price_1S4dVtRqXwHRnsDwJ4PTZ9Vj', // 299 NOK/month + 25% VAT = 373.75 NOK
        year: 'price_1S4dW1RqXwHRnsDwFYZErPJP',  // 2870 NOK/year + 25% VAT = 3587.50 NOK (20% discount)
      },
      professional: {
        month: 'price_1S4dW9RqXwHRnsDw7lnAM9x7', // 999 NOK/month + 25% VAT = 1248.75 NOK
        year: 'price_1S4dWGRqXwHRnsDwfdzAZEA8',  // 9590 NOK/year + 25% VAT = 11987.50 NOK (20% discount)
      },
      enterprise: {
        month: 'price_1S4dWPRqXwHRnsDwPF9uQPqz', // 2999 NOK/month + 25% VAT = 3748.75 NOK
        year: 'price_1S4dWXRqXwHRnsDwd1SScrvA',  // 28790 NOK/year + 25% VAT = 35987.50 NOK (20% discount)
      },
    };
  } else {
    // Live mode price IDs - tax-exclusive pricing (VAT added on top)
    return {
      basic: {
        month: 'price_1S3XARRqXwHRnsDwWeHWR1uT', // 299 NOK/month + 25% VAT = 373.75 NOK
        year: 'price_1S3XASRqXwHRnsDwl4hu21hw',  // 2870 NOK/year + 25% VAT = 3587.50 NOK (20% discount) - NEEDS UPDATE
      },
      professional: {
        month: 'price_1S3XASRqXwHRnsDwwS56xVk1', // 999 NOK/month + 25% VAT = 1248.75 NOK
        year: 'price_1S3XASRqXwHRnsDw8tqj4oIS',  // 9590 NOK/year + 25% VAT = 11987.50 NOK (20% discount) - NEEDS UPDATE
      },
      enterprise: {
        month: 'price_1S3XATRqXwHRnsDwiB9ljlXl', // 2999 NOK/month + 25% VAT = 3748.75 NOK
        year: 'price_1S3XATRqXwHRnsDwKmlyOC1e',  // 28790 NOK/year + 25% VAT = 35987.50 NOK (20% discount) - NEEDS UPDATE
      },
    };
  }
}

/**
 * Utility function to create checkout parameters for plan selection
 */
export function createPlanCheckoutParams(
  planLevel: 'basic' | 'professional' | 'enterprise',
  billingInterval: 'month' | 'year',
  options: {
    trialDays?: number;
    quantity?: number;
    customUrls?: { successUrl?: string; cancelUrl?: string };
  } = {}
): Partial<CheckoutParams> {
  const priceIds = getStripePriceIds();
  const priceId = priceIds[planLevel][billingInterval];
  const defaultUrls = getDefaultCheckoutUrls();

  // Use utility function to determine context consistently
  // Note: We don't have subscription state here, so we rely on trialDays parameter
  const context = determineCheckoutContext(
    undefined, // subscriptionStatus not available here
    false, // isInTrial not available here
    false, // isTrialExpired not available here
    false, // hasStripeCustomer not available here
    options.trialDays
  );

  // Create cancel URL with context parameters
  const baseUrl = window.location.origin;
  const cancelUrl = options.customUrls?.cancelUrl ||
    `${baseUrl}/checkout/cancel?plan=${planLevel}&billing=${billingInterval}&context=${context}`;

  return {
    priceId,
    planLevel,
    billingInterval,
    quantity: options.quantity || 1,
    trialDays: options.trialDays,
    successUrl: options.customUrls?.successUrl || defaultUrls.successUrl,
    cancelUrl,
    allowPromotionCodes: true,
    automaticTax: true,
  };
}

/**
 * Get price ID for a specific plan and billing interval
 */
export function getPriceId(
  planLevel: 'basic' | 'professional' | 'enterprise',
  billingInterval: 'month' | 'year'
): string {
  return STRIPE_PRICE_IDS[planLevel][billingInterval];
}
