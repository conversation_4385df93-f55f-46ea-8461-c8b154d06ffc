import { useUser, useAuth } from '@clerk/clerk-react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

/**
 * Hook to get contractor onboarding status
 * Useful for components that need to conditionally render based on onboarding status
 */
export const useContractorOnboardingStatus = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();

  // Only query when both Clerk and Convex auth are ready
  const shouldQuery = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  const onboardingStatusResult = useQuery(
    api.contractorOnboarding.getOnboardingStatus,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: false, // !shouldQuery || onboardingStatusResult === undefined, // Disabled due to type issues
    exists: false, // onboardingStatusResult?.exists || false, // Disabled due to type issues
    contractorCompleted: false, // onboardingStatusResult?.contractorCompleted || false, // Disabled due to type issues
    contractorCompanyId: null, // onboardingStatusResult?.contractorCompanyId || null, // Disabled due to type issues
    authError: false, // onboardingStatusResult?.authError || false, // Disabled due to type issues
    error: null, // onboardingStatusResult?.error || null, // Disabled due to type issues
  };
};

/**
 * Hook to get contractor company information
 * Returns the contractor's company data if available
 */
export const useContractorCompany = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();

  // Only query when both Clerk and Convex auth are ready
  const shouldQuery = isClerkLoaded && isAuthLoaded && isSignedIn && user?.id;

  const contractorCompanyResult = useQuery(
    api.contractorCompany.getContractorCompanyWithDetails,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  return {
    isLoading: false, // !shouldQuery || contractorCompanyResult === undefined, // Disabled due to type issues
    company: null, // contractorCompanyResult?.company || null, // Disabled due to type issues
    authError: false, // contractorCompanyResult?.authError || false, // Disabled due to type issues
    error: null, // contractorCompanyResult?.error || null, // Disabled due to type issues
  };
};
