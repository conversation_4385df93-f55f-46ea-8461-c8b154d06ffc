import { useState, useEffect, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';
import { offlineStorage, OfflineProject } from '../utils/offlineStorage';
import { offlineImageStorage, OfflineImage, ImageUploadQueueItem } from '../utils/offlineImageStorage';
import { usePWA } from './usePWA';

/**
 * Hook for managing projects with offline support
 * Combines online projects from Convex with offline projects from local storage
 */
export function useOfflineProjects() {
  const { user } = useUser();
  const { isOnline, canAccessOfflineData } = usePWA();
  const [offlineProjects, setOfflineProjects] = useState<OfflineProject[]>([]);
  const [isOfflineInitialized, setIsOfflineInitialized] = useState(false);
  const [offlineError, setOfflineError] = useState<string | null>(null);

  // Query online projects
  const onlineProjects = useQuery(
    api.projects.getByUserWithCustomers,
    user?.id ? { userId: user.id } : "skip"
  );

  // Initialize offline storage when user is authenticated and has consent
  useEffect(() => {
    const initializeOfflineStorage = async () => {
      if (!user?.id || !canAccessOfflineData) {
        setIsOfflineInitialized(false);
        setOfflineProjects([]);
        return;
      }

      try {
        const initialized = await offlineStorage.initializeForUser(user.id);
        setIsOfflineInitialized(initialized);

        if (initialized) {
          const projects = await offlineStorage.getAllProjects();
          setOfflineProjects(projects);
          setOfflineError(null);
        }
      } catch (error) {
        console.error('[useOfflineProjects] Failed to initialize offline storage:', error);
        setOfflineError('Kunne ikke initialisere offline-lagring');
        setIsOfflineInitialized(false);
      }
    };

    initializeOfflineStorage();
  }, [user?.id, canAccessOfflineData]);

  // Auto-cache online projects for offline access
  useEffect(() => {
    const cacheOnlineProjects = async () => {
      console.log('[useOfflineProjects] Checking caching conditions:', {
        hasOnlineProjects: !!onlineProjects?.length,
        isOfflineInitialized,
        canAccessOfflineData,
        hasUserId: !!user?.id,
        isOnline
      });

      if (!onlineProjects || !isOfflineInitialized || !canAccessOfflineData || !user?.id) {
        console.log('[useOfflineProjects] Skipping caching - conditions not met');
        return;
      }

      try {
        console.log('[useOfflineProjects] Starting to cache', onlineProjects.length, 'online projects');

        // Get currently cached projects
        const cachedProjects = await offlineStorage.getAllProjects();
        const cachedProjectIds = new Set(cachedProjects.map(p => p._id || p.id));

        console.log('[useOfflineProjects] Currently cached projects:', cachedProjectIds.size);

        // Cache new online projects that aren't already cached
        let cachedCount = 0;
        for (const project of onlineProjects) {
          const projectId = project._id || (project as any).id;

          if (!cachedProjectIds.has(projectId) && !(project as any).isOffline) {
            // Convert online project to offline format for caching
            const cacheableProject = {
              ...project,
              id: projectId,
              _id: projectId,
              isOffline: false, // Mark as online-sourced cache
              syncStatus: 'cached' as const,
              cachedAt: new Date().toISOString()
            };

            await offlineStorage.cacheOnlineProject(cacheableProject);
            cachedCount++;
            console.log('[useOfflineProjects] ✅ Cached project:', project.name || (project as any).title);
          } else {
            console.log('[useOfflineProjects] ⏭️ Skipping project (already cached or offline):', project.name || (project as any).title);
          }
        }

        console.log('[useOfflineProjects] Cached', cachedCount, 'new projects');

        // Refresh offline projects after caching
        const updatedProjects = await offlineStorage.getAllProjects();
        setOfflineProjects(updatedProjects);
        console.log('[useOfflineProjects] Updated offline projects count:', updatedProjects.length);
      } catch (error) {
        console.error('[useOfflineProjects] ❌ Failed to cache online projects:', error);
      }
    };

    // Only cache when online and have projects
    if (isOnline && onlineProjects && onlineProjects.length > 0) {
      console.log('[useOfflineProjects] Triggering caching for', onlineProjects.length, 'projects');
      cacheOnlineProjects();
    }
  }, [onlineProjects, isOfflineInitialized, canAccessOfflineData, user?.id, isOnline]);

  // Refresh offline projects periodically
  useEffect(() => {
    if (!isOfflineInitialized) return;

    const refreshOfflineProjects = async () => {
      try {
        const projects = await offlineStorage.getAllProjects();
        setOfflineProjects(projects);
      } catch (error) {
        console.error('[useOfflineProjects] Failed to refresh offline projects:', error);
      }
    };

    const interval = setInterval(refreshOfflineProjects, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [isOfflineInitialized]);

  // Combine online and offline projects
  const allProjects = useMemo(() => {
    const combined = [];
    
    // Add online projects
    if (onlineProjects) {
      combined.push(...onlineProjects.map(project => ({
        ...project,
        isOffline: false,
        syncStatus: 'synced' as const
      })));
    }

    // Add offline projects (avoid duplicates)
    if (offlineProjects.length > 0) {
      const onlineProjectIds = new Set(onlineProjects?.map(p => p._id) || []);
      const uniqueOfflineProjects = offlineProjects.filter(
        project => !onlineProjectIds.has(project.id as any)
      );
      combined.push(...uniqueOfflineProjects);
    }

    // Sort by creation date (newest first)
    return combined.sort((a, b) => {
      const dateA = new Date(a.createdAt || a._creationTime || 0);
      const dateB = new Date(b.createdAt || b._creationTime || 0);
      return dateB.getTime() - dateA.getTime();
    });
  }, [onlineProjects, offlineProjects]);

  // Create project offline
  const createProjectOffline = async (projectData: {
    title: string;
    description: string;
    status?: string;
  }): Promise<string> => {
    if (!user?.id || !isOfflineInitialized) {
      throw new Error('Offline storage ikke tilgjengelig');
    }

    const offlineId = `offline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const project = {
      id: offlineId,
      title: projectData.title,
      description: projectData.description,
      status: projectData.status || 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    try {
      await offlineStorage.addOfflineProject(project);
      
      // Refresh offline projects
      const updatedProjects = await offlineStorage.getAllProjects();
      setOfflineProjects(updatedProjects);
      
      return offlineId;
    } catch (error) {
      console.error('[useOfflineProjects] Failed to create offline project:', error);
      throw new Error('Kunne ikke opprette prosjekt offline');
    }
  };

  // Get project by ID (online or offline)
  const getProjectById = (projectId: string) => {
    console.log('[useOfflineProjects] getProjectById called for:', projectId, {
      isOnline,
      onlineProjectsCount: onlineProjects?.length || 0,
      offlineProjectsCount: offlineProjects.length
    });

    // First try to find in online projects (most up-to-date)
    if (isOnline && onlineProjects) {
      const onlineProject = onlineProjects.find(project =>
        project._id === projectId
      );
      if (onlineProject) {
        console.log('[useOfflineProjects] ✅ Found online project:', onlineProject.name);
        return onlineProject;
      } else {
        console.log('[useOfflineProjects] ⚠️ Online project not found. Available online projects:',
          onlineProjects.map(p => ({ id: p._id, name: p.name })));
      }
    }

    // Fallback to cached/offline projects
    const offlineProject = offlineProjects.find(project =>
      project._id === projectId || (project as any).id === projectId
    );

    if (offlineProject) {
      console.log('[useOfflineProjects] ✅ Found offline/cached project:', offlineProject.name || (offlineProject as any).title, 'syncStatus:', (offlineProject as any).syncStatus);
    } else {
      console.log('[useOfflineProjects] ❌ No project found with ID:', projectId);
      console.log('[useOfflineProjects] Available offline projects:', offlineProjects.map(p => ({
        id: p._id || (p as any).id,
        name: p.name || (p as any).title,
        syncStatus: p.syncStatus
      })));
    }

    return offlineProject;
  };

  // Check if project was created offline (not just cached)
  const isProjectOffline = (projectId: string): boolean => {
    const project = offlineProjects.find(p =>
      (p._id === projectId || p.id === projectId) && p.isOffline === true
    );
    return !!project;
  };

  // Check if project is available offline (either cached or created offline)
  const isProjectAvailableOffline = (projectId: string): boolean => {
    const project = offlineProjects.find(p =>
      (p._id === projectId || p.id === projectId)
    );
    return !!project; // Available if it exists in offline storage (cached or created offline)
  };

  // Get sync status for project
  const getProjectSyncStatus = (projectId: string): string => {
    // Check if it's an offline project
    const offlineProject = offlineProjects.find(p =>
      (p._id === projectId || p.id === projectId)
    );

    if (offlineProject) {
      if (offlineProject.isOffline) {
        return offlineProject.syncStatus || 'pending';
      } else {
        return 'cached'; // Cached online project
      }
    }

    // Online project
    return 'synced';
  };

  // Check if project is cached for offline access
  const isProjectCached = (projectId: string): boolean => {
    const cachedProject = offlineProjects.find(p =>
      (p._id === projectId || p.id === projectId) && p.syncStatus === 'cached'
    );
    return !!cachedProject;
  };

  return {
    // Data
    projects: allProjects,
    onlineProjects: onlineProjects || [],
    offlineProjects,

    // State
    isOnline,
    isOfflineInitialized,
    canAccessOfflineData,
    offlineError,

    // Actions
    createProjectOffline,
    getProjectById,
    isProjectOffline,
    isProjectCached,
    isProjectAvailableOffline,
    getProjectSyncStatus,

    // Utilities
    hasOfflineProjects: offlineProjects.length > 0,
    totalProjects: allProjects.length,
    onlineProjectCount: onlineProjects?.length || 0,
    offlineProjectCount: offlineProjects.length,
    cachedProjectCount: offlineProjects.filter(p => p.syncStatus === 'cached').length
  };
}

/**
 * Hook for managing project logs with offline support
 */
export function useOfflineProjectLogs(projectId: string) {
  const { user } = useUser();
  const { isOnline, canAccessOfflineData } = usePWA();
  const [offlineLogs, setOfflineLogs] = useState<any[]>([]);
  const [isOfflineInitialized, setIsOfflineInitialized] = useState(false);

  // Query online project logs
  const onlineLogs = useQuery(
    api.logEntries.getByProject,
    projectId && user?.id ? { projectId: projectId as any, userId: user.id } : "skip"
  );

  // Initialize and load offline logs
  useEffect(() => {
    const loadOfflineLogs = async () => {
      if (!user?.id || !canAccessOfflineData || !projectId) {
        setOfflineLogs([]);
        return;
      }

      try {
        const initialized = await offlineStorage.initializeForUser(user.id);
        setIsOfflineInitialized(initialized);
        
        if (initialized) {
          const logs = await offlineStorage.getProjectLogs(projectId);
          setOfflineLogs(logs);
        }
      } catch (error) {
        console.error('[useOfflineProjectLogs] Failed to load offline logs:', error);
      }
    };

    loadOfflineLogs();
  }, [user?.id, canAccessOfflineData, projectId]);

  // Combine online and offline logs
  const allLogs = useMemo(() => {
    const combined = [];
    
    // Add online logs
    // Temporarily disabled - onlineLogs is undefined due to type instantiation issues
    // if (onlineLogs && Array.isArray(onlineLogs)) {
    //   combined.push(...onlineLogs.map((log: any) => ({
    //     ...log,
    //     isOffline: false,
    //     syncStatus: 'synced' as const
    //   })));
    // }

    // Add offline logs
    if (offlineLogs.length > 0) {
      combined.push(...offlineLogs);
    }

    // Sort by creation date (newest first)
    return combined.sort((a: any, b: any) => {
      const dateA = new Date(a.createdAt || a._creationTime || 0);
      const dateB = new Date(b.createdAt || b._creationTime || 0);
      return dateB.getTime() - dateA.getTime();
    });
  }, [onlineLogs, offlineLogs]);

  // Create log entry offline
  const createLogOffline = async (logData: {
    description: string;
    images?: string[];
  }): Promise<string> => {
    if (!user?.id || !isOfflineInitialized || !projectId) {
      throw new Error('Offline storage ikke tilgjengelig');
    }

    const offlineId = `offline-log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const log = {
      id: offlineId,
      projectId,
      description: logData.description,
      images: logData.images || [],
      createdAt: new Date().toISOString()
    };

    try {
      await offlineStorage.addOfflineProjectLog(log);
      
      // Refresh offline logs
      const updatedLogs = await offlineStorage.getProjectLogs(projectId);
      setOfflineLogs(updatedLogs);
      
      return offlineId;
    } catch (error) {
      console.error('[useOfflineProjectLogs] Failed to create offline log:', error);
      throw new Error('Kunne ikke opprette loggoppføring offline');
    }
  };

  return {
    // Data
    logs: allLogs,
    onlineLogs: onlineLogs || [],
    offlineLogs,
    
    // State
    isOnline,
    isOfflineInitialized,
    canAccessOfflineData,
    
    // Actions
    createLogOffline,
    
    // Utilities
    hasOfflineLogs: offlineLogs.length > 0,
    totalLogs: allLogs.length,
    onlineLogCount: 0, // Temporarily disabled - onlineLogs is undefined due to type instantiation issues
    offlineLogCount: offlineLogs.length
  };
}

/**
 * Hook for managing offline images
 */
export function useOfflineImages(projectId?: string) {
  const { user } = useUser();
  const { canAccessOfflineData } = usePWA();
  const [offlineImages, setOfflineImages] = useState<OfflineImage[]>([]);
  const [uploadQueue, setUploadQueue] = useState<ImageUploadQueueItem[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize offline image storage
  useEffect(() => {
    const initializeImageStorage = async () => {
      if (!user?.id || !canAccessOfflineData) {
        setIsInitialized(false);
        return;
      }

      try {
        const initialized = await offlineImageStorage.initializeForUser(user.id);
        setIsInitialized(initialized);
        setError(null);
      } catch (error) {
        console.error('[useOfflineImages] Failed to initialize:', error);
        setError('Kunne ikke initialisere offline bildelagring');
        setIsInitialized(false);
      }
    };

    initializeImageStorage();
  }, [user?.id, canAccessOfflineData]);

  // Load offline images for project
  useEffect(() => {
    const loadProjectImages = async () => {
      if (!isInitialized || !projectId) {
        setOfflineImages([]);
        return;
      }

      try {
        const images = await offlineImageStorage.getProjectImages(projectId);
        setOfflineImages(images);
      } catch (error) {
        console.error('[useOfflineImages] Failed to load project images:', error);
      }
    };

    loadProjectImages();
  }, [isInitialized, projectId]);

  // Load upload queue
  useEffect(() => {
    const loadUploadQueue = async () => {
      if (!isInitialized) {
        setUploadQueue([]);
        return;
      }

      try {
        const queue = await offlineImageStorage.getUploadQueue();
        setUploadQueue(queue);
      } catch (error) {
        console.error('[useOfflineImages] Failed to load upload queue:', error);
      }
    };

    loadUploadQueue();

    // Refresh queue periodically
    const interval = setInterval(loadUploadQueue, 30000);
    return () => clearInterval(interval);
  }, [isInitialized]);

  // Store image offline
  const storeImageOffline = async (
    file: File,
    targetProjectId?: string,
    logId?: string
  ): Promise<string> => {
    if (!isInitialized || !user?.id) {
      throw new Error('Offline bildelagring ikke tilgjengelig');
    }

    const useProjectId = targetProjectId || projectId;
    if (!useProjectId) {
      throw new Error('Prosjekt-ID mangler');
    }

    try {
      const imageId = await offlineImageStorage.storeImageOffline(useProjectId, file, logId);

      // Refresh images and queue
      const [images, queue] = await Promise.all([
        offlineImageStorage.getProjectImages(useProjectId),
        offlineImageStorage.getUploadQueue()
      ]);

      setOfflineImages(images);
      setUploadQueue(queue);

      return imageId;
    } catch (error) {
      console.error('[useOfflineImages] Failed to store image offline:', error);
      throw error;
    }
  };

  // Get offline image as blob
  const getOfflineImageBlob = async (imageId: string): Promise<Blob | null> => {
    if (!isInitialized) return null;
    return await offlineImageStorage.getOfflineImage(imageId);
  };

  // Get offline image thumbnail
  const getOfflineImageThumbnail = async (imageId: string): Promise<Blob | null> => {
    if (!isInitialized) return null;
    return await offlineImageStorage.getOfflineImageThumbnail(imageId);
  };

  // Get image URL (creates object URL for offline images)
  const getImageUrl = async (imageId: string): Promise<string | null> => {
    const blob = await getOfflineImageBlob(imageId);
    if (!blob) return null;
    return URL.createObjectURL(blob);
  };

  // Get thumbnail URL
  const getThumbnailUrl = async (imageId: string): Promise<string | null> => {
    const blob = await getOfflineImageThumbnail(imageId);
    if (!blob) return null;
    return URL.createObjectURL(blob);
  };

  return {
    // Data
    offlineImages,
    uploadQueue,

    // State
    isInitialized,
    canAccessOfflineData,
    error,

    // Actions
    storeImageOffline,
    getOfflineImageBlob,
    getOfflineImageThumbnail,
    getImageUrl,
    getThumbnailUrl,

    // Utilities
    hasOfflineImages: offlineImages.length > 0,
    hasUploadQueue: uploadQueue.length > 0,
    pendingUploads: uploadQueue.filter(item => item.imageData.syncStatus === 'pending').length,
    failedUploads: uploadQueue.filter(item => item.imageData.syncStatus === 'error').length
  };
}
