import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@clerk/clerk-react';
import { getPWAPreferencesManager, PWAPreferences } from '../utils/pwaPreferences';

interface PWAInstallPrompt extends Event {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  isUpdateAvailable: boolean;
  installPrompt: PWAInstallPrompt | null;
  hasOfflineSession: boolean;
  canAccessOfflineData: boolean;
  notificationPermission: NotificationPermission;
  isPushSupported: boolean;
  // Enhanced PWA preferences
  shouldShowInstallPrompt: boolean;
  canShowMilestonePrompt: boolean;
  installPreferences: ReturnType<typeof getPWAPreferencesManager>['getPreferences'] | null;
}

interface PWAActions {
  installApp: () => Promise<boolean>;
  updateApp: () => Promise<void>;
  registerForNotifications: () => Promise<boolean>;
  checkOfflineAccess: () => boolean;
  requestNotificationPermission: () => Promise<NotificationPermission>;
  // Enhanced PWA preference actions
  dismissInstallPrompt: (isPermanent?: boolean) => void;
  recordMilestone: (milestone: keyof PWAPreferences['milestones']) => void;
  checkMilestonePrompt: (milestone: keyof PWAPreferences['milestones']) => boolean;
  resetInstallPreferences: () => void;
  getInstallStatus: () => { canShowPrompt: boolean; reason: string; nextPromptDate: Date | null; dismissalCount: number; hasInstalled: boolean; };
}

export function usePWA(): PWAState & PWAActions {
  const { user, isLoaded } = useUser();
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [installPrompt, setInstallPrompt] = useState<PWAInstallPrompt | null>(null);
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [hasOfflineSession, setHasOfflineSession] = useState(false);
  const [canAccessOfflineData, setCanAccessOfflineData] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState<NotificationPermission>('default');
  const [isPushSupported, setIsPushSupported] = useState(false);

  // Enhanced PWA preferences state
  const [shouldShowInstallPrompt, setShouldShowInstallPrompt] = useState(false);
  const [canShowMilestonePrompt, setCanShowMilestonePrompt] = useState(false);
  const [installPreferences, setInstallPreferences] = useState<(() => Readonly<PWAPreferences>) | null>(null);

  useEffect(() => {
    // Check if app is already installed
    const checkInstallStatus = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      const isPWAInstalled = isStandalone || isInWebAppiOS;

      setIsInstalled(isPWAInstalled);

      // Update preferences if PWA is detected as installed
      if (isPWAInstalled && user?.id) {
        const prefsManager = getPWAPreferencesManager(user.id);
        if (!prefsManager.getPreferences().hasInstalled) {
          prefsManager.recordInstallation();
          console.log('[PWA] Detected PWA installation, updating preferences');
        }
      }
    };

    checkInstallStatus();

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setInstallPrompt(e as PWAInstallPrompt);
      setIsInstallable(true);
    };

    // Listen for app installed
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setInstallPrompt(null);

      // Record installation in preferences
      if (user?.id) {
        getPWAPreferencesManager(user.id).recordInstallation();
      }

      console.log('[PWA] App installed successfully');
    };

    // Listen for online/offline status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    // Register event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Register service worker
    registerServiceWorker();

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Check offline session and data access capabilities
  useEffect(() => {
    const checkOfflineSession = () => {
      if (!isLoaded) return;

      // Check if user has a valid session (even offline)
      const hasValidSession = !!user;

      console.log('[PWA] Checking offline session:', {
        hasValidSession,
        isOnline,
        userId: user?.id?.substring(0, 8) + '...'
      });

      setHasOfflineSession(hasValidSession);

      // GDPR COMPLIANCE: Offline caching is now classified as "necessary"
      // under GDPR Article 6(1)(b) since it's required for service delivery.
      // JobbLogg is designed as an offline-first PWA for construction sites.
      // When user accepts "necessary cookies", offline functionality is included.
      const hasOfflineAccess = hasValidSession;
      setCanAccessOfflineData(hasOfflineAccess);

      console.log('[PWA] Set canAccessOfflineData to:', hasOfflineAccess, '(always true when authenticated - offline is necessary)');

      // Mark offline as enabled for authenticated users (necessary functionality)
      if (hasValidSession) {
        localStorage.setItem('jobblogg-offline-enabled', 'true');
      }
    };

    checkOfflineSession();
  }, [user, isLoaded, isOnline]);

  // Clear offline access on user logout
  useEffect(() => {
    if (isLoaded && !user) {
      localStorage.removeItem('jobblogg-offline-enabled');
      setHasOfflineSession(false);
      setCanAccessOfflineData(false);
    }
  }, [user, isLoaded]);

  // Manage PWA preferences state
  useEffect(() => {
    if (!isLoaded || !user?.id) {
      setShouldShowInstallPrompt(false);
      setCanShowMilestonePrompt(false);
      setInstallPreferences(null);
      return;
    }

    const prefsManager = getPWAPreferencesManager(user.id);
    const preferences = prefsManager.getPreferences();

    // Update state based on preferences and current PWA status
    const shouldShow = prefsManager.shouldShowInstallPrompt() && isInstallable && !isInstalled;
    const canShowMilestone = !isInstalled && !preferences.permanentlyDismissed;

    setShouldShowInstallPrompt(shouldShow);
    setCanShowMilestonePrompt(canShowMilestone);
    setInstallPreferences(() => preferences);

    console.log('[PWA] Updated preferences state:', {
      shouldShow,
      canShowMilestone,
      isInstallable,
      isInstalled,
      dismissalCount: preferences.dismissalCount
    });
  }, [user, isLoaded, isInstallable, isInstalled]);

  // Initialize push notification support
  useEffect(() => {
    const checkPushSupport = () => {
      const isSupported = 'serviceWorker' in navigator &&
                          'PushManager' in window &&
                          'Notification' in window;
      setIsPushSupported(isSupported);

      if (isSupported && 'Notification' in window) {
        setNotificationPermission(Notification.permission);
      }
    };

    checkPushSupport();
  }, []);

  const registerServiceWorker = async () => {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        });

        setServiceWorkerRegistration(registration);

        // Check for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                setIsUpdateAvailable(true);
                console.log('[PWA] New version available');
              }
            });
          }
        });

        // Listen for controlling service worker changes
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          window.location.reload();
        });

        console.log('[PWA] Service Worker registered successfully');
      } catch (error) {
        console.error('[PWA] Service Worker registration failed:', error instanceof Error ? error.message : String(error));
      }
    }
  };

  const installApp = async (): Promise<boolean> => {
    if (!installPrompt) {
      console.warn('[PWA] No install prompt available');
      return false;
    }

    try {
      await installPrompt.prompt();
      const choiceResult = await installPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        console.log('[PWA] User accepted the install prompt');
        setIsInstallable(false);
        setInstallPrompt(null);

        // Record installation in preferences
        if (user?.id) {
          getPWAPreferencesManager(user.id).recordInstallation();
        }

        return true;
      } else {
        console.log('[PWA] User dismissed the install prompt');

        // Record dismissal in preferences
        if (user?.id) {
          getPWAPreferencesManager(user.id).recordDismissal(false);
        }

        return false;
      }
    } catch (error) {
      console.error('[PWA] Install failed:', error instanceof Error ? error.message : String(error));
      return false;
    }
  };

  const updateApp = async (): Promise<void> => {
    if (!serviceWorkerRegistration) {
      console.warn('[PWA] No service worker registration available');
      return;
    }

    try {
      const waitingWorker = serviceWorkerRegistration.waiting;
      if (waitingWorker) {
        waitingWorker.postMessage({ type: 'SKIP_WAITING' });
        setIsUpdateAvailable(false);
      }
    } catch (error) {
      console.error('[PWA] Update failed:', error);
    }
  };

  const registerForNotifications = async (): Promise<boolean> => {
    if (!('Notification' in window) || !serviceWorkerRegistration) {
      console.warn('[PWA] Notifications not supported');
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      
      if (permission === 'granted') {
        console.log('[PWA] Notification permission granted');
        
        // Subscribe to push notifications (if needed)
        const subscription = await serviceWorkerRegistration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: urlBase64ToUint8Array(
            // Add your VAPID public key here
            'YOUR_VAPID_PUBLIC_KEY'
          )
        });

        console.log('[PWA] Push subscription created:', subscription);
        return true;
      } else {
        console.log('[PWA] Notification permission denied');
        return false;
      }
    } catch (error) {
      console.error('[PWA] Notification registration failed:', error);
      return false;
    }
  };

  const checkOfflineAccess = (): boolean => {
    return hasOfflineSession && canAccessOfflineData;
  };

  const requestNotificationPermission = async (): Promise<NotificationPermission> => {
    if (!isPushSupported || !('Notification' in window)) {
      console.warn('[PWA] Push notifications not supported');
      return 'denied';
    }

    try {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);

      if (permission === 'granted') {
        console.log('[PWA] Notification permission granted');

        // Register for push notifications if service worker is available
        if (serviceWorkerRegistration) {
          try {
            const subscription = await serviceWorkerRegistration.pushManager.subscribe({
              userVisibleOnly: true,
              applicationServerKey: process.env.VITE_VAPID_PUBLIC_KEY // You'll need to add this
            });

            console.log('[PWA] Push subscription created:', subscription);

            // TODO: Send subscription to your backend
            // await sendSubscriptionToBackend(subscription);

          } catch (pushError) {
            console.error('[PWA] Failed to subscribe to push notifications:', pushError);
          }
        }
      } else {
        console.log('[PWA] Notification permission denied or dismissed');
      }

      return permission;
    } catch (error) {
      console.error('[PWA] Error requesting notification permission:', error);
      return 'denied';
    }
  };

  // Enhanced PWA preference actions
  const dismissInstallPrompt = useCallback((isPermanent: boolean = false) => {
    if (user?.id) {
      getPWAPreferencesManager(user.id).recordDismissal(isPermanent);
      setShouldShowInstallPrompt(false);
      console.log('[PWA] Recorded prompt dismissal:', { isPermanent });
    }
  }, [user?.id]);

  const recordMilestone = useCallback((milestone: keyof PWAPreferences['milestones']) => {
    if (user?.id) {
      getPWAPreferencesManager(user.id).recordMilestone(milestone);
      console.log('[PWA] Recorded milestone:', milestone);
    }
  }, [user?.id]);

  const checkMilestonePrompt = useCallback((milestone: keyof PWAPreferences['milestones']): boolean => {
    if (!user?.id) return false;
    return getPWAPreferencesManager(user.id).shouldShowMilestonePrompt(milestone);
  }, [user?.id]);

  const resetInstallPreferences = useCallback(() => {
    if (user?.id) {
      getPWAPreferencesManager(user.id).resetPreferences();
      setShouldShowInstallPrompt(true);
      setCanShowMilestonePrompt(true);
      console.log('[PWA] Reset install preferences');
    }
  }, [user?.id]);

  const getInstallStatus = useCallback(() => {
    if (!user?.id) {
      return {
        canShowPrompt: false,
        reason: 'User not authenticated',
        nextPromptDate: null,
        dismissalCount: 0,
        hasInstalled: false
      };
    }
    return getPWAPreferencesManager(user.id).getStatus();
  }, [user?.id]);

  return {
    // State
    isInstallable,
    isInstalled,
    isOnline,
    isUpdateAvailable,
    installPrompt,
    hasOfflineSession,
    canAccessOfflineData,
    notificationPermission,
    isPushSupported,
    // Enhanced PWA preferences state
    shouldShowInstallPrompt,
    canShowMilestonePrompt,
    installPreferences,

    // Actions
    installApp,
    updateApp,
    registerForNotifications,
    checkOfflineAccess,
    requestNotificationPermission,
    // Enhanced PWA preference actions
    dismissInstallPrompt,
    recordMilestone,
    checkMilestonePrompt,
    resetInstallPreferences,
    getInstallStatus
  };
}

// Utility function for VAPID key conversion
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// PWA utility functions
export const PWAUtils = {
  // Check if running as PWA
  isPWA: (): boolean => {
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    return isStandalone || isInWebAppiOS;
  },

  // Get install instructions based on browser
  getInstallInstructions: (): string => {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      return 'Trykk på meny-ikonet (⋮) og velg "Installer JobbLogg"';
    } else if (userAgent.includes('firefox')) {
      return 'Trykk på adresselinjen og velg "Installer denne siden som app"';
    } else if (userAgent.includes('safari')) {
      return 'Trykk på Del-knappen og velg "Legg til på hjemskjerm"';
    } else if (userAgent.includes('edg')) {
      return 'Trykk på meny-ikonet (⋯) og velg "Installer denne siden som app"';
    } else {
      return 'Se nettleserens meny for å installere som app';
    }
  },

  // Share content using Web Share API
  shareContent: async (data: { title: string; text: string; url?: string }): Promise<boolean> => {
    if (navigator.share) {
      try {
        await navigator.share(data);
        return true;
      } catch (error) {
        console.error('[PWA] Share failed:', error);
        return false;
      }
    } else {
      // Fallback to clipboard
      try {
        const shareText = `${data.title}\n${data.text}${data.url ? `\n${data.url}` : ''}`;
        await navigator.clipboard.writeText(shareText);
        return true;
      } catch (error) {
        console.error('[PWA] Clipboard fallback failed:', error);
        return false;
      }
    }
  }
};
