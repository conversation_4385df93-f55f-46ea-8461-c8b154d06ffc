import { useEffect, useCallback } from 'react';
import { useUser } from '@clerk/clerk-react';
import { usePWA } from './usePWA';
import { getPWAPreferencesManager } from '../utils/pwaPreferences';

/**
 * PWA Milestones Hook
 * Automatically tracks user milestones and triggers PWA installation prompts
 * at strategic moments for better user engagement
 */
export const usePWAMilestones = () => {
  const { user } = useUser();
  const { 
    recordMilestone, 
    checkMilestonePrompt,
    isInstalled,
    shouldShowInstallPrompt 
  } = usePWA();

  /**
   * Record that user created their first project
   */
  const recordFirstProjectCreated = useCallback(() => {
    if (!user?.id || isInstalled) return;
    
    const prefsManager = getPWAPreferencesManager(user.id);
    const preferences = prefsManager.getPreferences();
    
    // Only record if not already recorded
    if (!preferences.milestones.firstProjectCreated) {
      recordMilestone('firstProjectCreated');
      console.log('[PWA Milestones] Recorded first project created');
    }
  }, [user?.id, isInstalled, recordMilestone]);

  /**
   * Record that user completed their first project
   */
  const recordFirstProjectCompleted = useCallback(() => {
    if (!user?.id || isInstalled) return;
    
    const prefsManager = getPWAPreferencesManager(user.id);
    const preferences = prefsManager.getPreferences();
    
    // Only record if not already recorded
    if (!preferences.milestones.firstProjectCompleted) {
      recordMilestone('firstProjectCompleted');
      console.log('[PWA Milestones] Recorded first project completed');
    }
  }, [user?.id, isInstalled, recordMilestone]);

  /**
   * Record that user created their tenth project
   */
  const recordTenthProjectCreated = useCallback(() => {
    if (!user?.id || isInstalled) return;
    
    const prefsManager = getPWAPreferencesManager(user.id);
    const preferences = prefsManager.getPreferences();
    
    // Only record if not already recorded
    if (!preferences.milestones.tenthProjectCreated) {
      recordMilestone('tenthProjectCreated');
      console.log('[PWA Milestones] Recorded tenth project created');
    }
  }, [user?.id, isInstalled, recordMilestone]);

  /**
   * Record that user has been active for a week
   */
  const recordFirstWeekActive = useCallback(() => {
    if (!user?.id || isInstalled) return;
    
    const prefsManager = getPWAPreferencesManager(user.id);
    const preferences = prefsManager.getPreferences();
    
    // Only record if not already recorded and user has been active for a week
    if (!preferences.milestones.firstWeekActive) {
      const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      if (preferences.consentTimestamp < oneWeekAgo) {
        recordMilestone('firstWeekActive');
        console.log('[PWA Milestones] Recorded first week active');
      }
    }
  }, [user?.id, isInstalled, recordMilestone]);

  /**
   * Record that user has been active for a month
   */
  const recordFirstMonthActive = useCallback(() => {
    if (!user?.id || isInstalled) return;
    
    const prefsManager = getPWAPreferencesManager(user.id);
    const preferences = prefsManager.getPreferences();
    
    // Only record if not already recorded and user has been active for a month
    if (!preferences.milestones.firstMonthActive) {
      const oneMonthAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
      if (preferences.consentTimestamp < oneMonthAgo) {
        recordMilestone('firstMonthActive');
        console.log('[PWA Milestones] Recorded first month active');
      }
    }
  }, [user?.id, isInstalled, recordMilestone]);

  /**
   * Check if we should show a milestone-based PWA prompt
   */
  const shouldShowMilestonePrompt = useCallback((milestone: 'firstProjectCreated' | 'firstProjectCompleted' | 'tenthProjectCreated' | 'firstWeekActive' | 'firstMonthActive'): boolean => {
    if (!user?.id || isInstalled || !shouldShowInstallPrompt) return false;
    return checkMilestonePrompt(milestone);
  }, [user?.id, isInstalled, shouldShowInstallPrompt, checkMilestonePrompt]);

  /**
   * Get the next milestone that should trigger a prompt
   */
  const getNextMilestonePrompt = useCallback((): {
    milestone: 'firstProjectCreated' | 'firstProjectCompleted' | 'tenthProjectCreated' | 'firstWeekActive' | 'firstMonthActive' | null;
    ready: boolean;
  } => {
    if (!user?.id || isInstalled) {
      return { milestone: null, ready: false };
    }

    const prefsManager = getPWAPreferencesManager(user.id);
    const preferences = prefsManager.getPreferences();

    // Check milestones in priority order
    const milestones: Array<'firstProjectCreated' | 'firstProjectCompleted' | 'tenthProjectCreated' | 'firstWeekActive' | 'firstMonthActive'> = [
      'firstProjectCompleted', // Highest priority - user just completed something
      'tenthProjectCreated',   // High engagement milestone
      'firstProjectCreated',   // Good first milestone
      'firstMonthActive',      // Long-term engagement
      'firstWeekActive'        // Basic engagement
    ];

    for (const milestone of milestones) {
      if (!preferences.milestones[milestone] && shouldShowMilestonePrompt(milestone)) {
        return { milestone, ready: true };
      }
    }

    return { milestone: null, ready: false };
  }, [user?.id, isInstalled, shouldShowMilestonePrompt]);

  /**
   * Auto-check for time-based milestones on component mount and user activity
   */
  useEffect(() => {
    if (!user?.id || isInstalled) return;

    // Check time-based milestones
    recordFirstWeekActive();
    recordFirstMonthActive();
  }, [user?.id, isInstalled, recordFirstWeekActive, recordFirstMonthActive]);

  /**
   * Get milestone statistics for debugging/analytics
   */
  const getMilestoneStats = useCallback(() => {
    if (!user?.id) return null;

    const prefsManager = getPWAPreferencesManager(user.id);
    const preferences = prefsManager.getPreferences();
    
    return {
      totalMilestones: Object.keys(preferences.milestones).length,
      milestones: preferences.milestones,
      accountAge: Date.now() - preferences.consentTimestamp,
      canShowPrompts: shouldShowInstallPrompt && !isInstalled
    };
  }, [user?.id, shouldShowInstallPrompt, isInstalled]);

  return {
    // Milestone recording functions
    recordFirstProjectCreated,
    recordFirstProjectCompleted,
    recordTenthProjectCreated,
    recordFirstWeekActive,
    recordFirstMonthActive,
    
    // Milestone checking functions
    shouldShowMilestonePrompt,
    getNextMilestonePrompt,
    getMilestoneStats,
    
    // Convenience functions for common checks
    canShowFirstProjectPrompt: () => shouldShowMilestonePrompt('firstProjectCreated'),
    canShowFirstCompletionPrompt: () => shouldShowMilestonePrompt('firstProjectCompleted'),
    canShowTenthProjectPrompt: () => shouldShowMilestonePrompt('tenthProjectCreated'),
    canShowWeeklyPrompt: () => shouldShowMilestonePrompt('firstWeekActive'),
    canShowMonthlyPrompt: () => shouldShowMilestonePrompt('firstMonthActive'),
  };
};

/**
 * PWA Milestone Tracker Component Hook
 * Automatically tracks project-related milestones
 * Use this in components that handle project creation/completion
 */
export const usePWAProjectMilestones = (projectCount?: number) => {
  const { 
    recordFirstProjectCreated, 
    recordTenthProjectCreated,
    recordFirstProjectCompleted 
  } = usePWAMilestones();

  /**
   * Call this when a user creates a new project
   */
  const onProjectCreated = useCallback((isFirstProject: boolean = false, totalProjects: number = 0) => {
    if (isFirstProject || totalProjects === 1) {
      recordFirstProjectCreated();
    } else if (totalProjects === 10) {
      recordTenthProjectCreated();
    }
  }, [recordFirstProjectCreated, recordTenthProjectCreated]);

  /**
   * Call this when a user completes a project
   */
  const onProjectCompleted = useCallback((isFirstCompletion: boolean = false) => {
    if (isFirstCompletion) {
      recordFirstProjectCompleted();
    }
  }, [recordFirstProjectCompleted]);

  /**
   * Auto-track based on project count changes
   */
  useEffect(() => {
    if (typeof projectCount === 'number') {
      if (projectCount === 1) {
        recordFirstProjectCreated();
      } else if (projectCount === 10) {
        recordTenthProjectCreated();
      }
    }
  }, [projectCount, recordFirstProjectCreated, recordTenthProjectCreated]);

  return {
    onProjectCreated,
    onProjectCompleted
  };
};

/**
 * PWA Milestone Banner Hook
 * Provides milestone-specific banner configuration
 */
export const usePWAMilestoneBanner = () => {
  const { getNextMilestonePrompt } = usePWAMilestones();
  
  const nextMilestone = getNextMilestonePrompt();
  
  return {
    shouldShowMilestoneBanner: nextMilestone.ready,
    milestoneType: nextMilestone.milestone,
    isMilestonePrompt: nextMilestone.ready && nextMilestone.milestone !== null
  };
};
