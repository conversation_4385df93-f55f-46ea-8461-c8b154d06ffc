import { useUser } from '@clerk/clerk-react';
import { useMutation, useQuery } from 'convex/react';
import { useCallback, useState } from 'react';
import { api } from '../../convex/_generated/api';
import { PLAN_FEATURES, getDowngradeRestrictions } from '../utils/planFeatures';

export interface PlanChangeState {
  isLoading: boolean;
  error: string | null;
  isProcessing: boolean;
  showComparison: boolean;
  showConfirmation: boolean;
  planChangeDetails: any;
}

export interface UsePlanManagementReturn {
  state: PlanChangeState;
  actions: {
    openPlanComparison: () => void;
    closePlanComparison: () => void;
    selectPlan: (planId: string, billingInterval: 'month' | 'year') => Promise<void>;
    confirmPlanChange: () => Promise<void>;
    cancelPlanChange: () => void;
    checkDowngradeRestrictions: (targetPlan: string, currentUsage: any) => {
      canDowngrade: boolean;
      restrictions: string[];
      warnings: string[];
    };
  };
  queries: {
    canChangeToPlan: (targetPlan: string) => any;
    planChangeHistory: any;
  };
}

export function usePlanManagement(
  currentPlan: string,
  currentBillingInterval: 'month' | 'year'
): UsePlanManagementReturn {
  const { user } = useUser();
  
  const [state, setState] = useState<PlanChangeState>({
    isLoading: false,
    error: null,
    isProcessing: false,
    showComparison: false,
    showConfirmation: false,
    planChangeDetails: null,
  });

  // Convex queries and mutations
  const calculateProration = useQuery(
    api.planChanges?.calculatePlanChangeProration,
    "skip"
  );
  
  const initiatePlanChange = useMutation(api.planChanges?.initiatePlanChange);
  
  const planChangeHistory = useQuery(
    api.planChanges?.getPlanChangeHistory,
    user ? { userId: user.id } : "skip"
  );

  const canChangeToPlan = useCallback((targetPlan: string) => {
    return useQuery(
      api.planChanges?.canChangeToPlan,
      user ? { userId: user.id, targetPlanId: targetPlan } : "skip"
    );
  }, [user]);

  // Actions
  const openPlanComparison = useCallback(() => {
    setState(prev => ({ ...prev, showComparison: true, error: null }));
  }, []);

  const closePlanComparison = useCallback(() => {
    setState(prev => ({ ...prev, showComparison: false }));
  }, []);

  const selectPlan = useCallback(async (planId: string, billingInterval: 'month' | 'year') => {
    if (!user || !api.planChanges?.calculatePlanChangeProration) return;

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Calculate proration details
      const prorationDetails = await calculateProration({
        userId: user.id,
        newPlanId: planId,
        newBillingInterval: billingInterval,
      });

      setState(prev => ({
        ...prev,
        isLoading: false,
        planChangeDetails: prorationDetails,
        showConfirmation: true,
        showComparison: false,
      }));
    } catch (error) {
      console.error('Failed to calculate proration:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Kunne ikke beregne prisendring. Prøv igjen senere.',
      }));
    }
  }, [user, calculateProration]);

  const confirmPlanChange = useCallback(async () => {
    if (!user || !state.planChangeDetails || !api.planChanges?.initiatePlanChange) return;

    setState(prev => ({ ...prev, isProcessing: true, error: null }));

    try {
      const result = await initiatePlanChange({
        userId: user.id,
        newPlanId: state.planChangeDetails.newPlan.id,
        newBillingInterval: state.planChangeDetails.newPlan.billingInterval,
      });

      if (result.status === 'initiated') {
        setState(prev => ({
          ...prev,
          isProcessing: false,
          showConfirmation: false,
          planChangeDetails: null,
        }));
        
        // Show success message and refresh after a delay
        alert('Planendring er startet! Siden vil oppdateres om et øyeblikk.');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to initiate plan change:', error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: 'Planendring feilet. Prøv igjen senere.',
      }));
    }
  }, [user, state.planChangeDetails, initiatePlanChange]);

  const cancelPlanChange = useCallback(() => {
    setState(prev => ({
      ...prev,
      showConfirmation: false,
      planChangeDetails: null,
      error: null,
    }));
  }, []);

  const checkDowngradeRestrictions = useCallback((targetPlan: string, currentUsage: any) => {
    return getDowngradeRestrictions(currentPlan, targetPlan, currentUsage);
  }, [currentPlan]);

  return {
    state,
    actions: {
      openPlanComparison,
      closePlanComparison,
      selectPlan,
      confirmPlanChange,
      cancelPlanChange,
      checkDowngradeRestrictions,
    },
    queries: {
      canChangeToPlan,
      planChangeHistory,
    },
  };
}

/**
 * Hook for checking feature access
 */
export function useFeatureAccess(userPlan: string) {
  const features = PLAN_FEATURES[userPlan];
  
  const hasFeature = useCallback((feature: keyof typeof PLAN_FEATURES.basic) => {
    return features ? features[feature] : false;
  }, [features]);

  const canCreateProject = useCallback((currentCount: number) => {
    if (!features) return false;
    if (features.maxProjects === -1) return true;
    return currentCount < features.maxProjects;
  }, [features]);

  const canAddTeamMember = useCallback((currentCount: number) => {
    if (!features) return false;
    if (features.maxTeamMembers === -1) return true;
    return currentCount < features.maxTeamMembers;
  }, [features]);

  const getUsagePercentage = useCallback((current: number, feature: 'maxProjects' | 'maxTeamMembers' | 'maxStorageGB') => {
    if (!features) return 0;
    const limit = features[feature];
    if (limit === -1) return 0; // Unlimited
    return Math.min((current / limit) * 100, 100);
  }, [features]);

  const isNearLimit = useCallback((current: number, feature: 'maxProjects' | 'maxTeamMembers' | 'maxStorageGB', threshold = 80) => {
    const percentage = getUsagePercentage(current, feature);
    return percentage >= threshold;
  }, [getUsagePercentage]);

  const isAtLimit = useCallback((current: number, feature: 'maxProjects' | 'maxTeamMembers' | 'maxStorageGB') => {
    if (!features) return true;
    const limit = features[feature];
    if (limit === -1) return false; // Unlimited
    return current >= limit;
  }, [features]);

  return {
    features,
    hasFeature,
    canCreateProject,
    canAddTeamMember,
    getUsagePercentage,
    isNearLimit,
    isAtLimit,
  };
}

/**
 * Hook for plan pricing calculations
 */
export function usePlanPricing() {
  const calculateAnnualSavings = useCallback((monthlyPrice: number) => {
    const discountPercentage = 20;
    const annualPrice = Math.round(monthlyPrice * 12 * (1 - discountPercentage / 100));
    const monthlySavings = Math.round((monthlyPrice * 12 - annualPrice) / 12);
    const totalSavings = monthlyPrice * 12 - annualPrice;
    
    return {
      annualPrice,
      monthlySavings,
      totalSavings,
      discountPercentage,
    };
  }, []);

  const formatPrice = useCallback((price: number, currency = 'NOK') => {
    return `${price.toLocaleString('nb-NO')} ${currency}`;
  }, []);

  const formatPriceWithPeriod = useCallback((price: number, period: 'month' | 'year', currency = 'NOK') => {
    const periodText = period === 'year' ? 'år' : 'måned';
    return `${formatPrice(price, currency)} per ${periodText}`;
  }, [formatPrice]);

  return {
    calculateAnnualSavings,
    formatPrice,
    formatPriceWithPeriod,
  };
}
