import { useEffect, useState } from 'react';
import { useSubscriptionRefresh } from '../contexts/SubscriptionRefreshContext';
import { useSubscriptionAccess } from './useSubscriptionAccess';

/**
 * Enhanced subscription access hook with real-time trial expiration detection
 * This hook provides all the functionality of useSubscriptionAccess but with
 * real-time updates for trial expiration, ensuring UI restrictions are applied
 * immediately when the trial period ends.
 */
export const useRealTimeSubscriptionAccess = () => {
  const subscriptionData = useSubscriptionAccess();

  // Make refresh context optional
  let forceRefresh = () => {};
  try {
    const refreshContext = useSubscriptionRefresh();
    forceRefresh = refreshContext.forceRefresh;
  } catch (error) {
    // SubscriptionRefreshProvider not available, continue without it
  }
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [lastRefresh, setLastRefresh] = useState(Date.now());

  // Update time every minute for real-time trial expiration detection
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  // Force refresh when returning from checkout (detect URL changes)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // User returned to tab - check if they might have completed checkout
        const now = Date.now();
        if (now - lastRefresh > 30000) { // Only refresh if it's been more than 30 seconds
          setLastRefresh(now);
          setCurrentTime(now);
        }
      }
    };

    const handleSubscriptionUpdate = (event: CustomEvent) => {
      console.log('🔄 Subscription update event received:', event.detail);
      const now = Date.now();
      setLastRefresh(now);
      setCurrentTime(now);
      // Force refresh of subscription queries
      forceRefresh();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('subscription-updated', handleSubscriptionUpdate as EventListener);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('subscription-updated', handleSubscriptionUpdate as EventListener);
    };
  }, [lastRefresh]);

  // Calculate real-time trial status
  const trialEnd = (subscriptionData.subscription as any)?.trialEnd || 0;
  const subscriptionStatus = (subscriptionData.subscription as any)?.status;

  // Enhanced logic: if subscription is active, don't show trial messages
  const hasActiveSubscription = subscriptionData.hasActiveSubscription || subscriptionStatus === 'active';
  const isRealTimeExpired = currentTime >= trialEnd && trialEnd > 0 && !hasActiveSubscription;
  const isStillCountingDown = currentTime < trialEnd && trialEnd > 0 && !hasActiveSubscription;
  const isInActiveTrial = subscriptionData.isInTrial && !isRealTimeExpired && !hasActiveSubscription;

  // Real-time feature access calculations
  const realTimeCanCreateProjects = hasActiveSubscription || isInActiveTrial;
  const realTimeCanAccessProjects = hasActiveSubscription || isInActiveTrial || subscriptionData.isInGracePeriod;
  const realTimeHasFullAccess = hasActiveSubscription || isInActiveTrial;
  const realTimeIsReadOnly = subscriptionData.isInGracePeriod || (isRealTimeExpired && !hasActiveSubscription);
  const realTimeNeedsUpgrade = !hasActiveSubscription && (isRealTimeExpired || !isInActiveTrial);

  // Override subscription access data with real-time calculations
  return {
    ...subscriptionData,

    // Enhanced subscription status - key fix!
    hasActiveSubscription,

    // Real-time overrides for trial expiration
    isTrialExpired: subscriptionData.isTrialExpired || (isRealTimeExpired && !hasActiveSubscription),
    isInTrial: isInActiveTrial,

    // Real-time feature access calculations
    canCreateProjects: realTimeCanCreateProjects,
    canAccessProjects: realTimeCanAccessProjects,
    hasFullAccess: realTimeHasFullAccess,
    isReadOnly: realTimeIsReadOnly,
    needsUpgrade: realTimeNeedsUpgrade,

    // Additional real-time calculated values
    currentTime,
    trialEnd,
    isRealTimeExpired,
    isStillCountingDown,

    // Combined status for easy use - CRITICAL FIX HERE!
    shouldShowTrialStatus: isInActiveTrial && !hasActiveSubscription,
    shouldShowExpiredStatus: isRealTimeExpired && !hasActiveSubscription,
    
    // Feature-specific access checks with real-time updates
    featureAccess: {
      createProject: realTimeCanCreateProjects,
      teamManagement: realTimeHasFullAccess,
      projectSharing: realTimeHasFullAccess,
      fileUpload: realTimeHasFullAccess,
      fullAccess: realTimeHasFullAccess,
      viewProjects: realTimeCanAccessProjects,
      readOnly: realTimeCanAccessProjects || realTimeIsReadOnly,
    }
  };
};

/**
 * Simplified hook for components that only need to check if a specific feature is available
 */
export const useFeatureAccess = (feature: 'create_project' | 'team_management' | 'project_sharing' | 'file_upload' | 'full_access' | 'view_projects' | 'read_only') => {
  const { featureAccess, isLoading, isRealTimeExpired, shouldShowExpiredStatus } = useRealTimeSubscriptionAccess();
  
  const featureMap = {
    create_project: featureAccess.createProject,
    team_management: featureAccess.teamManagement,
    project_sharing: featureAccess.projectSharing,
    file_upload: featureAccess.fileUpload,
    full_access: featureAccess.fullAccess,
    view_projects: featureAccess.viewProjects,
    read_only: featureAccess.readOnly,
  };

  return {
    hasAccess: featureMap[feature],
    isLoading,
    isRealTimeExpired,
    shouldShowExpiredStatus,
    restrictionReason: isRealTimeExpired ? 'trial_expired' : 'no_subscription'
  };
};
