import { useEffect, useState } from 'react';
import { useSubscriptionAccess } from './useSubscriptionAccess';

/**
 * Hook that provides real-time trial status updates
 * Updates every minute to ensure trial expiration is detected immediately
 */
export const useRealTimeTrialStatus = () => {
  const subscriptionData = useSubscriptionAccess();
  const [currentTime, setCurrentTime] = useState(Date.now());

  // Update time every minute for real-time trial expiration detection
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  // Calculate real-time trial status
  const trialEnd = (subscriptionData.subscription as any)?.trialEnd || 0;
  const isRealTimeExpired = currentTime >= trialEnd && trialEnd > 0;
  const isStillCountingDown = currentTime < trialEnd && trialEnd > 0;

  // Override subscription access data with real-time calculations
  const realTimeSubscriptionData = {
    ...subscriptionData,

    // Real-time overrides for trial expiration
    isTrialExpired: subscriptionData.isTrialExpired || isRealTimeExpired,
    isInTrial: subscriptionData.isInTrial && !isRealTimeExpired,

    // Real-time feature access calculations
    canCreateProjects: subscriptionData.hasActiveSubscription || (subscriptionData.isInTrial && !isRealTimeExpired),
    canAccessProjects: subscriptionData.hasActiveSubscription || (subscriptionData.isInTrial && !isRealTimeExpired) || subscriptionData.isInGracePeriod,
    hasFullAccess: subscriptionData.hasActiveSubscription || (subscriptionData.isInTrial && !isRealTimeExpired),
    isReadOnly: subscriptionData.isInGracePeriod || isRealTimeExpired,
    needsUpgrade: !subscriptionData.hasActiveSubscription && (isRealTimeExpired || !subscriptionData.isInTrial),

    // Additional real-time calculated values
    currentTime,
    trialEnd,
    isRealTimeExpired,
    isStillCountingDown,

    // Combined status for easy use
    shouldShowTrialStatus: (subscriptionData.isInTrial || isStillCountingDown) && !isRealTimeExpired,
    shouldShowExpiredStatus: isRealTimeExpired,
  };

  return realTimeSubscriptionData;
};
