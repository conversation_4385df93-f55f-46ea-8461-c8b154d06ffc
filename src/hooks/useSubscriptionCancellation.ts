import { useUser } from '@clerk/clerk-react';
import { useMutation, useQuery } from 'convex/react';
import { useCallback, useState } from 'react';
import { api } from '../../convex/_generated/api';

export interface CancellationFeedback {
  reason: string;
  details?: string;
  wouldRecommend: boolean;
  improvementSuggestions?: string;
}

export interface CancellationState {
  isLoading: boolean;
  error: string | null;
  isProcessing: boolean;
  showModal: boolean;
  step: 'reason' | 'retention' | 'confirmation';
  selectedReason: string;
  feedback: Partial<CancellationFeedback>;
}

export interface UseSubscriptionCancellationReturn {
  state: CancellationState;
  actions: {
    openCancellationFlow: () => void;
    closeCancellationFlow: () => void;
    setStep: (step: CancellationState['step']) => void;
    setSelectedReason: (reason: string) => void;
    updateFeedback: (feedback: Partial<CancellationFeedback>) => void;
    submitCancellation: (cancelImmediately: boolean) => Promise<void>;
    acceptRetentionOffer: (offerId: string) => Promise<void>;
  };
  queries: {
    canCancel: any;
    cancellationHistory: any;
  };
}

export function useSubscriptionCancellation(): UseSubscriptionCancellationReturn {
  const { user } = useUser();
  
  const [state, setState] = useState<CancellationState>({
    isLoading: false,
    error: null,
    isProcessing: false,
    showModal: false,
    step: 'reason',
    selectedReason: '',
    feedback: {},
  });

  // Convex queries and mutations
  const canCancel = useQuery(
    api.subscriptionCancellation?.canCancelSubscription,
    user ? { userId: user.id } : "skip"
  );
  
  const cancellationHistory = useQuery(
    api.subscriptionCancellation?.getCancellationHistory,
    user ? { userId: user.id } : "skip"
  );
  
  const initiateCancellation = useMutation(api.subscriptionCancellation?.initiateSubscriptionCancellation);

  // Actions
  const openCancellationFlow = useCallback(() => {
    setState(prev => ({
      ...prev,
      showModal: true,
      step: 'reason',
      selectedReason: '',
      feedback: {},
      error: null,
    }));
  }, []);

  const closeCancellationFlow = useCallback(() => {
    setState(prev => ({
      ...prev,
      showModal: false,
      step: 'reason',
      selectedReason: '',
      feedback: {},
      error: null,
      isProcessing: false,
    }));
  }, []);

  const setStep = useCallback((step: CancellationState['step']) => {
    setState(prev => ({ ...prev, step }));
  }, []);

  const setSelectedReason = useCallback((reason: string) => {
    setState(prev => ({ ...prev, selectedReason: reason }));
  }, []);

  const updateFeedback = useCallback((feedback: Partial<CancellationFeedback>) => {
    setState(prev => ({
      ...prev,
      feedback: { ...prev.feedback, ...feedback },
    }));
  }, []);

  const submitCancellation = useCallback(async (cancelImmediately: boolean) => {
    if (!user || !api.subscriptionCancellation?.initiateSubscriptionCancellation) return;

    setState(prev => ({ ...prev, isProcessing: true, error: null }));

    try {
      const feedback: CancellationFeedback = {
        reason: state.selectedReason,
        details: state.feedback.details,
        wouldRecommend: state.feedback.wouldRecommend ?? true,
        improvementSuggestions: state.feedback.improvementSuggestions,
      };

      const result = await initiateCancellation({
        userId: user.id,
        feedback,
        cancelImmediately,
      });

      if (result.status === 'initiated') {
        setState(prev => ({ ...prev, isProcessing: false, showModal: false }));
        
        // Show success message and refresh after a delay
        alert(result.message + ' Siden vil oppdateres om et øyeblikk.');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: 'Avbryting feilet. Prøv igjen senere.',
      }));
    }
  }, [user, state.selectedReason, state.feedback, initiateCancellation]);

  const acceptRetentionOffer = useCallback(async (offerId: string) => {
    setState(prev => ({ ...prev, isProcessing: true, error: null }));

    try {
      // Handle different retention offers
      switch (offerId) {
        case 'discount_25':
          // Apply 25% discount for 3 months
          console.log('Applying 25% discount for 3 months');
          alert('25% rabatt er aktivert for de neste 3 månedene!');
          break;
          
        case 'free_training':
          // Schedule free training session
          console.log('Scheduling free training session');
          alert('Vi vil kontakte deg for å avtale en gratis opplæringssesjon!');
          break;
          
        case 'downgrade_offer':
          // Redirect to plan downgrade
          console.log('Redirecting to plan downgrade');
          alert('Vi omdirigerer deg til planendring...');
          break;
          
        case 'pause_subscription':
          // Pause subscription
          console.log('Pausing subscription');
          alert('Abonnementet ditt er pauset. Du kan reaktivere det når som helst!');
          break;
          
        case 'feature_roadmap':
          // Add to priority feature development
          console.log('Adding to priority feature development');
          alert('Dine ønskede funksjoner er prioritert! Vi kontakter deg når de er klare.');
          break;
          
        case 'priority_support':
          // Provide priority support access
          console.log('Providing priority support access');
          alert('Du har nå prioritert teknisk støtte! Kontakt oss på <EMAIL>');
          break;
          
        default:
          console.log('Unknown retention offer:', offerId);
          alert('Tilbudet er aktivert! Vi kontakter deg med mer informasjon.');
      }

      // Close the cancellation flow
      setState(prev => ({ ...prev, isProcessing: false, showModal: false }));
      
    } catch (error) {
      console.error('Failed to accept retention offer:', error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: 'Kunne ikke aktivere tilbudet. Prøv igjen senere.',
      }));
    }
  }, []);

  return {
    state,
    actions: {
      openCancellationFlow,
      closeCancellationFlow,
      setStep,
      setSelectedReason,
      updateFeedback,
      submitCancellation,
      acceptRetentionOffer,
    },
    queries: {
      canCancel,
      cancellationHistory,
    },
  };
}

/**
 * Hook for cancellation analytics and insights
 */
export function useCancellationAnalytics() {
  const { user } = useUser();
  
  const cancellationHistory = useQuery(
    api.subscriptionCancellation?.getCancellationHistory,
    user ? { userId: user.id } : "skip"
  );

  const getCancellationStats = useCallback(() => {
    if (!cancellationHistory) return null;

    const totalCancellations = cancellationHistory.length;
    const reasonCounts = cancellationHistory.reduce((acc: Record<string, number>, cancellation: any) => {
      acc[cancellation.reason] = (acc[cancellation.reason] || 0) + 1;
      return acc;
    }, {});

    const mostCommonReason = Object.entries(reasonCounts)
      .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0];

    const recommendationRate = cancellationHistory.length > 0
      ? cancellationHistory.filter((c: any) => c.wouldRecommend).length / cancellationHistory.length
      : 0;

    return {
      totalCancellations,
      reasonCounts,
      mostCommonReason,
      recommendationRate,
    };
  }, [cancellationHistory]);

  const hasRecentCancellation = useCallback((withinDays = 30) => {
    if (!cancellationHistory) return false;
    
    const cutoffDate = Date.now() - (withinDays * 24 * 60 * 60 * 1000);
    return cancellationHistory.some((c: any) => c.createdAt > cutoffDate);
  }, [cancellationHistory]);

  return {
    cancellationHistory,
    getCancellationStats,
    hasRecentCancellation,
  };
}

/**
 * Hook for retention offer management
 */
export function useRetentionOffers() {
  const [acceptedOffers, setAcceptedOffers] = useState<string[]>([]);

  const markOfferAccepted = useCallback((offerId: string) => {
    setAcceptedOffers(prev => [...prev, offerId]);
  }, []);

  const hasAcceptedOffer = useCallback((offerId: string) => {
    return acceptedOffers.includes(offerId);
  }, [acceptedOffers]);

  const getOfferSuccessRate = useCallback(() => {
    // This would typically come from analytics data
    return {
      'discount_25': 0.65,
      'free_training': 0.78,
      'downgrade_offer': 0.45,
      'pause_subscription': 0.82,
      'feature_roadmap': 0.58,
      'priority_support': 0.71,
    };
  }, []);

  return {
    acceptedOffers,
    markOfferAccepted,
    hasAcceptedOffer,
    getOfferSuccessRate,
  };
}
