import { useUser } from '@clerk/clerk-react';
import { useMutation, useQuery } from 'convex/react';
import { useCallback, useState } from 'react';
import { api } from '../../convex/_generated/api';

export interface ReactivationState {
  isLoading: boolean;
  error: string | null;
  isProcessing: boolean;
  showModal: boolean;
  selectedPlan: string;
  selectedBillingInterval: 'month' | 'year';
}

export interface UseSubscriptionReactivationReturn {
  state: ReactivationState;
  actions: {
    openReactivationFlow: () => void;
    closeReactivationFlow: () => void;
    setSelectedPlan: (planId: string) => void;
    setSelectedBillingInterval: (interval: 'month' | 'year') => void;
    submitReactivation: (planId: string, billingInterval: 'month' | 'year', paymentMethodId?: string) => Promise<void>;
  };
  queries: {
    canReactivate: any;
    availablePlans: any;
    reactivationHistory: any;
  };
}

export function useSubscriptionReactivation(): UseSubscriptionReactivationReturn {
  const { user } = useUser();
  
  const [state, setState] = useState<ReactivationState>({
    isLoading: false,
    error: null,
    isProcessing: false,
    showModal: false,
    selectedPlan: '',
    selectedBillingInterval: 'month',
  });

  // Convex queries and mutations
  const canReactivate = useQuery(
    api.subscriptionReactivation?.canReactivateSubscription,
    user ? { userId: user.id } : "skip"
  );
  
  const availablePlans = useQuery(
    api.subscriptionReactivation?.getReactivationPlans,
    user ? { userId: user.id } : "skip"
  );
  
  const reactivationHistory = useQuery(
    api.subscriptionReactivation?.getReactivationHistory,
    user ? { userId: user.id } : "skip"
  );
  
  const initiateReactivation = useMutation(api.subscriptionReactivation?.initiateSubscriptionReactivation);

  // Actions
  const openReactivationFlow = useCallback(() => {
    setState(prev => ({
      ...prev,
      showModal: true,
      selectedPlan: '',
      selectedBillingInterval: 'month',
      error: null,
    }));
  }, []);

  const closeReactivationFlow = useCallback(() => {
    setState(prev => ({
      ...prev,
      showModal: false,
      selectedPlan: '',
      selectedBillingInterval: 'month',
      error: null,
      isProcessing: false,
    }));
  }, []);

  const setSelectedPlan = useCallback((planId: string) => {
    setState(prev => ({ ...prev, selectedPlan: planId }));
  }, []);

  const setSelectedBillingInterval = useCallback((interval: 'month' | 'year') => {
    setState(prev => ({ ...prev, selectedBillingInterval: interval }));
  }, []);

  const submitReactivation = useCallback(async (
    planId: string, 
    billingInterval: 'month' | 'year', 
    paymentMethodId?: string
  ) => {
    if (!user || !api.subscriptionReactivation?.initiateSubscriptionReactivation) return;

    setState(prev => ({ ...prev, isProcessing: true, error: null }));

    try {
      const result = await initiateReactivation({
        userId: user.id,
        planId,
        billingInterval,
        paymentMethodId,
      });

      if (result.status === 'initiated') {
        setState(prev => ({ ...prev, isProcessing: false, showModal: false }));
        
        // Show success message and refresh after a delay
        alert(result.message + ' Siden vil oppdateres om et øyeblikk.');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to reactivate subscription:', error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        error: 'Reaktivering feilet. Prøv igjen senere.',
      }));
    }
  }, [user, initiateReactivation]);

  return {
    state,
    actions: {
      openReactivationFlow,
      closeReactivationFlow,
      setSelectedPlan,
      setSelectedBillingInterval,
      submitReactivation,
    },
    queries: {
      canReactivate,
      availablePlans,
      reactivationHistory,
    },
  };
}

/**
 * Hook for reactivation analytics and insights
 */
export function useReactivationAnalytics() {
  const { user } = useUser();
  
  const reactivationHistory = useQuery(
    api.subscriptionReactivation?.getReactivationHistory,
    user ? { userId: user.id } : "skip"
  );

  const getReactivationStats = useCallback(() => {
    if (!reactivationHistory) return null;

    const totalReactivations = reactivationHistory.length;
    const successfulReactivations = reactivationHistory.filter((r: any) => r.status === 'completed').length;
    const failedReactivations = reactivationHistory.filter((r: any) => r.status === 'failed').length;
    
    const successRate = totalReactivations > 0 ? (successfulReactivations / totalReactivations) * 100 : 0;

    const planChoices = reactivationHistory.reduce((acc: Record<string, number>, reactivation: any) => {
      if (reactivation.status === 'completed') {
        acc[reactivation.newPlanId] = (acc[reactivation.newPlanId] || 0) + 1;
      }
      return acc;
    }, {});

    const mostChosenPlan = Object.entries(planChoices)
      .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0];

    return {
      totalReactivations,
      successfulReactivations,
      failedReactivations,
      successRate,
      planChoices,
      mostChosenPlan,
    };
  }, [reactivationHistory]);

  const hasRecentReactivation = useCallback((withinDays = 30) => {
    if (!reactivationHistory) return false;
    
    const cutoffDate = Date.now() - (withinDays * 24 * 60 * 60 * 1000);
    return reactivationHistory.some((r: any) => r.createdAt > cutoffDate);
  }, [reactivationHistory]);

  const getLastReactivation = useCallback(() => {
    if (!reactivationHistory || reactivationHistory.length === 0) return null;
    return reactivationHistory[0]; // Most recent first due to order("desc")
  }, [reactivationHistory]);

  return {
    reactivationHistory,
    getReactivationStats,
    hasRecentReactivation,
    getLastReactivation,
  };
}

/**
 * Hook for checking reactivation eligibility
 */
export function useReactivationEligibility() {
  const { user } = useUser();
  
  const canReactivate = useQuery(
    api.subscriptionReactivation?.canReactivateSubscription,
    user ? { userId: user.id } : "skip"
  );

  const isEligible = canReactivate?.canReactivate || false;
  const reason = canReactivate?.reason;
  const cancelledSubscription = canReactivate?.subscription;

  const getDataRetentionStatus = useCallback(() => {
    if (!cancelledSubscription) return null;

    const dataRetentionUntil = new Date(cancelledSubscription.dataRetentionUntil);
    const now = new Date();
    const isExpired = now > dataRetentionUntil;
    const daysRemaining = Math.max(0, Math.ceil((dataRetentionUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));

    return {
      isExpired,
      daysRemaining,
      expiryDate: dataRetentionUntil,
      isNearExpiry: daysRemaining <= 7 && daysRemaining > 0,
    };
  }, [cancelledSubscription]);

  const getReactivationBenefits = useCallback(() => {
    const dataStatus = getDataRetentionStatus();
    
    return {
      welcomeBackDiscount: 15, // 15% discount on first billing period
      dataPreservation: !dataStatus?.isExpired,
      immediateAccess: true,
      noSetupFee: true,
      teamAccessRestoration: true,
    };
  }, [getDataRetentionStatus]);

  return {
    isEligible,
    reason,
    cancelledSubscription,
    getDataRetentionStatus,
    getReactivationBenefits,
  };
}

/**
 * Hook for reactivation onboarding and guidance
 */
export function useReactivationOnboarding() {
  const [hasSeenWelcomeBack, setHasSeenWelcomeBack] = useState(false);
  const [currentStep, setCurrentStep] = useState<'welcome' | 'plan-selection' | 'payment' | 'confirmation'>('welcome');

  const markWelcomeBackSeen = useCallback(() => {
    setHasSeenWelcomeBack(true);
    localStorage.setItem('jobblogg-reactivation-welcome-seen', 'true');
  }, []);

  const nextStep = useCallback(() => {
    setCurrentStep(prev => {
      switch (prev) {
        case 'welcome': return 'plan-selection';
        case 'plan-selection': return 'payment';
        case 'payment': return 'confirmation';
        default: return prev;
      }
    });
  }, []);

  const previousStep = useCallback(() => {
    setCurrentStep(prev => {
      switch (prev) {
        case 'confirmation': return 'payment';
        case 'payment': return 'plan-selection';
        case 'plan-selection': return 'welcome';
        default: return prev;
      }
    });
  }, []);

  const resetOnboarding = useCallback(() => {
    setCurrentStep('welcome');
    setHasSeenWelcomeBack(false);
  }, []);

  // Check if user has seen welcome back message
  React.useEffect(() => {
    const seen = localStorage.getItem('jobblogg-reactivation-welcome-seen');
    if (seen === 'true') {
      setHasSeenWelcomeBack(true);
    }
  }, []);

  return {
    hasSeenWelcomeBack,
    currentStep,
    markWelcomeBackSeen,
    nextStep,
    previousStep,
    resetOnboarding,
  };
}
