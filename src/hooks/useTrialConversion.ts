/**
 * useTrialConversion Hook
 * 
 * Manages trial-to-paid conversion logic with automatic triggering
 * based on trial status, expiration timing, and user behavior.
 * 
 * Features:
 * - Automatic conversion flow triggering
 * - Trial expiration monitoring
 * - Grace period handling
 * - Conversion state management
 * - Norwegian localization support
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useSubscriptionAccess } from './useSubscriptionAccess';
import { useUserRole } from './useUserRole';
import { formatTimeRemaining } from '../utils/timeFormatting';

export type ConversionTrigger = 'expiring_soon' | 'expired' | 'grace_period' | 'early_upgrade' | 'manual';

export interface TrialConversionState {
  shouldShowConversion: boolean;
  trigger: ConversionTrigger;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  timeRemaining: string;
  daysLeft: number;
  isAutoTriggered: boolean;
}

export interface UseTrialConversionOptions {
  /** Auto-trigger conversion flow based on trial status */
  autoTrigger?: boolean;
  /** Days before expiration to show "expiring soon" */
  expiringSoonDays?: number;
  /** Enable grace period conversion */
  enableGracePeriod?: boolean;
  /** Only show for administrators */
  adminOnly?: boolean;
}

export interface UseTrialConversionReturn {
  conversionState: TrialConversionState;
  triggerConversion: (trigger?: ConversionTrigger) => void;
  dismissConversion: () => void;
  resetConversion: () => void;
  canTriggerConversion: boolean;
}

/**
 * Hook for managing trial conversion flows
 */
export function useTrialConversion(options: UseTrialConversionOptions = {}): UseTrialConversionReturn {
  const {
    autoTrigger = true,
    expiringSoonDays = 2,
    enableGracePeriod = true,
    adminOnly = false,
  } = options;

  const { subscription, isInTrial, isTrialExpired, isInGracePeriod } = useSubscriptionAccess();
  const { isAdministrator } = useUserRole();
  
  const [manualTrigger, setManualTrigger] = useState<ConversionTrigger | null>(null);
  const [isDismissed, setIsDismissed] = useState(false);

  // Calculate trial timing
  const trialEnd = (subscription as any)?.trialEnd || 0;
  const now = Date.now();
  const timeUntilExpiration = trialEnd - now;
  const daysLeft = Math.max(0, Math.ceil(timeUntilExpiration / (24 * 60 * 60 * 1000)));
  const timeRemaining = formatTimeRemaining(trialEnd);

  // Determine if user can trigger conversion
  const canTriggerConversion = useMemo(() => {
    if (!subscription) return false;
    if (adminOnly && !isAdministrator) return false;
    return isInTrial || isTrialExpired || isInGracePeriod;
  }, [subscription, adminOnly, isAdministrator, isInTrial, isTrialExpired, isInGracePeriod]);

  // Determine conversion trigger and urgency
  const getConversionState = useCallback((): TrialConversionState => {
    // Manual trigger takes precedence
    if (manualTrigger) {
      return {
        shouldShowConversion: true,
        trigger: manualTrigger,
        urgency: manualTrigger === 'grace_period' ? 'critical' : 'medium',
        timeRemaining: timeRemaining.text,
        daysLeft,
        isAutoTriggered: false,
      };
    }

    // Don't auto-trigger if dismissed or auto-trigger disabled
    if (isDismissed || !autoTrigger || !canTriggerConversion) {
      return {
        shouldShowConversion: false,
        trigger: 'manual',
        urgency: 'low',
        timeRemaining: timeRemaining.text,
        daysLeft,
        isAutoTriggered: false,
      };
    }

    // Grace period - critical urgency
    if (enableGracePeriod && isInGracePeriod) {
      return {
        shouldShowConversion: true,
        trigger: 'grace_period',
        urgency: 'critical',
        timeRemaining: timeRemaining.text,
        daysLeft,
        isAutoTriggered: true,
      };
    }

    // Trial expired - high urgency
    if (isTrialExpired) {
      return {
        shouldShowConversion: true,
        trigger: 'expired',
        urgency: 'high',
        timeRemaining: timeRemaining.text,
        daysLeft,
        isAutoTriggered: true,
      };
    }

    // Trial expiring soon - medium urgency
    if (isInTrial && daysLeft <= expiringSoonDays) {
      return {
        shouldShowConversion: true,
        trigger: 'expiring_soon',
        urgency: daysLeft <= 1 ? 'high' : 'medium',
        timeRemaining: timeRemaining.text,
        daysLeft,
        isAutoTriggered: true,
      };
    }

    // Default state - no conversion needed
    return {
      shouldShowConversion: false,
      trigger: 'manual',
      urgency: 'low',
      timeRemaining: timeRemaining.text,
      daysLeft,
      isAutoTriggered: false,
    };
  }, [
    manualTrigger,
    isDismissed,
    autoTrigger,
    canTriggerConversion,
    enableGracePeriod,
    isInGracePeriod,
    isTrialExpired,
    isInTrial,
    daysLeft,
    expiringSoonDays,
    timeRemaining.text,
  ]);

  const conversionState = getConversionState();

  // Manual trigger function
  const triggerConversion = useCallback((trigger: ConversionTrigger = 'manual') => {
    if (!canTriggerConversion) {
      console.warn('Cannot trigger conversion - user does not have permission or valid subscription');
      return;
    }
    
    setManualTrigger(trigger);
    setIsDismissed(false);
  }, [canTriggerConversion]);

  // Dismiss conversion
  const dismissConversion = useCallback(() => {
    setIsDismissed(true);
    setManualTrigger(null);
  }, []);

  // Reset conversion state
  const resetConversion = useCallback(() => {
    setManualTrigger(null);
    setIsDismissed(false);
  }, []);

  // Auto-reset dismissal for critical states
  useEffect(() => {
    if (isDismissed && (isInGracePeriod || (isTrialExpired && daysLeft === 0))) {
      // Don't allow dismissal of critical states
      setIsDismissed(false);
    }
  }, [isDismissed, isInGracePeriod, isTrialExpired, daysLeft]);

  // Log conversion state changes for debugging
  useEffect(() => {
    if (conversionState.shouldShowConversion) {
      console.log('🔄 Trial conversion triggered:', {
        trigger: conversionState.trigger,
        urgency: conversionState.urgency,
        timeRemaining: conversionState.timeRemaining,
        daysLeft: conversionState.daysLeft,
        isAutoTriggered: conversionState.isAutoTriggered,
      });
    }
  }, [conversionState]);

  return {
    conversionState,
    triggerConversion,
    dismissConversion,
    resetConversion,
    canTriggerConversion,
  };
}

/**
 * Utility function to get conversion messaging based on trigger
 */
export function getConversionMessaging(trigger: ConversionTrigger, timeRemaining: string) {
  switch (trigger) {
    case 'expiring_soon':
      return {
        title: 'Prøveperioden utløper snart',
        subtitle: `${timeRemaining} igjen`,
        message: 'Fortsett med full tilgang ved å velge en plan som passer for din bedrift.',
        ctaText: 'Fortsett med betalt plan',
      };
    
    case 'expired':
      return {
        title: 'Prøveperioden er utløpt',
        subtitle: 'Velg en plan for å fortsette',
        message: 'Din prøveperiode er utløpt. Velg en plan for å få tilbake full tilgang.',
        ctaText: 'Aktiver betalt plan',
      };
    
    case 'grace_period':
      return {
        title: 'Siste sjanse - Kontoen stenges snart',
        subtitle: `${timeRemaining} til kontoen stenges`,
        message: 'Du er i en 3-dagers nådeperiode. Aktiver en betalt plan nå for å unngå at kontoen stenges.',
        ctaText: 'Aktiver plan nå',
      };
    
    case 'early_upgrade':
      return {
        title: 'Oppgrader til betalt plan',
        subtitle: `${timeRemaining} igjen av prøveperioden`,
        message: 'Oppgrader når som helst for å sikre kontinuerlig tilgang til alle funksjoner.',
        ctaText: 'Oppgrader nå',
      };
    
    default:
      return {
        title: 'Velg din plan',
        subtitle: 'Fortsett med JobbLogg',
        message: 'Velg planen som passer best for din bedrift.',
        ctaText: 'Velg plan',
      };
  }
}
