import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../convex/_generated/api';
import { useEffect, useState } from 'react';

/**
 * Hook to get current user's role and team information
 * Returns user role (administrator/utfoerende) and team context
 * Automatically creates user record if it doesn't exist
 */
export const useUserRole = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const [hasTriedCreatingUser, setHasTriedCreatingUser] = useState(false);
  const [isCreatingUser, setIsCreatingUser] = useState(false);

  // Mutation to create user if it doesn't exist
  const getOrCreateUserSafe = useMutation(api.contractorOnboardingSafe.getOrCreateUserSafe);

  // Only query when user is loaded and authenticated
  const shouldQuery = isClerkLoaded && user?.id;

  // Get user with role information
  const userWithRole = useQuery(
    api.teamManagement.getUserWithRole,
    shouldQuery ? { clerkUserId: user.id } : "skip"
  );

  // Handle case where user doesn't exist in database yet
  useEffect(() => {
    if (shouldQuery && userWithRole === null && !hasTriedCreatingUser && !isCreatingUser) {
      console.log('User not found in database, attempting to create user record');
      setHasTriedCreatingUser(true);
      setIsCreatingUser(true);

      getOrCreateUserSafe({ clerkUserId: user.id })
        .then((result) => {
          if (result.authError) {
            console.error('Failed to create user record:', result.error);
          } else {
            console.log('User record created/retrieved successfully');
          }
        })
        .catch((error) => {
          console.error('Failed to create user record:', error);
        })
        .finally(() => {
          setIsCreatingUser(false);
        });
    }
  }, [shouldQuery, userWithRole, hasTriedCreatingUser, isCreatingUser, user?.id, getOrCreateUserSafe]);

  // Debug logging
  console.log('🔍 [useUserRole] Hook state:', {
    isClerkLoaded,
    hasUser: !!user?.id,
    userId: user?.id,
    userWithRole: userWithRole ? 'found' : userWithRole === null ? 'null' : 'undefined',
    role: userWithRole?.role,
    isAdministrator: userWithRole?.role === "administrator",
    hasTriedCreatingUser,
    isCreatingUser
  });

  return {
    isLoading: !isClerkLoaded || userWithRole === undefined || isCreatingUser,
    user: userWithRole,
    role: userWithRole?.role || null,
    isAdministrator: userWithRole?.role === "administrator",
    isProsjektleder: userWithRole?.role === "prosjektleder",
    isUtfoerende: userWithRole?.role === "utfoerende",
    contractorCompanyId: userWithRole?.contractorCompanyId || null,
    hasCompletedOnboarding: userWithRole?.contractorCompleted || false,
  };
};

/**
 * Hook to get team members (for administrators and prosjektleder)
 */
export const useTeamMembers = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator, isProsjektleder, hasCompletedOnboarding } = useUserRole();

  // Only query if user is administrator or prosjektleder AND has completed onboarding
  const shouldQuery = isClerkLoaded && user?.id && (isAdministrator || isProsjektleder) && hasCompletedOnboarding;

  // For now, return empty array if conditions aren't met
  // This avoids the "user not found" error until the user completes onboarding
  const teamMembers = shouldQuery ? useQuery(
    api.teamManagement.getTeamMembers,
    { clerkUserId: user.id }
  ) : [];

  return {
    isLoading: shouldQuery && teamMembers === undefined,
    teamMembers: Array.isArray(teamMembers) ? teamMembers : [],
    canManageTeam: isAdministrator && hasCompletedOnboarding,
    canViewTeam: (isAdministrator || isProsjektleder) && hasCompletedOnboarding,
  };
};

/**
 * Hook to get pending invitations (only for administrators)
 */
export const usePendingInvitations = () => {
  const { user, isLoaded: isClerkLoaded } = useUser();
  const { isAdministrator, hasCompletedOnboarding } = useUserRole();

  // Only query if user is administrator AND has completed onboarding
  const shouldQuery = isClerkLoaded && user?.id && isAdministrator && hasCompletedOnboarding;

  // For now, return empty array if conditions aren't met
  // This avoids the "user not found" error until the user completes onboarding
  const pendingInvitations = shouldQuery ? useQuery(
    api.teamManagement.getPendingInvitations,
    { clerkUserId: user.id }
  ) : [];

  return {
    isLoading: shouldQuery && pendingInvitations === undefined,
    invitations: Array.isArray(pendingInvitations) ? pendingInvitations : [],
    canManageInvitations: isAdministrator && hasCompletedOnboarding,
  };
};

/**
 * Hook to get team workload overview (only for administrators)
 */
export const useTeamWorkload = () => {
  // const { user, isLoaded: isClerkLoaded } = useUser();
  // const { isAdministrator } = useUserRole();

  // Only query if user is administrator
  // const shouldQuery = isClerkLoaded && user?.id && isAdministrator;

  // const workloadData = useQuery(
  //   api.teamManagement.getTeamWorkloadOverview as any,
  //   shouldQuery ? { requestedBy: user.id } : "skip"
  // );
  // const workloadData = null; // Temporarily provide fallback - unused

  return {
    isLoading: false, // shouldQuery && workloadData === undefined, // Disabled due to type issues
    workload: null, // workloadData, // Disabled due to type issues
    canViewWorkload: false, // isAdministrator, // Disabled due to type issues
  };
};

/**
 * Hook to check if current user can access team management features
 */
export const useCanManageTeam = () => {
  const { isAdministrator, isLoading } = useUserRole();

  return {
    canManageTeam: isAdministrator,
    isLoading,
  };
};
