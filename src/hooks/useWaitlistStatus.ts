import { useState, useEffect } from 'react';
import { useClerk } from '@clerk/clerk-react';

interface WaitlistStatus {
  isWaitlistEnabled: boolean;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook to check if waitlist is enabled in the current Clerk instance
 * This determines whether to show regular sign-up or waitlist form
 *
 * Since you've changed from waitlist to public mode, we'll default to public
 * and add a simple environment variable override if needed.
 */
export const useWaitlistStatus = (): WaitlistStatus => {
  const [status, setStatus] = useState<WaitlistStatus>({
    isWaitlistEnabled: false,
    isLoading: true,
    error: null,
  });

  const clerk = useClerk();

  useEffect(() => {
    const checkWaitlistStatus = async () => {
      try {
        if (!clerk.loaded) {
          return; // Wait for Clerk to load
        }

        // Check for environment variable override first
        const forceWaitlist = import.meta.env.VITE_FORCE_WAITLIST === 'true';

        if (forceWaitlist) {
          console.log('🔍 Waitlist forced via VITE_FORCE_WAITLIST environment variable');
          setStatus({
            isWaitlistEnabled: true,
            isLoading: false,
            error: null,
          });
          return;
        }

        // Try to access Clerk's environment configuration
        try {
          // Method 1: Check Clerk's internal environment settings
          const environment = (clerk as any).__unstable__environment;
          if (environment?.userSettings?.signUp?.mode) {
            const isWaitlist = environment.userSettings.signUp.mode === 'waitlist';
            console.log('🔍 Clerk environment mode detected:', environment.userSettings.signUp.mode);
            setStatus({
              isWaitlistEnabled: isWaitlist,
              isLoading: false,
              error: null,
            });
            return;
          }

          // Method 2: Check if sign-up is restricted by trying to access sign-up methods
          const signUpMethods = clerk.client?.signUp;
          if (!signUpMethods) {
            // If no sign-up methods available, likely waitlist mode
            console.log('🔍 No sign-up methods available - likely waitlist mode');
            setStatus({
              isWaitlistEnabled: true,
              isLoading: false,
              error: null,
            });
            return;
          }

          // Method 3: Check for waitlist-specific methods
          const hasJoinWaitlist = typeof clerk.joinWaitlist === 'function';
          const hasCreateSignUp = typeof signUpMethods.create === 'function';

          console.log('🔍 Clerk methods check:', {
            hasJoinWaitlist,
            hasCreateSignUp,
            clerkMethods: Object.keys(clerk).filter(key => typeof (clerk as any)[key] === 'function')
          });

          // If joinWaitlist exists but create doesn't, it's likely waitlist mode
          if (hasJoinWaitlist && !hasCreateSignUp) {
            setStatus({
              isWaitlistEnabled: true,
              isLoading: false,
              error: null,
            });
            return;
          }

          // Default to public sign-up if we can't determine otherwise
          console.log('🔍 Defaulting to public sign-up mode');
          setStatus({
            isWaitlistEnabled: false,
            isLoading: false,
            error: null,
          });

        } catch (innerError) {
          console.log('🔍 Error checking Clerk configuration, defaulting to public:', innerError);
          setStatus({
            isWaitlistEnabled: false,
            isLoading: false,
            error: null,
          });
        }

      } catch (error) {
        console.error('Error checking waitlist status:', error);
        setStatus({
          isWaitlistEnabled: false, // Default to public sign-up on error
          isLoading: false,
          error: 'Kunne ikke sjekke waitlist-status',
        });
      }
    };

    checkWaitlistStatus();
  }, [clerk.loaded]);

  return status;
};
