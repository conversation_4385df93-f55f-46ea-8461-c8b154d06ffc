/**
 * Checkout Utilities
 * 
 * Utility functions for processing checkout-related URL parameters,
 * validating session data, and handling checkout flow logic.
 */

/**
 * Checkout session parameters from URL
 */
export interface CheckoutSessionParams {
  sessionId: string | null;
  success: boolean;
  canceled: boolean;
}

/**
 * Validation result for checkout parameters
 */
export interface CheckoutParamValidation {
  isValid: boolean;
  errors: string[];
  sessionId?: string;
}

/**
 * Extract checkout session parameters from URL search params
 */
export function extractCheckoutParams(searchParams: URLSearchParams): CheckoutSessionParams {
  const sessionId = searchParams.get('session_id');
  const success = searchParams.has('success') || window.location.pathname.includes('/success');
  const canceled = searchParams.has('canceled') || window.location.pathname.includes('/cancel');

  return {
    sessionId,
    success,
    canceled,
  };
}

/**
 * Validate Stripe session ID format
 */
export function validateSessionId(sessionId: string | null): CheckoutParamValidation {
  const errors: string[] = [];

  if (!sessionId) {
    errors.push('Session ID is required');
    return { isValid: false, errors };
  }

  if (typeof sessionId !== 'string') {
    errors.push('Session ID must be a string');
    return { isValid: false, errors };
  }

  // Stripe checkout session IDs start with 'cs_'
  if (!sessionId.startsWith('cs_')) {
    errors.push('Invalid session ID format - must start with "cs_"');
    return { isValid: false, errors };
  }

  // Basic length validation (Stripe session IDs are typically 60+ characters)
  if (sessionId.length < 20) {
    errors.push('Session ID appears to be too short');
    return { isValid: false, errors };
  }

  // Check for valid characters (alphanumeric and underscores)
  if (!/^cs_[a-zA-Z0-9_]+$/.test(sessionId)) {
    errors.push('Session ID contains invalid characters');
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    sessionId,
  };
}

/**
 * Validate checkout success URL parameters
 */
export function validateSuccessParams(searchParams: URLSearchParams): CheckoutParamValidation {
  const sessionId = searchParams.get('session_id');
  return validateSessionId(sessionId);
}

/**
 * Validate checkout cancel URL parameters
 */
export function validateCancelParams(searchParams: URLSearchParams): {
  isValid: boolean;
  errors: string[];
  hasSessionId: boolean;
  sessionId?: string;
} {
  const sessionId = searchParams.get('session_id');
  const hasSessionId = sessionId !== null;

  // For cancel pages, session ID is optional but if present should be valid
  if (hasSessionId) {
    const validation = validateSessionId(sessionId);
    return {
      isValid: validation.isValid,
      errors: validation.errors,
      hasSessionId: true,
      sessionId: validation.sessionId,
    };
  }

  return {
    isValid: true,
    errors: [],
    hasSessionId: false,
  };
}

/**
 * Parse plan and billing information from URL parameters
 */
export function parsePlanParams(searchParams: URLSearchParams): {
  planLevel?: 'basic' | 'professional' | 'enterprise';
  billingInterval?: 'month' | 'year';
  context?: string;
} {
  const plan = searchParams.get('plan');
  const billing = searchParams.get('billing');
  const context = searchParams.get('context');

  let planLevel: 'basic' | 'professional' | 'enterprise' | undefined;
  let billingInterval: 'month' | 'year' | undefined;

  // Validate plan level
  if (plan && ['basic', 'professional', 'enterprise'].includes(plan)) {
    planLevel = plan as 'basic' | 'professional' | 'enterprise';
  }

  // Validate billing interval
  if (billing && ['month', 'year'].includes(billing)) {
    billingInterval = billing as 'month' | 'year';
  }

  return {
    planLevel,
    billingInterval,
    context: context || undefined,
  };
}

/**
 * Generate checkout success URL with session ID
 */
export function generateSuccessUrl(baseUrl: string, sessionId?: string): string {
  const url = new URL('/checkout/success', baseUrl);
  if (sessionId) {
    url.searchParams.set('session_id', sessionId);
  }
  return url.toString();
}

/**
 * Generate checkout cancel URL with optional parameters
 */
export function generateCancelUrl(
  baseUrl: string, 
  params?: {
    plan?: string;
    billing?: string;
    context?: string;
  }
): string {
  const url = new URL('/checkout/cancel', baseUrl);
  
  if (params?.plan) {
    url.searchParams.set('plan', params.plan);
  }
  
  if (params?.billing) {
    url.searchParams.set('billing', params.billing);
  }
  
  if (params?.context) {
    url.searchParams.set('context', params.context);
  }
  
  return url.toString();
}

/**
 * Clean and normalize URL parameters for checkout
 */
export function normalizeCheckoutParams(searchParams: URLSearchParams): URLSearchParams {
  const normalized = new URLSearchParams();

  // Copy and validate known parameters
  const sessionId = searchParams.get('session_id');
  if (sessionId && validateSessionId(sessionId).isValid) {
    normalized.set('session_id', sessionId);
  }

  const plan = searchParams.get('plan');
  if (plan && ['basic', 'professional', 'enterprise'].includes(plan)) {
    normalized.set('plan', plan);
  }

  const billing = searchParams.get('billing');
  if (billing && ['month', 'year'].includes(billing)) {
    normalized.set('billing', billing);
  }

  const context = searchParams.get('context');
  if (context) {
    normalized.set('context', context);
  }

  return normalized;
}

/**
 * Get user-friendly error messages in Norwegian
 */
export function getCheckoutErrorMessage(errors: string[]): string {
  if (errors.length === 0) return '';

  const norwegianMessages: Record<string, string> = {
    'Session ID is required': 'Betalings-ID mangler',
    'Session ID must be a string': 'Ugyldig betalings-ID format',
    'Invalid session ID format - must start with "cs_"': 'Ugyldig betalings-ID format',
    'Session ID appears to be too short': 'Betalings-ID er for kort',
    'Session ID contains invalid characters': 'Betalings-ID inneholder ugyldige tegn',
  };

  const norwegianErrors = errors.map(error => 
    norwegianMessages[error] || 'Ukjent feil med betalings-ID'
  );

  return norwegianErrors.join(', ');
}

/**
 * Check if current URL is a checkout-related page
 */
export function isCheckoutPage(pathname: string): boolean {
  return pathname.startsWith('/checkout/') || 
         pathname === '/checkout' ||
         pathname.includes('success') ||
         pathname.includes('cancel');
}

/**
 * Extract checkout context from URL and referrer
 */
export function getCheckoutContext(): {
  isCheckoutFlow: boolean;
  pageType: 'success' | 'cancel' | 'other';
  referrer?: string;
} {
  const pathname = window.location.pathname;
  const referrer = document.referrer;

  const isCheckoutFlow = isCheckoutPage(pathname);

  let pageType: 'success' | 'cancel' | 'other' = 'other';
  if (pathname.includes('/success')) {
    pageType = 'success';
  } else if (pathname.includes('/cancel')) {
    pageType = 'cancel';
  }

  return {
    isCheckoutFlow,
    pageType,
    referrer: referrer || undefined,
  };
}

/**
 * Determine checkout context based on subscription state and trial days
 */
export function determineCheckoutContext(
  subscriptionStatus: string | undefined,
  isInTrial: boolean,
  isTrialExpired: boolean,
  hasStripeCustomer: boolean,
  trialDays?: number
): 'trial_setup' | 'trial_conversion' | 'plan_upgrade' {
  // If trialDays is specified, use it to determine context
  if (trialDays !== undefined) {
    if (trialDays > 0) {
      return 'trial_setup'; // New user starting trial
    } else {
      return 'trial_conversion'; // Converting from trial to paid
    }
  }

  // Determine based on subscription state
  if (subscriptionStatus === 'active' && !isInTrial && !isTrialExpired) {
    return 'plan_upgrade'; // Existing paid customer changing plans
  }

  if (isTrialExpired || subscriptionStatus === 'past_due') {
    return 'trial_conversion'; // Trial expired, needs to convert
  }

  if (isInTrial && subscriptionStatus === 'trialing') {
    return 'trial_conversion'; // Active trial user converting
  }

  if (!hasStripeCustomer && !isInTrial && !isTrialExpired) {
    return 'trial_setup'; // New user, no trial started yet
  }

  // Default fallback
  return 'trial_conversion';
}

/**
 * Utility to safely parse JSON from URL parameters
 */
export function parseJsonParam(param: string | null): any {
  if (!param) return null;
  
  try {
    return JSON.parse(decodeURIComponent(param));
  } catch (error) {
    console.warn('Failed to parse JSON parameter:', param);
    return null;
  }
}
