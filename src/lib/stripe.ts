/**
 * Stripe.js Frontend Integration for JobbLogg
 * 
 * This module provides frontend Stripe integration using @stripe/stripe-js
 * for secure payment processing and checkout flows.
 * 
 * Features:
 * - Stripe instance initialization with publishable key
 * - Environment-aware configuration (development/production)
 * - Error handling for Stripe loading failures
 * - TypeScript support with proper typing
 * - Norwegian localization support
 */

import { loadStripe, Stripe } from '@stripe/stripe-js';

// Stripe instance cache to avoid multiple initializations
let stripePromise: Promise<Stripe | null> | null = null;

/**
 * Environment configuration for Stripe publishable keys
 */
const STRIPE_CONFIG = {
  development: {
    publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 'pk_test_your_publishable_key_here',
  },
  production: {
    publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY_LIVE || 'pk_live_your_publishable_key_here',
  },
} as const;

/**
 * Get the appropriate Stripe publishable key based on environment
 */
function getStripePublishableKey(): string {
  const isDevelopment = import.meta.env.DEV || import.meta.env.MODE === 'development';
  const config = isDevelopment ? STRIPE_CONFIG.development : STRIPE_CONFIG.production;
  
  const publishableKey = config.publishableKey;
  
  // Validate that the key is properly configured
  if (!publishableKey || publishableKey.includes('your_publishable_key_here')) {
    const envVar = isDevelopment ? 'VITE_STRIPE_PUBLISHABLE_KEY' : 'VITE_STRIPE_PUBLISHABLE_KEY_LIVE';
    throw new Error(
      `Stripe publishable key not configured. Please set ${envVar} in your environment variables.`
    );
  }
  
  return publishableKey;
}

/**
 * Initialize and return Stripe instance
 * 
 * This function loads Stripe.js with the appropriate publishable key
 * and caches the promise to avoid multiple initializations.
 * 
 * @returns Promise that resolves to Stripe instance or null if loading fails
 */
export function getStripe(): Promise<Stripe | null> {
  if (!stripePromise) {
    try {
      const publishableKey = getStripePublishableKey();
      
      console.log('🔄 Initializing Stripe.js with publishable key:', 
        publishableKey.substring(0, 12) + '...' + publishableKey.slice(-4)
      );
      
      stripePromise = loadStripe(publishableKey, {
        // Norwegian localization
        locale: 'nb',
      });
      
    } catch (error) {
      console.error('❌ Error initializing Stripe:', error);
      stripePromise = Promise.resolve(null);
    }
  }
  
  return stripePromise;
}

/**
 * Reset Stripe instance (useful for testing or environment changes)
 */
export function resetStripe(): void {
  stripePromise = null;
  console.log('🔄 Stripe instance reset');
}

/**
 * Validate Stripe instance and throw error if not available
 * 
 * @param stripe - Stripe instance to validate
 * @throws Error if Stripe is not available
 */
export function validateStripe(stripe: Stripe | null): asserts stripe is Stripe {
  if (!stripe) {
    throw new Error(
      'Stripe.js failed to load. Please check your internet connection and try again.'
    );
  }
}

/**
 * Stripe configuration constants
 */
export const STRIPE_CONSTANTS = {
  // Supported payment methods for Norwegian customers
  PAYMENT_METHODS: ['card', 'klarna'] as const,
  
  // Norwegian currency
  CURRENCY: 'nok' as const,
  
  // Checkout session configuration
  CHECKOUT: {
    // Billing address collection for Norwegian tax requirements
    billingAddressCollection: 'required' as const,
    
    // Allow promotion codes
    allowPromotionCodes: true,
    
    // Automatic tax calculation
    automaticTax: {
      enabled: true,
    },
    
    // Customer creation for subscription management
    customerCreation: 'always' as const,
    
    // Norwegian locale
    locale: 'nb' as const,
  },
} as const;

/**
 * Error types for Stripe operations
 */
export enum StripeErrorType {
  INITIALIZATION_FAILED = 'stripe_initialization_failed',
  CHECKOUT_FAILED = 'stripe_checkout_failed',
  PAYMENT_FAILED = 'stripe_payment_failed',
  NETWORK_ERROR = 'stripe_network_error',
  CONFIGURATION_ERROR = 'stripe_configuration_error',
}

/**
 * Stripe error class with Norwegian error messages
 */
export class StripeError extends Error {
  public readonly type: StripeErrorType;
  public readonly norwegianMessage: string;
  
  constructor(type: StripeErrorType, message: string, norwegianMessage: string) {
    super(message);
    this.name = 'StripeError';
    this.type = type;
    this.norwegianMessage = norwegianMessage;
  }
}

/**
 * Create Norwegian-localized Stripe error with enhanced messaging
 */
export function createStripeError(type: StripeErrorType, originalError?: Error): StripeError {
  const errorMessages = {
    [StripeErrorType.INITIALIZATION_FAILED]: {
      english: 'Failed to initialize payment system',
      norwegian: 'Kunne ikke initialisere betalingssystem. Sjekk internettforbindelsen og prøv igjen.',
    },
    [StripeErrorType.CHECKOUT_FAILED]: {
      english: 'Failed to start checkout process',
      norwegian: 'Kunne ikke starte betalingsprosess. Prøv igjen eller kontakt support.',
    },
    [StripeErrorType.PAYMENT_FAILED]: {
      english: 'Payment processing failed',
      norwegian: 'Betalingsbehandling feilet. Sjekk betalingsmetoden og prøv igjen.',
    },
    [StripeErrorType.NETWORK_ERROR]: {
      english: 'Network error during payment processing',
      norwegian: 'Nettverksfeil under betalingsbehandling. Sjekk internettforbindelsen og prøv igjen.',
    },
    [StripeErrorType.CONFIGURATION_ERROR]: {
      english: 'Payment system configuration error',
      norwegian: 'Konfigurasjonsfeil i betalingssystem. Kontakt support for hjelp.',
    },
  };

  const messages = errorMessages[type];
  const fullMessage = originalError
    ? `${messages.english}: ${originalError.message}`
    : messages.english;

  return new StripeError(type, fullMessage, messages.norwegian);
}

/**
 * Enhanced error handling utilities for common Stripe scenarios
 */
export const StripeErrorUtils = {
  /**
   * Determine if an error is retryable
   */
  isRetryableError(error: Error | StripeError): boolean {
    if (error instanceof StripeError) {
      return error.type === StripeErrorType.NETWORK_ERROR ||
             error.type === StripeErrorType.CHECKOUT_FAILED;
    }

    const message = error.message.toLowerCase();
    return message.includes('network') ||
           message.includes('timeout') ||
           message.includes('fetch') ||
           message.includes('connection');
  },

  /**
   * Get user-friendly Norwegian error message
   */
  getUserFriendlyMessage(error: Error | StripeError): string {
    if (error instanceof StripeError) {
      return error.norwegianError;
    }

    const message = error.message.toLowerCase();

    if (message.includes('network') || message.includes('fetch')) {
      return 'Nettverksfeil. Sjekk internettforbindelsen og prøv igjen.';
    }

    if (message.includes('timeout')) {
      return 'Forespørselen tok for lang tid. Prøv igjen.';
    }

    if (message.includes('card') || message.includes('payment')) {
      return 'Problem med betalingsmetoden. Sjekk kortinformasjonen og prøv igjen.';
    }

    if (message.includes('subscription')) {
      return 'Problem med abonnement. Kontakt support for hjelp.';
    }

    return 'En ukjent feil oppstod. Prøv igjen eller kontakt support.';
  },

  /**
   * Get retry delay based on attempt count (exponential backoff)
   */
  getRetryDelay(attemptCount: number): number {
    return Math.min(1000 * Math.pow(2, attemptCount), 10000); // Max 10 seconds
  },

  /**
   * Check if error indicates a permanent failure
   */
  isPermanentFailure(error: Error | StripeError): boolean {
    if (error instanceof StripeError) {
      return error.type === StripeErrorType.CONFIGURATION_ERROR;
    }

    const message = error.message.toLowerCase();
    return message.includes('configuration') ||
           message.includes('key') ||
           message.includes('unauthorized') ||
           message.includes('forbidden');
  },
};

/**
 * Development utilities
 */
export const StripeDevUtils = {
  /**
   * Check if Stripe is properly configured
   */
  async checkConfiguration(): Promise<boolean> {
    try {
      const stripe = await getStripe();
      return stripe !== null;
    } catch (error) {
      console.error('Stripe configuration check failed:', error);
      return false;
    }
  },
  
  /**
   * Get current Stripe configuration info
   */
  getConfigInfo() {
    const isDevelopment = import.meta.env.DEV || import.meta.env.MODE === 'development';
    return {
      environment: isDevelopment ? 'development' : 'production',
      hasPublishableKey: !!getStripePublishableKey(),
      locale: 'nb',
      currency: STRIPE_CONSTANTS.CURRENCY,
    };
  },
};
