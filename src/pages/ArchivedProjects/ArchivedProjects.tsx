import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  ProjectCard,
  EmptyState,
  DashboardLayout,
  StatsCard,
  BodyText,
  TextMuted,
  SecondaryButton
} from '../../components/ui';

/**
 * Archived Projects Management Page
 *
 * Provides contractors with a dedicated interface to:
 * - View all archived projects
 * - Search and filter archived projects
 * - Restore projects back to active status
 * - View archive statistics and metadata
 *
 * Features:
 * - Norwegian localization throughout
 * - WCAG AA accessibility compliance
 * - Mobile-first responsive design
 * - JobbLogg design system integration
 * - Real-time data updates via Convex
 */
const ArchivedProjects: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'archivedAt' | 'createdAt' | 'name'>('archivedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Data queries
  const archivedProjects = useQuery(api.projects.getArchivedByUserWithCustomers, {
    userId: user?.id || ""
  });
  const activeProjects = useQuery(api.projects.getByUserWithCustomers, {
    userId: user?.id || ""
  });

  // Mutations
  const restoreProject = useMutation(api.projects.restoreProject);
  // Loading state
  if (archivedProjects === undefined || activeProjects === undefined) {
    return (
      <DashboardLayout
        title="Arkiverte prosjekter"
        subtitle="Laster arkiverte prosjekter..."
        headerActions={
          <SecondaryButton onClick={() => navigate('/')}>
            ← Tilbake til oversikt
          </SecondaryButton>
        }
      >
        <div className="space-y-8">
          {/* Stats Skeleton */}
          <div className="grid-stats">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-lg p-6 border border-jobblogg-border">
                <div className="skeleton h-12 w-12 rounded-lg mb-4"></div>
                <div className="skeleton h-8 w-16 mb-2"></div>
                <div className="skeleton h-4 w-24"></div>
              </div>
            ))}
          </div>

          {/* Projects Grid Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-lg p-6 border border-jobblogg-border">
                <div className="skeleton h-48 w-full rounded-lg mb-4"></div>
                <div className="skeleton h-6 w-3/4 mb-2"></div>
                <div className="skeleton h-4 w-full mb-4"></div>
                <div className="flex gap-2">
                  <div className="skeleton h-8 w-20"></div>
                  <div className="skeleton h-8 w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Handle project restoration
  const handleRestoreProject = async (projectId: string) => {
    if (!user?.id) return;

    try {
      await restoreProject({
        projectId: projectId as any,
        userId: user.id
      });
    } catch (error) {
      console.error('Failed to restore project:', error);
      // Error handling would be enhanced with toast notifications
    }
  };

  // Filter and sort projects
  const filteredProjects = archivedProjects
    ?.filter(project => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        project.name.toLowerCase().includes(query) ||
        project.description?.toLowerCase().includes(query) ||
        project.customer?.name.toLowerCase().includes(query) ||
        project.customer?.address?.toLowerCase().includes(query)
      );
    })
    .sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'archivedAt':
          aValue = a.archivedAt || 0;
          bValue = b.archivedAt || 0;
          break;
        case 'createdAt':
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        default:
          aValue = a.archivedAt || 0;
          bValue = b.archivedAt || 0;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    }) || [];

  // Calculate statistics
  const totalArchived = archivedProjects?.length || 0;
  const archivedThisMonth = archivedProjects?.filter(p => {
    if (!p.archivedAt) return false;
    const archivedDate = new Date(p.archivedAt);
    const now = new Date();
    return archivedDate.getMonth() === now.getMonth() &&
           archivedDate.getFullYear() === now.getFullYear();
  }).length || 0;

  const oldestArchived = archivedProjects && archivedProjects.length > 0
    ? archivedProjects
        .filter(p => p.archivedAt)
        .sort((a, b) => (a.archivedAt || 0) - (b.archivedAt || 0))[0]
    : null;

  return (
    <DashboardLayout
      title="Arkiverte prosjekter"
      subtitle={`${totalArchived} arkiverte prosjekter tilgjengelig for gjenåpning`}
      headerActions={
        <div className="flex items-center gap-3">
          <SecondaryButton onClick={() => navigate('/')}>
            ← Tilbake til oversikt
          </SecondaryButton>
        </div>
      }
      statsSection={
        <div className="grid-stats">
          <StatsCard
            title="Totalt arkiverte"
            value={totalArchived}
            variant="warning"
            animationDelay="0s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4m0 6l-4-4-4 4" />
              </svg>
            }
          />
          <StatsCard
            title="Arkivert denne måneden"
            value={archivedThisMonth}
            variant="accent"
            animationDelay="0.1s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
              </svg>
            }
          />
          <StatsCard
            title="Eldste arkiverte"
            value={oldestArchived && oldestArchived.archivedAt
              ? new Date(oldestArchived.archivedAt).toLocaleDateString('nb-NO', { day: 'numeric', month: 'short', year: 'numeric' })
              : '-'
            }
            variant="primary"
            animationDelay="0.2s"
            icon={
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />
        </div>
      }
    >
      {/* Search and Filter Controls */}
      <section className="space-section">
        <div className="bg-white rounded-xl shadow-soft border border-jobblogg-border p-6">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            {/* Search Input */}
            <div className="flex-1 max-w-md">
              <label htmlFor="search" className="sr-only">Søk i arkiverte prosjekter</label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  id="search"
                  type="text"
                  placeholder="Søk etter prosjektnavn, beskrivelse, kunde..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-jobblogg-border rounded-lg focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary text-jobblogg-text-strong placeholder-jobblogg-text-muted"
                />
              </div>
            </div>

            {/* Sort Controls */}
            <div className="flex items-center gap-3">
              <BodyText className="text-jobblogg-text-medium whitespace-nowrap">Sorter etter:</BodyText>
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder];
                  setSortBy(newSortBy);
                  setSortOrder(newSortOrder);
                }}
                className="border border-jobblogg-border rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary"
              >
                <option value="archivedAt-desc">Nyest arkiverte</option>
                <option value="archivedAt-asc">Eldst arkiverte</option>
                <option value="name-asc">Navn A-Å</option>
                <option value="name-desc">Navn Å-A</option>
                <option value="createdAt-desc">Nyest opprettet</option>
                <option value="createdAt-asc">Eldst opprettet</option>
              </select>
            </div>
          </div>

          {/* Results Summary */}
          {searchQuery && (
            <div className="mt-4 pt-4 border-t border-jobblogg-border">
              <TextMuted>
                Viser {filteredProjects.length} av {totalArchived} arkiverte prosjekter
                {searchQuery && ` som matcher "${searchQuery}"`}
              </TextMuted>
            </div>
          )}
        </div>
      </section>

      {/* Archived Projects Grid */}
      <section className="space-section">
        <div className="grid-mobile-cards">
          {filteredProjects.map((project, index) => (
            <ProjectCard
              key={project._id}
              title={project.name}
              description={project.description || 'Ingen beskrivelse tilgjengelig'}
              projectId={project._id}
              updatedAt={project.archivedAt
                ? `Arkivert ${new Date(project.archivedAt).toLocaleDateString('nb-NO')}`
                : new Date(project.createdAt).toLocaleDateString('nb-NO')
              }
              onClick={() => navigate(`/project/${project._id}`)}
              animationDelay={`${index * 0.1}s`}
              customer={project.customer as any}
              isArchived={true}
              archivedAt={project.archivedAt}
              showArchiveActions={true}
              onRestore={() => handleRestoreProject(project._id)}
            />
          ))}

          {/* Empty State */}
          {filteredProjects.length === 0 && (
            <div className="col-span-full">
              <EmptyState
                title={searchQuery ? "🔍 Ingen treff funnet" : "📦 Ingen arkiverte prosjekter"}
                description={searchQuery
                  ? `Ingen arkiverte prosjekter matcher søket "${searchQuery}". Prøv et annet søkeord eller juster filteret.`
                  : "Du har ingen arkiverte prosjekter ennå. Prosjekter du arkiverer fra hovedoversikten vil vises her."
                }
                actionLabel={searchQuery ? "Tøm søk" : "Gå til aktive prosjekter"}
                onAction={() => searchQuery ? setSearchQuery('') : navigate('/')}
              />
            </div>
          )}
        </div>
      </section>
    </DashboardLayout>
  );
};

export default ArchivedProjects;
