import React from 'react';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  PageLayout,
  Heading1,
  Heading2,
  BodyText,
  TextMuted,
  SecondaryButton
} from '../../components/ui';
import { useBlockedUserPageAccess } from '../../hooks/useBlockedUserCheck';

/**
 * BlockedUser page - shown when a user tries to access the application but is blocked
 */
const BlockedUser: React.FC = () => {
  const { user, isLoaded } = useUser();

  // Use the specialized hook for blocked user page access
  const { blockStatus, isLoading } = useBlockedUserPageAccess();

  // Get company administrator information
    // const administrator = useQuery(
  //   api.teamManagement.getCompanyAdministrator,
  //   user?.id ? { clerkUserId: user.id } : "skip"
  // );
  const administrator = {
    email: '',
    name: '',
    role: '',
    companyName: ''
  }; // Temporarily provide fallback structure due to type instantiation issues

  // Format blocked date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleContactAdministrator = () => {
    if (administrator?.email) {
      const subject = encodeURIComponent('Forespørsel om tilgang - Sperret bruker');
      const body = encodeURIComponent(
        `Hei ${administrator.name || administrator.role},\n\n` +
        `Jeg har fått beskjed om at min tilgang til JobbLogg er sperret. ` +
        `Kan du hjelpe meg med å forstå situasjonen og eventuelt gjenåpne tilgangen min?\n\n` +
        `Takk for hjelpen.\n\n` +
        `Med vennlig hilsen,\n` +
        `${user?.firstName || ''} ${user?.lastName || ''}`.trim()
      );
      window.location.href = `mailto:${administrator.email}?subject=${subject}&body=${body}`;
    }
  };

  const handleSignOut = () => {
    window.location.href = '/sign-out';
  };

  if (!isLoaded || !user || isLoading || administrator === undefined) {
    return (
      <PageLayout containerWidth="narrow">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary"></div>
        </div>
      </PageLayout>
    );
  }

  // The useBlockedUserPageAccess hook handles redirection automatically
  // If we reach here, the user is blocked

  return (
    <PageLayout containerWidth="narrow">
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="max-w-md w-full space-y-8 text-center">
          {/* Blocked icon */}
          <div className="w-20 h-20 mx-auto rounded-full bg-jobblogg-error/10 flex items-center justify-center">
            <svg className="w-10 h-10 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m4-6V9a4 4 0 10-8 0v2m0 0v2a2 2 0 002 2h4a2 2 0 002-2v-2" />
            </svg>
          </div>

          {/* Main heading */}
          <div className="space-y-2">
            <Heading1 className="text-jobblogg-error">
              Tilgang sperret
            </Heading1>
            <TextMuted>
              Din tilgang til JobbLogg har blitt midlertidig sperret
            </TextMuted>
          </div>

          {/* Blocking details */}
          <div className="bg-jobblogg-error/5 border border-jobblogg-error/10 rounded-lg p-6 text-left space-y-4">
            <Heading2 className="text-lg text-jobblogg-error">
              Sperring detaljer
            </Heading2>

            {blockStatus?.blockedAt && (
              <div>
                <BodyText className="font-medium text-sm">Sperret:</BodyText>
                <TextMuted className="text-sm">
                  {formatDate(blockStatus.blockedAt)}
                </TextMuted>
              </div>
            )}

            <TextMuted className="text-sm">
              Kontakt din administrator for mer informasjon om sperringen.
            </TextMuted>
          </div>

          {/* Information */}
          <div className="bg-jobblogg-neutral/30 rounded-lg p-6 text-left space-y-3">
            <Heading2 className="text-lg">
              Hva betyr dette?
            </Heading2>

            <div className="space-y-2 text-sm text-jobblogg-text-medium">
              <p>• Du kan ikke logge inn på JobbLogg</p>
              <p>• Du har ikke tilgang til prosjekter eller team-funksjoner</p>
              <p>• Dine historiske data er bevart og trygg</p>
              <p>• En administrator kan gjenåpne tilgangen din</p>
            </div>
          </div>

          {/* Contact information */}
          <div className="bg-jobblogg-primary/5 border border-jobblogg-primary/10 rounded-lg p-6 text-left space-y-3">
            <Heading2 className="text-lg text-jobblogg-primary">
              Trenger du hjelp?
            </Heading2>

            {administrator ? (
              <>
                <TextMuted className="text-sm">
                  Hvis du mener dette er en feil, eller hvis du har spørsmål om sperringen,
                  kan du kontakte din administrator.
                </TextMuted>

                {/* Administrator contact info */}
                <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 rounded-full bg-jobblogg-primary/10 flex items-center justify-center flex-shrink-0">
                      <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <BodyText className="font-medium">
                        {administrator.name || administrator.role}
                      </BodyText>
                      <TextMuted className="text-sm">
                        {administrator.role} • {administrator.companyName}
                      </TextMuted>
                      <TextMuted className="text-sm">
                        {administrator.email}
                      </TextMuted>
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <TextMuted className="text-sm">
                Hvis du mener dette er en feil, eller hvis du har spørsmål om sperringen,
                kontakt din administrator for mer informasjon.
              </TextMuted>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              {administrator?.email && (
                <SecondaryButton
                  onClick={handleContactAdministrator}
                  size="sm"
                  className="flex-1"
                >
                  Kontakt administrator
                </SecondaryButton>
              )}

              <SecondaryButton
                onClick={handleSignOut}
                size="sm"
                variant="outline"
                className="flex-1"
              >
                Logg ut
              </SecondaryButton>
            </div>
          </div>

          {/* Footer note */}
          <TextMuted className="text-xs">
            Denne siden vises kun for sperrede brukere.
            Kontakt din administrator for å få tilgang gjenåpnet.
          </TextMuted>
        </div>
      </div>
    </PageLayout>
  );
};

export default BlockedUser;
