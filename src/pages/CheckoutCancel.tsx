/**
 * Checkout Cancel Page Component
 *
 * Comprehensive cancellation handling with user-friendly messaging,
 * retry options, and contextual guidance based on subscription status.
 *
 * Features:
 * - Context-aware messaging (trial vs upgrade cancellation)
 * - Multiple action options (retry, contact support, return to dashboard)
 * - Norwegian localization
 * - Analytics tracking for cancellation reasons
 * - Error handling and edge cases
 * - JobbLogg design system integration
 */

import { useUser } from '@clerk/clerk-react';
import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { BodyText, Heading1, Heading2, PageLayout, PrimaryButton, SecondaryButton, TextMuted } from '../components/ui';
import { CheckoutButton } from '../components/ui/CheckoutButton';
import { createPlanCheckoutParams, useCheckout } from '../hooks/useCheckout';
import { useSubscriptionAccess } from '../hooks/useSubscriptionAccess';
import { getCheckoutContext, parsePlanParams, validateCancelParams } from '../lib/checkout-utils';
import { determineCancellationReason, trackCheckoutCancellation, trackCheckoutRetry } from '../utils/checkoutAnalytics';

interface CancellationContext {
  type: 'trial_setup' | 'trial_conversion' | 'plan_upgrade' | 'unknown';
  planLevel?: 'basic' | 'professional' | 'enterprise';
  billingInterval?: 'month' | 'year';
  isTrialExpired: boolean;
  daysLeftInTrial: number;
}

const CheckoutCancel: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useUser();
  const { subscription, isInTrial, isTrialExpired } = useSubscriptionAccess();
  const checkout = useCheckout();

  // State for cancellation context and analytics
  const [context, setContext] = useState<CancellationContext>({
    type: 'unknown',
    isTrialExpired: false,
    daysLeftInTrial: 0,
  });
  const [showRetryOptions, setShowRetryOptions] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<'basic' | 'professional' | 'enterprise'>('basic');
  const [selectedBilling, setSelectedBilling] = useState<'month' | 'year'>('month');

  // Determine cancellation context
  useEffect(() => {
    if (!subscription) return;

    const trialEnd = (subscription as any)?.trialEnd || 0;
    const daysLeft = Math.max(0, Math.ceil((trialEnd - Date.now()) / (24 * 60 * 60 * 1000)));

    let cancellationType: CancellationContext['type'] = 'unknown';

    // Determine context based on subscription status and URL parameters
    if (!subscription.stripeCustomerId && isInTrial) {
      cancellationType = 'trial_setup';
    } else if (isTrialExpired || daysLeft <= 0) {
      cancellationType = 'trial_conversion';
    } else if (subscription.status === 'active') {
      cancellationType = 'plan_upgrade';
    }

    // Extract plan info using utility functions
    const planParams = parsePlanParams(searchParams);
    const planLevel = planParams.planLevel || (subscription as any)?.planLevel || 'basic';
    const billingInterval = planParams.billingInterval || (subscription as any)?.billingInterval || 'month';

    setContext({
      type: cancellationType,
      planLevel,
      billingInterval,
      isTrialExpired,
      daysLeftInTrial: daysLeft,
    });

    setSelectedPlan(planLevel);
    setSelectedBilling(billingInterval);

    // Validate URL parameters and log context
    const paramValidation = validateCancelParams(searchParams);
    const checkoutContext = getCheckoutContext();

    console.log('Checkout cancel context:', {
      paramValidation,
      checkoutContext,
      planParams,
      cancellationType,
    });

    // Track cancellation for analytics
    const cancellationReason = determineCancellationReason(
      cancellationType,
      navigator.userAgent
    );

    trackCheckoutCancellation({
      type: cancellationType,
      planLevel,
      billingInterval,
      isTrialExpired,
      daysLeftInTrial: daysLeft,
      userId: user?.id,
      subscriptionStatus: subscription.status,
      reason: 'user_cancelled',
    });

    console.log('🚫 Checkout cancelled:', {
      type: cancellationType,
      planLevel,
      billingInterval,
      userId: user?.id,
      subscriptionStatus: subscription.status,
      daysLeftInTrial: daysLeft,
      reason: cancellationReason,
    });
  }, [subscription, isInTrial, isTrialExpired, searchParams, user]);

  // Get contextual messaging
  const getContextualMessaging = () => {
    switch (context.type) {
      case 'trial_setup':
        return {
          title: 'Prøveperiode ikke startet',
          subtitle: 'Du avbrøt oppsettet av prøveperioden',
          description: 'Ingen bekymring! Du kan starte din 7-dagers gratis prøveperiode når som helst. Ingen kredittkort påkrevd.',
          icon: '⏸️',
          iconBg: 'bg-blue-100',
          iconColor: 'text-blue-600',
          urgency: 'low' as const,
        };

      case 'trial_conversion':
        return {
          title: 'Oppgradering avbrutt',
          subtitle: context.isTrialExpired
            ? 'Prøveperioden er utløpt'
            : `${context.daysLeftInTrial} dager igjen av prøveperioden`,
          description: context.isTrialExpired
            ? 'Du må oppgradere til en betalt plan for å fortsette å bruke JobbLogg. Velg en plan som passer for din bedrift.'
            : 'Du kan fortsette å bruke JobbLogg i prøveperioden, men husk å oppgradere før den utløper.',
          icon: '⚠️',
          iconBg: context.isTrialExpired ? 'bg-red-100' : 'bg-yellow-100',
          iconColor: context.isTrialExpired ? 'text-red-600' : 'text-yellow-600',
          urgency: context.isTrialExpired ? 'high' as const : 'medium' as const,
        };

      case 'plan_upgrade':
        return {
          title: 'Planoppgradering avbrutt',
          subtitle: 'Du avbrøt endringen av abonnementsplan',
          description: 'Din nåværende plan fortsetter som normalt. Du kan oppgradere eller endre plan når som helst.',
          icon: '📋',
          iconBg: 'bg-gray-100',
          iconColor: 'text-gray-600',
          urgency: 'low' as const,
        };

      default:
        return {
          title: 'Betaling avbrutt',
          subtitle: 'Du avbrøt betalingsprosessen',
          description: 'Ingen betaling er gjennomført. Du kan prøve igjen når som helst.',
          icon: '💳',
          iconBg: 'bg-yellow-100',
          iconColor: 'text-yellow-600',
          urgency: 'medium' as const,
        };
    }
  };

  // Handle retry payment
  const handleRetryPayment = async () => {
    if (!context.planLevel || !context.billingInterval) return;

    // Track retry attempt
    trackCheckoutRetry({
      userId: user?.id,
      planLevel: context.planLevel,
      billingInterval: context.billingInterval,
      context: context.type,
      previousCancellationReason: 'user_cancelled',
    });

    const params = createPlanCheckoutParams(context.planLevel, context.billingInterval, {
      trialDays: context.type === 'trial_setup' ? 7 : 0,
    });

    if (params.priceId) {
      await checkout.initiateCheckout({
        priceId: params.priceId,
        planLevel: params.planLevel!,
        billingInterval: params.billingInterval!,
        quantity: params.quantity,
        trialDays: params.trialDays,
        successUrl: params.successUrl,
        cancelUrl: params.cancelUrl,
        allowPromotionCodes: params.allowPromotionCodes,
        automaticTax: params.automaticTax,
      });
    }
  };

  // Handle plan selection for retry
  const handlePlanChange = (plan: 'basic' | 'professional' | 'enterprise', billing: 'month' | 'year') => {
    setSelectedPlan(plan);
    setSelectedBilling(billing);
    setContext(prev => ({ ...prev, planLevel: plan, billingInterval: billing }));
  };

  const messaging = getContextualMessaging();

  return (
    <PageLayout title="Betaling avbrutt" showBackButton={false} containerWidth="medium">
      <div className="bg-white rounded-xl shadow-soft p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className={`w-20 h-20 ${messaging.iconBg} rounded-full flex items-center justify-center mx-auto mb-4`}>
            <span className="text-3xl">{messaging.icon}</span>
          </div>
          <Heading1 className={`mb-2 ${messaging.urgency === 'high' ? 'text-red-800' : messaging.urgency === 'medium' ? 'text-yellow-800' : 'text-gray-800'}`}>
            {messaging.title}
          </Heading1>
          <TextMuted className="text-lg">{messaging.subtitle}</TextMuted>
        </div>

        {/* Description */}
        <div className="text-center mb-8">
          <BodyText className="text-jobblogg-text-medium max-w-2xl mx-auto">
            {messaging.description}
          </BodyText>
        </div>

        {/* Urgency Banner for Critical Cases */}
        {context.type === 'trial_conversion' && context.isTrialExpired && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
            <div className="flex items-start gap-3">
              <svg className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 className="font-semibold text-red-800 mb-1">Handling påkrevd</h3>
                <p className="text-red-700 text-sm">
                  Prøveperioden din er utløpt. Du må velge en betalt plan for å fortsette å bruke JobbLogg.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Options */}
        <div className="space-y-6">
          {/* Primary Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            {(context.type === 'trial_setup' || context.type === 'trial_conversion') && (
              <CheckoutButton
                onClick={handleRetryPayment}
                isLoading={checkout.state.isLoading}
                isRetrying={checkout.state.isRetrying}
                loadingText="Starter betaling..."
                className="flex-1"
                fullWidth
              >
                {context.type === 'trial_setup' ? 'Start prøveperiode' : 'Velg plan nå'}
              </CheckoutButton>
            )}

            {context.type === 'plan_upgrade' && (
              <PrimaryButton
                onClick={() => setShowRetryOptions(!showRetryOptions)}
                className="flex-1"
              >
                Velg annen plan
              </PrimaryButton>
            )}

            <SecondaryButton
              onClick={() => navigate('/')}
              className="flex-1 sm:flex-none"
            >
              Gå til dashbordet
            </SecondaryButton>
          </div>

          {/* Retry Options */}
          {showRetryOptions && (
            <div className="bg-jobblogg-surface rounded-lg p-6">
              <Heading2 className="mb-4">Velg plan</Heading2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                {[
                  { id: 'basic', name: 'Liten bedrift', price: 299 },
                  { id: 'professional', name: 'Mellomstor bedrift', price: 999 },
                  { id: 'enterprise', name: 'Stor bedrift', price: 2999 },
                ].map((plan) => (
                  <button
                    key={plan.id}
                    onClick={() => handlePlanChange(plan.id as any, selectedBilling)}
                    className={`p-4 rounded-lg border text-left transition-colors ${
                      selectedPlan === plan.id
                        ? 'border-jobblogg-primary bg-jobblogg-primary-soft'
                        : 'border-jobblogg-border hover:border-jobblogg-primary'
                    }`}
                  >
                    <h3 className="font-semibold text-jobblogg-text-primary">{plan.name}</h3>
                    <p className="text-jobblogg-text-medium text-sm">{plan.price} kr/md.</p>
                  </button>
                ))}
              </div>

              <div className="flex gap-2 mb-4">
                <button
                  onClick={() => handlePlanChange(selectedPlan, 'month')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedBilling === 'month'
                      ? 'bg-jobblogg-primary text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  Månedlig
                </button>
                <button
                  onClick={() => handlePlanChange(selectedPlan, 'year')}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedBilling === 'year'
                      ? 'bg-jobblogg-primary text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  Årlig (spar 20%)
                </button>
              </div>

              <CheckoutButton
                onClick={handleRetryPayment}
                isLoading={checkout.state.isLoading}
                isRetrying={checkout.state.isRetrying}
                loadingText="Starter betaling..."
                fullWidth
              >
                Fortsett med valgt plan
              </CheckoutButton>
            </div>
          )}
        </div>

        {/* Alternative Actions */}
        <div className="mt-8 pt-6 border-t border-jobblogg-border">
          <Heading2 className="mb-4 text-center">Andre alternativer</Heading2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* View Pricing */}
            <Link
              to="/pricing"
              className="flex items-center gap-3 p-4 rounded-lg border border-jobblogg-border hover:border-jobblogg-primary transition-colors"
            >
              <div className="w-10 h-10 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-jobblogg-text-primary">Se priser</h3>
                <p className="text-jobblogg-text-medium text-sm">Sammenlign planer</p>
              </div>
            </Link>

            {/* Subscription Management */}
            <Link
              to="/subscription"
              className="flex items-center gap-3 p-4 rounded-lg border border-jobblogg-border hover:border-jobblogg-primary transition-colors"
            >
              <div className="w-10 h-10 bg-jobblogg-success-soft rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-jobblogg-text-primary">Administrer abonnement</h3>
                <p className="text-jobblogg-text-medium text-sm">Se nåværende plan</p>
              </div>
            </Link>

            {/* Contact Support */}
            <Link
              to="/help"
              className="flex items-center gap-3 p-4 rounded-lg border border-jobblogg-border hover:border-jobblogg-primary transition-colors"
            >
              <div className="w-10 h-10 bg-jobblogg-warning-soft rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-jobblogg-text-primary">Kontakt support</h3>
                <p className="text-jobblogg-text-medium text-sm">Få hjelp</p>
              </div>
            </Link>
          </div>
        </div>

        {/* Common Cancellation Reasons & Solutions */}
        <div className="mt-8 pt-6 border-t border-jobblogg-border">
          <Heading2 className="mb-4">Vanlige årsaker til avbrudd</Heading2>
          <div className="space-y-4">
            <details className="group">
              <summary className="flex items-center justify-between p-4 bg-jobblogg-surface rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                <span className="font-medium text-jobblogg-text-primary">Usikker på hvilken plan som passer?</span>
                <svg className="w-5 h-5 text-jobblogg-text-muted group-open:rotate-180 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div className="p-4 pt-2">
                <p className="text-jobblogg-text-medium text-sm mb-3">
                  Vi hjelper deg gjerne med å finne riktig plan for din bedrift.
                </p>
                <div className="flex gap-2">
                  <Link to="/pricing" className="text-jobblogg-primary text-sm hover:underline">
                    Sammenlign planer
                  </Link>
                  <span className="text-jobblogg-text-muted text-sm">•</span>
                  <Link to="/help" className="text-jobblogg-primary text-sm hover:underline">
                    Kontakt oss
                  </Link>
                </div>
              </div>
            </details>

            <details className="group">
              <summary className="flex items-center justify-between p-4 bg-jobblogg-surface rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                <span className="font-medium text-jobblogg-text-primary">Tekniske problemer under betaling?</span>
                <svg className="w-5 h-5 text-jobblogg-text-muted group-open:rotate-180 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div className="p-4 pt-2">
                <p className="text-jobblogg-text-medium text-sm mb-3">
                  Prøv å oppdatere siden eller bruk en annen nettleser. Kontakt support hvis problemet vedvarer.
                </p>
                <div className="flex gap-2">
                  <button
                    onClick={() => window.location.reload()}
                    className="text-jobblogg-primary text-sm hover:underline"
                  >
                    Oppdater siden
                  </button>
                  <span className="text-jobblogg-text-muted text-sm">•</span>
                  <Link to="/help" className="text-jobblogg-primary text-sm hover:underline">
                    Rapporter problem
                  </Link>
                </div>
              </div>
            </details>

            <details className="group">
              <summary className="flex items-center justify-between p-4 bg-jobblogg-surface rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                <span className="font-medium text-jobblogg-text-primary">Trenger mer tid til å bestemme deg?</span>
                <svg className="w-5 h-5 text-jobblogg-text-muted group-open:rotate-180 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </summary>
              <div className="p-4 pt-2">
                <p className="text-jobblogg-text-medium text-sm mb-3">
                  {context.type === 'trial_setup'
                    ? 'Du kan starte prøveperioden når som helst. Ingen forpliktelser.'
                    : context.daysLeftInTrial > 0
                    ? `Du har ${context.daysLeftInTrial} dager igjen av prøveperioden.`
                    : 'Kontakt oss for å diskutere dine behov.'
                  }
                </p>
                <Link to="/" className="text-jobblogg-primary text-sm hover:underline">
                  Gå til dashbordet
                </Link>
              </div>
            </details>
          </div>
        </div>

        {/* Support Information */}
        <div className="mt-8 pt-6 border-t border-jobblogg-border text-center">
          <TextMuted className="text-sm">
            Har du spørsmål eller trenger hjelp?
            <br />
            <Link to="/help" className="text-jobblogg-primary hover:underline">Kontakt support</Link> eller
            send e-post til <a href="mailto:<EMAIL>" className="text-jobblogg-primary hover:underline"><EMAIL></a>
          </TextMuted>
        </div>
      </div>
    </PageLayout>
  );
};

export default CheckoutCancel;
