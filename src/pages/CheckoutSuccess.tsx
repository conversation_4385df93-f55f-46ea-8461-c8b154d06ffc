/**
 * Checkout Success Page Component
 * 
 * Displays payment confirmation and guides users to their next steps
 * after successful Stripe checkout completion.
 * 
 * Features:
 * - Session ID parameter processing
 * - Payment confirmation display
 * - Subscription details and next steps
 * - Norwegian localization
 * - Error handling for invalid sessions
 * - Loading states during session retrieval
 */

import { useUser } from '@clerk/clerk-react';
import { useAction } from 'convex/react';
import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { api } from '../../convex/_generated/api';
import { BodyText, Heading1, Heading2, PageLayout, PrimaryButton, SecondaryButton, TextMuted } from '../components/ui';
import { getCheckoutContext, getCheckoutErrorMessage, validateSuccessParams } from '../lib/checkout-utils';
import { formatCurrency } from '../utils/formatCurrency';

interface CheckoutSessionData {
  sessionId: string;
  paymentStatus: string;
  customerEmail: string;
  amountTotal: number;
  currency: string;
  subscriptionId: string;
  planLevel: 'basic' | 'professional' | 'enterprise';
  billingInterval: 'month' | 'year';
  trialEnd?: number;
  isTrialConversion: boolean;
  createdAt: number;
}

const CheckoutSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useUser();
  const sessionId = searchParams.get('session_id');
  const isMockMode = searchParams.get('mock') === 'true';
  const mockPlan = searchParams.get('plan');
  const mockBilling = searchParams.get('billing');

  // State for error handling and session data
  const [error, setError] = useState<string | null>(null);
  const [sessionData, setSessionData] = useState<CheckoutSessionData | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Action to get checkout session details
  const getCheckoutSession = useAction(api.subscriptions.getCheckoutSession);

  // Fetch session data when component mounts
  useEffect(() => {
    const fetchSessionData = async () => {
      if (!sessionId || error || isMockMode) return;

      setIsLoading(true);
      try {
        console.log('🔄 Fetching checkout session data:', sessionId);
        const data = await getCheckoutSession({ sessionId });
        setSessionData(data);
        console.log('✅ Session data fetched successfully:', data);

        // Force a subscription data refresh by triggering a window event
        // This will help the real-time hooks pick up the updated subscription status
        window.dispatchEvent(new CustomEvent('subscription-updated', {
          detail: { source: 'checkout-success', sessionId }
        }));
      } catch (err) {
        console.error('❌ Error fetching session data:', err);
        const errorMessage = err instanceof Error ? err.message : 'Ukjent feil';
        setError(`Kunne ikke hente betalingsinformasjon: ${errorMessage}`);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessionData();
  }, [sessionId, error, isMockMode, getCheckoutSession]);

  // Handle URL parameter validation
  useEffect(() => {
    const validation = validateSuccessParams(searchParams);

    if (!validation.isValid) {
      const norwegianError = getCheckoutErrorMessage(validation.errors);
      setError(norwegianError || 'Ugyldig betalings-ID. Kontakt support hvis problemet vedvarer.');
      return;
    }

    // Log checkout context for debugging
    const context = getCheckoutContext();
    console.log('Checkout success context:', context);
  }, [searchParams]);

  // Get plan display information
  const getPlanInfo = (planLevel: string, billingInterval: string) => {
    const planNames = {
      basic: 'Liten bedrift',
      professional: 'Mellomstor bedrift',
      enterprise: 'Stor bedrift',
    };

    const intervalText = billingInterval === 'year' ? 'årlig' : 'månedlig';
    
    return {
      name: planNames[planLevel as keyof typeof planNames] || planLevel,
      interval: intervalText,
    };
  };

  // Get next steps based on payment type
  const getNextSteps = (isTrialConversion: boolean, planLevel: string) => {
    if (isTrialConversion) {
      return {
        title: 'Gratulerer! Abonnementet ditt er nå aktivt',
        subtitle: 'Du har fullført oppgraderingen fra prøveperioden',
        steps: [
          {
            icon: '🎯',
            title: 'Gå til dashbordet',
            description: 'Start med å utforske alle funksjonene i JobbLogg',
            action: 'dashboard',
          },
          {
            icon: '👥',
            title: 'Inviter teammedlemmer',
            description: 'Legg til kollegaer og samarbeid på prosjekter',
            action: 'team',
          },
          {
            icon: '📋',
            title: 'Opprett ditt første prosjekt',
            description: 'Begynn å dokumentere arbeidet ditt',
            action: 'projects',
          },
        ],
      };
    }

    return {
      title: 'Takk for kjøpet!',
      subtitle: 'Betalingen din er bekreftet og abonnementet er aktivt',
      steps: [
        {
          icon: '✅',
          title: 'Abonnement aktivert',
          description: 'Du har nå full tilgang til alle funksjoner',
          action: 'dashboard',
        },
        {
          icon: '📧',
          title: 'Sjekk e-posten din',
          description: 'Du vil motta en bekreftelse på e-post',
          action: null,
        },
        {
          icon: '🔧',
          title: 'Administrer abonnement',
          description: 'Endre plan eller betalingsmetode når som helst',
          action: 'subscription',
        },
      ],
    };
  };

  // Handle navigation actions
  const handleNavigation = (action: string | null) => {
    switch (action) {
      case 'dashboard':
        navigate('/');
        break;
      case 'team':
        navigate('/team');
        break;
      case 'projects':
        navigate('/projects');
        break;
      case 'subscription':
        navigate('/subscription');
        break;
      default:
        break;
    }
  };

  // Loading state
  if (!sessionId || isLoading || (!sessionData && !error && !isMockMode)) {
    return (
      <PageLayout title="Bekrefter betaling..." showBackButton={false} containerWidth="medium">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
            <Heading2 className="mb-2">Bekrefter betalingen din...</Heading2>
            <TextMuted>Dette kan ta noen sekunder</TextMuted>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Mock mode for development
  if (isMockMode && sessionId) {
    return (
      <PageLayout title="Betaling bekreftet (Utviklingsmodus)" showBackButton={false} containerWidth="medium">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <Heading1 className="mb-4 text-jobblogg-text-primary">🧪 Utviklingsmodus</Heading1>
            <BodyText className="mb-6 text-jobblogg-text-medium">
              Dette er en simulert betalingsbekreftelse for utvikling. I produksjon ville dette vært en ekte Stripe-betaling.
            </BodyText>

            <div className="bg-jobblogg-surface rounded-lg p-4 mb-6">
              <div className="text-sm text-jobblogg-text-medium space-y-2">
                <div><strong>Session ID:</strong> {sessionId}</div>
                {mockPlan && <div><strong>Plan:</strong> {mockPlan}</div>}
                {mockBilling && <div><strong>Fakturering:</strong> {mockBilling}</div>}
                <div><strong>Status:</strong> Mock Success</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <PrimaryButton onClick={() => navigate('/dashboard')}>
                Gå til dashbordet
              </PrimaryButton>
              <SecondaryButton onClick={() => navigate('/subscription')}>
                Se abonnement
              </SecondaryButton>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Error state
  if (error || !sessionData) {
    return (
      <PageLayout title="Betalingsfeil" showBackButton={false} containerWidth="medium">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <Heading2 className="mb-4 text-red-800">Kunne ikke bekrefte betalingen</Heading2>
            <BodyText className="mb-6 text-jobblogg-text-medium">
              {error || 'Det oppstod en feil under bekreftelsen av betalingen din. Kontakt support hvis problemet vedvarer.'}
            </BodyText>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <PrimaryButton onClick={() => navigate('/')}>
                Gå til dashbordet
              </PrimaryButton>
              <SecondaryButton onClick={() => navigate('/subscription')}>
                Se abonnement
              </SecondaryButton>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Success state
  const planInfo = getPlanInfo(sessionData.planLevel, sessionData.billingInterval);
  const nextSteps = getNextSteps(sessionData.isTrialConversion, sessionData.planLevel);

  return (
    <PageLayout title="Betaling bekreftet" showBackButton={false} containerWidth="medium">
      <div className="bg-white rounded-xl shadow-soft p-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <Heading1 className="mb-2 text-green-800">{nextSteps.title}</Heading1>
          <TextMuted className="text-lg">{nextSteps.subtitle}</TextMuted>
        </div>

        {/* Payment Details */}
        <div className="bg-jobblogg-surface rounded-lg p-6 mb-8">
          <Heading2 className="mb-4">Betalingsdetaljer</Heading2>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-jobblogg-text-medium">Plan:</span>
              <span className="font-medium">{planInfo.name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-jobblogg-text-medium">Fakturering:</span>
              <span className="font-medium">{planInfo.interval}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-jobblogg-text-medium">Beløp:</span>
              <span className="font-medium">
                {formatCurrency(sessionData.amountTotal / 100, sessionData.currency.toUpperCase())}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-jobblogg-text-medium">E-post:</span>
              <span className="font-medium">{sessionData.customerEmail}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-jobblogg-text-medium">Betalings-ID:</span>
              <span className="font-mono text-sm text-jobblogg-text-muted">
                {sessionData.sessionId.slice(-12)}
              </span>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="mb-8">
          <Heading2 className="mb-4">Neste steg</Heading2>
          <div className="space-y-4">
            {nextSteps.steps.map((step, index) => (
              <div
                key={index}
                className={`flex items-start gap-4 p-4 rounded-lg border ${
                  step.action ? 'border-jobblogg-border hover:border-jobblogg-primary cursor-pointer transition-colors' : 'border-jobblogg-border'
                }`}
                onClick={() => step.action && handleNavigation(step.action)}
              >
                <div className="text-2xl">{step.icon}</div>
                <div className="flex-1">
                  <h3 className="font-semibold text-jobblogg-text-primary mb-1">{step.title}</h3>
                  <p className="text-jobblogg-text-medium text-sm">{step.description}</p>
                </div>
                {step.action && (
                  <svg className="w-5 h-5 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Primary Actions */}
        <div className="flex flex-col sm:flex-row gap-3">
          <PrimaryButton 
            onClick={() => handleNavigation('dashboard')}
            className="flex-1"
          >
            Gå til dashbordet
          </PrimaryButton>
          <SecondaryButton 
            onClick={() => handleNavigation('subscription')}
            className="flex-1 sm:flex-none"
          >
            Se abonnement
          </SecondaryButton>
        </div>

        {/* Support Information */}
        <div className="mt-8 pt-6 border-t border-jobblogg-border text-center">
          <TextMuted className="text-sm">
            Har du spørsmål? <Link to="/help" className="text-jobblogg-primary hover:underline">Kontakt support</Link> eller 
            send e-post til <a href="mailto:<EMAIL>" className="text-jobblogg-primary hover:underline"><EMAIL></a>
          </TextMuted>
        </div>
      </div>
    </PageLayout>
  );
};

export default CheckoutSuccess;
