import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Header } from "../../components/Landing";
import { SEOHead } from "../../components/SEO";
import {
    Footer,
    Heading2,
    PrimaryButton,
    TextMedium,
    TextMuted,
} from "../../components/ui";

/* ------------------------------------------------
   INNHOLD (lett å redigere uten å røre markup)
-------------------------------------------------*/

const HERO = {
  titleTop: "Dokumenter og kommuniser",
  titleAccent: "underveis",
  targetAudience: "For håndverkere og entreprenører i Norge",
  subtitle: "Hold kunden oppdatert mens du jobber – ikke bare når du er ferdig.",
  ctaPrimary: "Start gratis prøveperiode",
  ctaPrimaryHref: "/sign-up",
};

const FEATURE_PREVIEW = [
  {
    bg: "bg-jobblogg-primary/10 hover:bg-jobblogg-primary/20",
    iconClass: "text-jobblogg-primary",
    title: "Dokumenter underveis",
    text: "Vis fremgang mens du jobber, ikke bare sluttresultat.",
    icon: (
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
      />
    ),
  },
  {
    bg: "bg-jobblogg-success/10 hover:bg-jobblogg-success/20",
    iconClass: "text-jobblogg-success",
    title: "Kommuniser i sanntid",
    text: "Svar på spørsmål med en gang, unngå misforståelser.",
    icon: (
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
      />
    ),
  },
  {
    bg: "bg-jobblogg-accent/10 hover:bg-jobblogg-accent/20",
    iconClass: "text-jobblogg-accent",
    title: "Del løpende fremgang",
    text: "Kunden følger med dag for dag, ikke bare ved ferdigstilling.",
    icon: (
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
      />
    ),
  },
] as const;

const FEATURES = [
  {
    color: "primary",
    title: "Prosjektdokumentasjon",
    text: "Dokumenter arbeidsframgang med bilder og beskrivelser. Alt samlet på ett sted.",
    iconPath:
      "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",
  },
  {
    color: "success",
    title: "Sanntids-chat",
    text: "Kommuniser direkte med kunder og team. Rask avklaring, færre misforståelser.",
    iconPath:
      "M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z",
  },
  {
    color: "accent",
    title: "Enkel deling",
    text: "Del framdrift via sikre lenker. Kunden trenger ikke egen app.",
    iconPath:
      "M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z",
  },
  {
    color: "warning",
    title: "Sikker lagring",
    text: "Data krypteres og sikkerhetskopieres automatisk.",
    iconPath:
      "M12 15v2m-6 4h12a2 2 0 002 2h-2a2 2 0 01-2-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 01-2 2zm10-10V7a4 4 0 00-8 0v4h8z",
  },
  {
    color: "primary",
    title: "Team-samarbeid",
    text: "Inviter kollegaer og underleverandører. Roller og tilgangsstyring.",
    iconPath:
      "M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",
  },
  {
    color: "success",
    title: "Rapporter og innsikt",
    text: "Få oversikt over framdrift og kvalitet – delbart med ett klikk.",
    iconPath:
      "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
  },
] as const;

const STEPS = [
  {
    title: "Registrer deg og bedriften",
    text:
      "Opprett konto med e-post. Bedriftsinfo kan hentes fra Brønnøysund.",
    badge: "✓ Din bedriftsprofil er klar",
  },
  {
    title: "Opprett ditt første prosjekt",
    text: "Legg inn kundeinfo, adresse og varighet.",
    badge: "✓ Prosjektet er klart",
  },
  {
    title: "Dokumenter underveis",
    text: "Ta bilder og skriv beskrivelser mens du jobber – ikke bare til slutt.",
    badge: "✓ Fremgang dokumentert løpende",
  },
  {
    title: "Kunde følger prosessen",
    text:
      "Kunden får automatisk e-post invitasjon og ser utviklingen dag for dag.",
    badge: "✓ Kunden følger med underveis",
  },
] as const;

const PRICING_CTA = {
  title: "Klar til å komme i gang?",
  subtitle: "Prøv gratis i 7 dager – helt uten binding eller oppstartsgebyr.",
  price: "299 kr",
  period: "per måned",
  note: "ekskl. mva.",
  bullets: ["7 dagers gratis prøveperiode", "Ingen binding", "Norsk support"],
};

const FAQ = [
  {
    id: "getting-started",
    title: "Komme i gang",
    items: [
      {
        q: "Hvordan registrerer jeg bedriften?",
        a: "Opprett konto med e-post. Bedriftsinformasjon kan hentes automatisk fra Brønnøysundregisteret.",
      },
      {
        q: "Kan jeg bruke løsningen uten internett?",
        a: "JobbLogg fungerer best med nettforbindelse. Vi anbefaler mobilnett eller Wi-Fi på byggeplassen.",
      },
      {
        q: "Hvor mange prosjekter kan jeg ha?",
        a: "Ubegrenset – opprett så mange du vil.",
      },
      {
        q: "Er JobbLogg det samme som FDV-dokumentasjon?",
        a: "Nei, JobbLogg er for kommunikasjon og oppfølging underveis i prosjektet. Vi erstatter ikke FDV-dokumentasjon, men gjør prosessen frem til ferdigstilling mye bedre.",
      },
      {
        q: "Bruker jeg JobbLogg under arbeidet eller etter ferdigstilling?",
        a: "Under arbeidet! JobbLogg er laget for å holde kunden oppdatert mens du jobber, ikke bare når du er ferdig.",
      },
    ],
  },
  {
    id: "customer-team",
    title: "Kunde og team",
    items: [
      {
        q: "Hvordan inviterer jeg kunden?",
        a: "Kunden får en sikker lenke på e-post eller SMS. Ingen app kreves.",
      },
      {
        q: "Kan jeg legge til ansatte?",
        a: "Ja – inviter kollegaer og gi dem riktige roller og tilganger.",
      },
      {
        q: "Kan underleverandører bidra?",
        a: "Ja – inviter dem inn i prosjektet for å dokumentere sitt arbeid.",
      },
      {
        q: "Hva ser kunden?",
        a: "Bilder, beskrivelser og chat. Kunden kan ikke endre eller slette noe.",
      },
    ],
  },
  {
    id: "technical",
    title: "Teknisk",
    items: [
      {
        q: "Hvilke enheter støttes?",
        a: "Alle moderne mobiler, nettbrett og datamaskiner (iOS, Android og web).",
      },
      {
        q: "Hvor lagres data?",
        a: "Data lagres sikkert, krypteres og sikkerhetskopieres. Vi følger GDPR.",
      },
      {
        q: "Kan jeg eksportere data?",
        a: "Ja – som PDF-rapport eller ZIP når som helst.",
      },
    ],
  },
  {
    id: "pricing",
    title: "Priser og abonnement",
    items: [
      {
        q: "Hva koster det?",
        a:
          "Liten bedrift (1–9 ansatte): 299 kr per måned ekskl. mva (374 kr inkl. mva). Ubegrenset prosjekter og alle funksjoner. Start gratis i 7 dager.",
      },
      {
        q: "Finnes det andre planer?",
        a:
          "Ja – mellomstor (10–49) og stor (50–249). Se prissiden for detaljer.",
      },
      {
        q: "Er det binding eller oppstartsgebyr?",
        a: "Ingen binding, ingen oppstartsgebyr – og ingen skjulte kostnader.",
      },
    ],
  },
] as const;

/* ------------------------------------------------
   SMÅ PRESENTASJONSKOMPONENTER
-------------------------------------------------*/

const IconWrap: React.FC<{
  tone:
    | "primary"
    | "success"
    | "accent"
    | "warning"
    | "neutral";
  children: React.ReactNode;
  size?: "sm" | "md" | "lg";
}> = ({ tone, children, size = "md" }) => {
  const base =
    "rounded-xl flex items-center justify-center transition-all duration-300";
  const toneMap: Record<string, string> = {
    primary: "bg-jobblogg-primary/10 group-hover:bg-jobblogg-primary/20",
    success: "bg-jobblogg-success/10 group-hover:bg-jobblogg-success/20",
    accent: "bg-jobblogg-accent/10 group-hover:bg-jobblogg-accent/20",
    warning: "bg-jobblogg-warning/10 group-hover:bg-jobblogg-warning/20",
    neutral: "bg-jobblogg-card-bg",
  };
  const sizeMap = {
    sm: "w-10 h-10",
    md: "w-12 h-12",
    lg: "w-16 h-16",
  };
  return (
    <div className={`${base} ${toneMap[tone]} ${sizeMap[size]}`}>
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
        {children}
      </svg>
    </div>
  );
};

const FeatureCard: React.FC<{
  title: string;
  text: string;
  tone: "primary" | "success" | "accent" | "warning";
  iconPath: string;
}> = ({ title, text, tone, iconPath }) => (
  <div className="group bg-white rounded-xl lg:rounded-2xl border border-jobblogg-border shadow-soft p-6 lg:p-8 space-y-4 lg:space-y-6 hover:shadow-lg hover:scale-[1.02] transition-all duration-300 h-full">
    <IconWrap tone={tone} size="md">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={iconPath} />
    </IconWrap>
    <div>
      <h3 className={`font-semibold text-jobblogg-text-strong text-lg lg:text-xl`}>
        {title}
      </h3>
      <TextMuted className="text-sm lg:text-base leading-relaxed">{text}</TextMuted>
    </div>
  </div>
);

const StepItem: React.FC<{
  index: number;
  title: string;
  text: string;
  badge: string;
}> = ({ index, title, text, badge }) => (
  <div className="relative">
    <div className="flex items-start gap-4">
      <div className="flex-shrink-0">
        <div className="w-12 h-12 bg-jobblogg-primary rounded-xl flex items-center justify-center">
          <span className="text-white font-bold text-lg">{index}</span>
        </div>
      </div>
      <div className="flex-1">
        <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-2">{title}</h3>
        <TextMuted className="text-sm mb-3 leading-relaxed">{text}</TextMuted>
        <span className="inline-flex items-center px-3 py-1 rounded-full bg-jobblogg-success/10 text-jobblogg-success text-xs font-medium">
          {badge}
        </span>
      </div>
    </div>
  </div>
);

const BenefitPill: React.FC<{ text: string }> = ({ text }) => (
  <div className="flex items-center gap-2">
    <svg className="w-5 h-5 text-jobblogg-accent" fill="currentColor" viewBox="0 0 20 20">
      <path
        fillRule="evenodd"
        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
        clipRule="evenodd"
      />
    </svg>
    <span>{text}</span>
  </div>
);

const FAQSection: React.FC<{
  id: string;
  title: string;
  openId: string | null;
  onToggle: (id: string) => void;
  items: { q: string; a: string }[];
  tone?: "primary" | "success" | "accent" | "warning";
}> = ({ id, title, openId, onToggle, items, tone = "primary" }) => (
  <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft overflow-hidden">
    <button
      onClick={() => onToggle(id)}
      className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-jobblogg-card-bg transition-colors duration-200"
    >
      <div className="flex items-center gap-3">
        <IconWrap tone={tone} size="sm">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </IconWrap>
        <h3 className="font-semibold text-jobblogg-text-strong">{title}</h3>
      </div>
      <svg
        className={`w-5 h-5 text-jobblogg-text-medium transition-transform duration-200 ${
          openId === id ? "rotate-180" : ""
        }`}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    {openId === id && (
      <div className="px-6 pb-4 space-y-4 border-t border-jobblogg-border">
        {items.map(({ q, a }, i) => (
          <div key={i} className={i === 0 ? "pt-4" : ""}>
            <h4 className="font-medium text-jobblogg-text-strong mb-2">{q}</h4>
            <TextMuted className="text-sm">{a}</TextMuted>
          </div>
        ))}
      </div>
    )}
  </div>
);

/* ------------------------------------------------
   SIDE
-------------------------------------------------*/

export const LandingPage: React.FC = () => {
  const [openFaqSection, setOpenFaqSection] = useState<string | null>(null);
  const toggleFaqSection = (section: string) =>
    setOpenFaqSection((cur) => (cur === section ? null : section));

  return (
    <>
      <SEOHead
        title="JobbLogg – Dokumenter arbeidet ditt enkelt og profesjonelt"
        description="Transparent prosjektoppfølging for håndverkere og fagfolk. Del framgang med kunder i sanntid og bygg tillit gjennom åpenhet."
        keywords="håndverker, prosjektdokumentasjon, byggebransje, chat, dokumentasjon, kunde, kommunikasjon, Norge"
        url="/"
        type="website"
      />

      <div className="min-h-screen bg-white flex flex-col">
        <Header />

        <main className="flex-1 pt-16">
          <div className="space-y-16 sm:space-y-24">
            {/* HERO */}
            <section id="hero" className="container-wide px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-28">
              <div className="text-center space-y-8 lg:space-y-12">
                <div className="max-w-4xl mx-auto space-y-6 lg:space-y-8">
                  <div className="space-y-4">
                    <div className="inline-flex items-center px-4 py-2 bg-jobblogg-primary/10 rounded-full">
                      <span className="text-sm font-medium text-jobblogg-primary">{HERO.targetAudience}</span>
                    </div>
                    <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-jobblogg-text-strong leading-tight">
                      {HERO.titleTop}
                      <span className="block text-jobblogg-primary">{HERO.titleAccent}</span>
                    </h1>
                  </div>
                  <TextMedium className="text-lg sm:text-xl lg:text-2xl max-w-3xl mx-auto text-jobblogg-text-medium">
                    {HERO.subtitle}
                  </TextMedium>
                </div>

                <div className="flex justify-center pt-2">
                  <Link to={HERO.ctaPrimaryHref} className="w-full sm:w-auto">
                    <PrimaryButton
                      size="lg"
                      className="w-full sm:w-auto min-w-[220px]"
                      ariaLabel="Start gratis prøveperiode"
                    >
                      {HERO.ctaPrimary}
                    </PrimaryButton>
                  </Link>
                </div>

                {/* Feature preview (3 bobler) */}
                <div className="max-w-5xl mx-auto mt-16 lg:mt-20">
                  <div className="bg-gradient-to-br from-jobblogg-primary/5 to-jobblogg-accent/5 rounded-2xl lg:rounded-3xl p-6 sm:p-8 lg:p-12 border border-jobblogg-border">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-8 items-start">
                      {FEATURE_PREVIEW.map((f, i) => (
                        <div key={i} className="text-center space-y-4">
                          <div
                            className={`w-16 h-16 lg:w-20 lg:h-20 ${f.bg} rounded-xl lg:rounded-2xl flex items-center justify-center mx-auto transition-all duration-300`}
                          >
                            <svg className={`w-8 h-8 lg:w-10 lg:h-10 ${f.iconClass}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              {f.icon}
                            </svg>
                          </div>
                          <div>
                            <TextMedium className="font-semibold text-jobblogg-text-strong text-base lg:text-lg">
                              {f.title}
                            </TextMedium>
                            <TextMuted className="text-sm lg:text-base mt-1">{f.text}</TextMuted>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* PASSER FOR */}
            <section id="target-audience" className="bg-jobblogg-neutral-secondary">
              <div className="container-wide px-4 sm:px-6 lg:px-8 py-16 sm:py-20">
              <div className="text-center space-y-6 lg:space-y-8 mb-16">
                <Heading2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">
                  Passer for alle typer håndverksarbeid
                </Heading2>
                <TextMedium className="text-lg sm:text-xl max-w-3xl mx-auto text-jobblogg-text-medium">
                  Uansett om du jobber med rør, elektro, bygg eller andre fagområder – JobbLogg tilpasser seg ditt arbeid.
                </TextMedium>
              </div>

              <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* Rørleggere */}
                <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4 hover:shadow-lg transition-all duration-300">
                  <div className="w-16 h-16 bg-jobblogg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                    <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong text-lg">Rørleggere</h3>
                    <TextMuted className="text-sm">Dokumenter installasjoner, reparasjoner og rørføring</TextMuted>
                  </div>
                </div>

                {/* Elektrikere */}
                <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4 hover:shadow-lg transition-all duration-300">
                  <div className="w-16 h-16 bg-jobblogg-warning/10 rounded-xl flex items-center justify-center mx-auto">
                    <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong text-lg">Elektrikere</h3>
                    <TextMuted className="text-sm">Vis kabelføring, tavlearbeid og elektriske installasjoner</TextMuted>
                  </div>
                </div>

                {/* Malere */}
                <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4 hover:shadow-lg transition-all duration-300">
                  <div className="w-16 h-16 bg-jobblogg-accent/10 rounded-xl flex items-center justify-center mx-auto">
                    <svg className="w-8 h-8 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2a2 2 0 002-2V5a2 2 0 00-2-2z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 9h6l-6 6h6" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong text-lg">Malere</h3>
                    <TextMuted className="text-sm">Før/etter bilder og dokumenter malingsprosess</TextMuted>
                  </div>
                </div>

                {/* Tømrere */}
                <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4 hover:shadow-lg transition-all duration-300">
                  <div className="w-16 h-16 bg-jobblogg-success/10 rounded-xl flex items-center justify-center mx-auto">
                    <svg className="w-8 h-8 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong text-lg">Tømrere</h3>
                    <TextMuted className="text-sm">Byggeprosess, konstruksjoner og kvalitetskontroll</TextMuted>
                  </div>
                </div>

                {/* Entreprenører */}
                <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4 hover:shadow-lg transition-all duration-300">
                  <div className="w-16 h-16 bg-jobblogg-primary/10 rounded-xl flex items-center justify-center mx-auto">
                    <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong text-lg">Entreprenører</h3>
                    <TextMuted className="text-sm">Koordiner team og underleverandører på store prosjekter</TextMuted>
                  </div>
                </div>

                {/* Andre fagområder */}
                <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 text-center space-y-4 hover:shadow-lg transition-all duration-300">
                  <div className="w-16 h-16 bg-jobblogg-text-strong/10 rounded-xl flex items-center justify-center mx-auto">
                    <svg className="w-8 h-8 text-jobblogg-text-strong" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-jobblogg-text-strong text-lg">Andre fagområder</h3>
                    <TextMuted className="text-sm">HVAC, flislegging, tak, og alle andre håndverkstjenester</TextMuted>
                  </div>
                </div>
              </div>
              </div>
            </section>

            {/* IKKE BARE SLUTTDOKUMENTASJON */}
            <section className="container-wide px-4 sm:px-6 lg:px-8 py-16 sm:py-20">
              <div className="max-w-5xl mx-auto">
                <div className="text-center space-y-6 mb-16">
                  <Heading2 className="text-3xl sm:text-4xl font-bold">
                    Ikke bare sluttdokumentasjon
                  </Heading2>
                  <TextMedium className="text-lg max-w-3xl mx-auto text-jobblogg-text-medium">
                    JobbLogg er ikke en FDV-plattform. Vi fokuserer på kommunikasjon og oppfølging underveis i prosjektet.
                  </TextMedium>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
                  {/* Tradisjonell dokumentasjon */}
                  <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 lg:p-8 space-y-6">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                        <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-jobblogg-text-strong">Tradisjonell dokumentasjon</h3>
                    </div>
                    <ul className="space-y-3 text-jobblogg-text-medium">
                      <li className="flex items-start gap-3">
                        <span className="text-red-600 mt-1">•</span>
                        <span>Leveres når alt er ferdig</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="text-red-600 mt-1">•</span>
                        <span>FDV-dokumentasjon for drift og vedlikehold</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="text-red-600 mt-1">•</span>
                        <span>Kunden ser ikke prosessen</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="text-red-600 mt-1">•</span>
                        <span>Misforståelser oppdages for sent</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="text-red-600 mt-1">•</span>
                        <span>Lite kommunikasjon underveis</span>
                      </li>
                    </ul>
                  </div>

                  {/* JobbLogg */}
                  <div className="bg-white rounded-xl border border-jobblogg-success shadow-soft p-6 lg:p-8 space-y-6">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-jobblogg-success/10 rounded-lg flex items-center justify-center">
                        <svg className="w-5 h-5 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-jobblogg-text-strong">JobbLogg</h3>
                    </div>
                    <ul className="space-y-3 text-jobblogg-text-medium">
                      <li className="flex items-start gap-3">
                        <span className="text-jobblogg-success mt-1">•</span>
                        <span>Kommunikasjon og oppfølging underveis</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="text-jobblogg-success mt-1">•</span>
                        <span>Kunden følger prosessen dag for dag</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="text-jobblogg-success mt-1">•</span>
                        <span>Avklar spørsmål før de blir problemer</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="text-jobblogg-success mt-1">•</span>
                        <span>Sanntids chat og tilbakemeldinger</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <span className="text-jobblogg-success mt-1">•</span>
                        <span>Bedre sluttresultat gjennom løpende dialog</span>
                      </li>
                    </ul>
                  </div>
                </div>

                <div className="text-center mt-12">
                  <div className="inline-flex items-center px-6 py-3 bg-jobblogg-primary/10 rounded-xl">
                    <svg className="w-5 h-5 text-jobblogg-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm font-medium text-jobblogg-primary">
                      JobbLogg erstatter ikke FDV-dokumentasjon, men gjør prosessen frem til ferdigstilling mye bedre
                    </span>
                  </div>
                </div>
              </div>
            </section>

            {/* FUNKSJONER */}
            <section id="features" className="container-wide px-4 sm:px-6 lg:px-8 py-16 sm:py-20 lg:py-28">
              <div className="text-center space-y-6 lg:space-y-8 mb-16 lg:mb-20">
                <Heading2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">
                  Alt håndverkere trenger for profesjonell prosjektdokumentasjon
                </Heading2>
                <TextMedium className="text-lg sm:text-xl lg:text-2xl max-w-3xl mx-auto text-jobblogg-text-medium">
                  Bygg tillit med kunder gjennom åpen kommunikasjon og ryddig dokumentasjon.
                </TextMedium>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-10">
                {FEATURES.map((f, i) => (
                  <FeatureCard
                    key={i}
                    title={f.title}
                    text={f.text}
                    iconPath={f.iconPath}
                    tone={f.color}
                  />
                ))}
              </div>
            </section>

            {/* KOM I GANG */}
            <section id="getting-started" className="bg-jobblogg-neutral-secondary">
              <div className="container-wide px-4 sm:px-6 py-16 sm:py-24">
              <div className="text-center space-y-6 mb-16">
                <Heading2 className="text-3xl sm:text-4xl font-bold">
                  Fra registrering til første prosjekt på 5 minutter
                </Heading2>
                <TextMedium className="text-lg max-w-2xl mx-auto text-jobblogg-text-medium">
                  Kom i gang raskt og enkelt – ingen teknisk erfaring nødvendig.
                </TextMedium>
              </div>

              <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
                {STEPS.map((s, i) => (
                  <StepItem key={s.title} index={i + 1} title={s.title} text={s.text} badge={s.badge} />
                ))}
              </div>

              <div className="text-center mt-12">
                <Link to="/sign-up">
                  <PrimaryButton size="lg" ariaLabel="Start gratis prøveperiode">
                    Start gratis prøveperiode
                  </PrimaryButton>
                </Link>
              </div>
              </div>
            </section>

            {/* PRISING – CTA */}
            <section id="pricing-cta" className="container-wide px-4 sm:px-6 py-16 sm:py-24">
              <div className="max-w-4xl mx-auto">
                <div className="bg-gradient-to-br from-jobblogg-primary to-jobblogg-primary-dark rounded-2xl lg:rounded-3xl p-8 lg:p-12 text-center text-white shadow-large">
                  <div className="space-y-6 lg:space-y-8">
                    <div className="space-y-3">
                      <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">{PRICING_CTA.title}</h2>
                      <TextMedium className="text-lg sm:text-xl text-white/90 max-w-2xl mx-auto">
                        {PRICING_CTA.subtitle}
                      </TextMedium>
                    </div>

                    <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 max-w-md mx-auto">
                      <div className="text-center space-y-1">
                        <div className="text-4xl sm:text-5xl font-bold">{PRICING_CTA.price}</div>
                        <div className="text-lg text-white/80">{PRICING_CTA.period}</div>
                        <div className="text-sm text-white/70">{PRICING_CTA.note}</div>
                        <div className="text-sm text-white/70 mt-1">Ubegrenset prosjekter • Alle funksjoner</div>
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
                      <Link to="/sign-up" className="w-full sm:w-auto">
                        <button
                          className="w-full sm:w-auto min-w-[200px] bg-white text-jobblogg-primary hover:bg-gray-50 border border-white rounded-xl px-6 py-3.5 text-base sm:text-lg min-h-[48px] font-semibold inline-flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white shadow-soft hover:shadow-medium"
                          aria-label="Start gratis prøveperiode"
                        >
                          Start gratis prøveperiode
                        </button>
                      </Link>
                      <Link to="/pricing" className="w-full sm:w-auto">
                        <button
                          className="w-full sm:w-auto min-w-[160px] bg-transparent text-white hover:bg-white/10 border-2 border-white rounded-xl px-6 py-3.5 text-base sm:text-lg min-h-[48px] font-semibold inline-flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
                          aria-label="Se alle priser"
                        >
                          Se alle priser
                        </button>
                      </Link>
                    </div>

                    <div className="flex flex-col sm:flex-row items-center justify-center gap-6 pt-6 text-sm text-white/70">
                      {PRICING_CTA.bullets.map((b) => (
                        <BenefitPill key={b} text={b} />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* FAQ */}
            <section id="help" className="bg-jobblogg-neutral-secondary">
              <div className="container-wide px-4 sm:px-6 py-16 sm:py-24">
                <div className="text-center space-y-6 mb-16">
                  <Heading2 className="text-3xl sm:text-4xl font-bold">Vi hjelper deg i gang</Heading2>
                  <TextMedium className="text-lg max-w-2xl mx-auto text-jobblogg-text-medium">
                    Få svar på vanlige spørsmål – eller kontakt oss for personlig hjelp.
                  </TextMedium>
                </div>

              <div className="max-w-4xl mx-auto space-y-4">
                {FAQ.map((section, i) => (
                  <FAQSection
                    key={section.id}
                    id={section.id}
                    title={section.title}
                    items={section.items as unknown as { q: string; a: string; }[]}
                    openId={openFaqSection}
                    onToggle={toggleFaqSection}
                    tone={i % 4 === 0 ? "primary" : i % 4 === 1 ? "success" : i % 4 === 2 ? "accent" : "warning"}
                  />
                ))}
              </div>
              </div>
            </section>
          </div>
        </main>

        <Footer />
      </div>
    </>
  );
};

export default LandingPage;
