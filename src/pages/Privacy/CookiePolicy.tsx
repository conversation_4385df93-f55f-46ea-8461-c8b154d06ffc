import React from 'react';
import { PageLayout, Heading2, TextMedium, TextMuted } from '../../components/ui';
import { SEOHead } from '../../components/SEO';
import { useCookieConsent } from '../../hooks/useCookieConsent';

export const CookiePolicy: React.FC = () => {
  const { showConsentModal } = useCookieConsent();

  return (
    <>
      <SEOHead
        title="Cookie-erklæring - JobbLogg"
        description="Les hvordan JobbLogg bruker informasjonskapsler (cookies) for å forbedre din brukeropplevelse og levere tjenesten."
        keywords="cookies, informasjonskapsler, personvern, databehandling, GDPR"
        url="/cookie-policy"
        noIndex={false}
      />
    <PageLayout
      title="Informasjonskapsler (Cookies)"
      showBackButton
      backUrl="/"
      containerWidth="wide"
    >
      <div className="w-full px-4 sm:px-0 sm:max-w-4xl sm:mx-auto space-y-6 sm:space-y-8">
        {/* Introduction */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <div className="space-y-4">
            <TextMuted className="text-sm">
              Sist oppdatert: {new Date().toLocaleDateString('nb-NO')}
            </TextMuted>

            <TextMedium>
              Denne siden forklarer hvordan JobbLogg bruker informasjonskapsler (cookies)
              og lignende teknologier for å gjenkjenne deg når du besøker vår tjeneste.
            </TextMedium>
          </div>
        </div>

        {/* Data Controller */}
        <section className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Behandlingsansvarlig</Heading2>
          <div className="bg-jobblogg-neutral-secondary rounded-xl p-6 space-y-2">
            <TextMedium><strong>Bedrift:</strong> RH CONSULTING AS</TextMedium>
            <TextMedium><strong>Organisasjonsnummer:</strong> 918168117</TextMedium>
            <TextMedium><strong>Adresse:</strong> Nørvegata 34E, 6008 Ålesund</TextMedium>
            <TextMedium><strong>E-post:</strong> <EMAIL></TextMedium>
            <TextMedium><strong>Telefon:</strong> +47 966 44 444</TextMedium>
          </div>
        </section>

        {/* What are cookies */}
        <section className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Hva er informasjonskapsler?</Heading2>

          <TextMedium>
            Informasjonskapsler er små tekstfiler som lagres på din enhet (datamaskin,
            telefon eller nettbrett) når du besøker en nettside. De hjelper nettsiden
            med å huske informasjon om ditt besøk, som språkinnstillinger og andre
            preferanser.
          </TextMedium>

          <div className="bg-jobblogg-primary/5 border border-jobblogg-primary/10 rounded-xl p-6">
            <h3 className="font-semibold text-jobblogg-primary mb-3">
              Hvorfor bruker vi cookies?
            </h3>
            <ul className="space-y-2 text-jobblogg-text-medium ml-4">
              <li>• Holde deg innlogget mens du navigerer</li>
              <li>• Huske dine innstillinger og preferanser</li>
              <li>• Sikre at tjenesten fungerer trygt</li>
              <li>• Forbedre ytelsen til appen</li>
              <li>• Forstå hvordan du bruker tjenesten</li>
            </ul>
          </div>
        </section>

        {/* Types of cookies */}
        <section className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Hvilke typer cookies bruker vi?</Heading2>

          <div className="space-y-6">
            {/* Necessary cookies */}
            <div className="border border-jobblogg-success/20 bg-jobblogg-success/5 rounded-xl p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-jobblogg-success rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="font-semibold text-jobblogg-success text-lg">
                  Nødvendige cookies (Alltid aktive)
                </h3>
              </div>

              <TextMedium className="mb-4">
                Disse cookies er essensielle for at tjenesten skal fungere og kan ikke deaktiveres.
              </TextMedium>

              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">Autentisering</h4>
                  <TextMuted className="text-sm mb-2">
                    <strong>Leverandør:</strong> Clerk (autentiseringstjeneste)
                  </TextMuted>
                  <TextMuted className="text-sm mb-2">
                    <strong>Formål:</strong> Holder deg innlogget og sikrer trygg tilgang
                  </TextMuted>
                  <TextMuted className="text-sm">
                    <strong>Varighet:</strong> Sesjon eller 30 dager (avhengig av "Husk meg")
                  </TextMuted>
                </div>

                <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">Sikkerhet</h4>
                  <TextMuted className="text-sm mb-2">
                    <strong>Leverandør:</strong> JobbLogg
                  </TextMuted>
                  <TextMuted className="text-sm mb-2">
                    <strong>Formål:</strong> Beskytter mot CSRF-angrep og andre sikkerhetstrusler
                  </TextMuted>
                  <TextMuted className="text-sm">
                    <strong>Varighet:</strong> Sesjon
                  </TextMuted>
                </div>

                <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">Funksjonsdata</h4>
                  <TextMuted className="text-sm mb-2">
                    <strong>Leverandør:</strong> JobbLogg
                  </TextMuted>
                  <TextMuted className="text-sm mb-2">
                    <strong>Formål:</strong> Lagrer midlertidige data for skjemaer og brukergrensesnitt
                  </TextMuted>
                  <TextMuted className="text-sm">
                    <strong>Varighet:</strong> Sesjon eller 7 dager
                  </TextMuted>
                </div>
              </div>
            </div>

            {/* Functional cookies */}
            <div className="border border-jobblogg-primary/20 bg-jobblogg-primary/5 rounded-xl p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-jobblogg-primary rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-jobblogg-primary text-lg">
                  Funksjonelle cookies (Valgfrie)
                </h3>
              </div>
              
              <TextMedium className="mb-4">
                Disse cookies forbedrer funksjonaliteten og personaliseringen av tjenesten.
              </TextMedium>

              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">Brukerpreferanser</h4>
                  <TextMuted className="text-sm mb-2">
                    <strong>Leverandør:</strong> JobbLogg
                  </TextMuted>
                  <TextMuted className="text-sm mb-2">
                    <strong>Formål:</strong> Husker dine innstillinger som språk, visningsalternativer
                  </TextMuted>
                  <TextMuted className="text-sm">
                    <strong>Varighet:</strong> 1 år
                  </TextMuted>
                </div>

                <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">Chat-funksjonalitet</h4>
                  <TextMuted className="text-sm mb-2">
                    <strong>Leverandør:</strong> JobbLogg
                  </TextMuted>
                  <TextMuted className="text-sm mb-2">
                    <strong>Formål:</strong> Forbedrer chat-opplevelsen og varslingsfunksjoner
                  </TextMuted>
                  <TextMuted className="text-sm">
                    <strong>Varighet:</strong> 30 dager
                  </TextMuted>
                </div>
              </div>
            </div>

            {/* Analytics cookies */}
            <div className="border border-jobblogg-warning/20 bg-jobblogg-warning/5 rounded-xl p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-jobblogg-warning rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-jobblogg-warning text-lg">
                  Analyse-cookies (Valgfrie)
                </h3>
              </div>
              
              <TextMedium className="mb-4">
                Disse cookies hjelper oss å forstå hvordan tjenesten brukes og forbedre den.
              </TextMedium>

              <div className="space-y-4">
                <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">Bruksstatistikk</h4>
                  <TextMuted className="text-sm mb-2">
                    <strong>Leverandør:</strong> JobbLogg (anonymisert)
                  </TextMuted>
                  <TextMuted className="text-sm mb-2">
                    <strong>Formål:</strong> Måler bruk av funksjoner og identifiserer forbedringspunkter
                  </TextMuted>
                  <TextMuted className="text-sm">
                    <strong>Varighet:</strong> 2 år
                  </TextMuted>
                </div>

                <div className="bg-white rounded-lg p-4 border border-jobblogg-border">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">Ytelsesmåling</h4>
                  <TextMuted className="text-sm mb-2">
                    <strong>Leverandør:</strong> JobbLogg
                  </TextMuted>
                  <TextMuted className="text-sm mb-2">
                    <strong>Formål:</strong> Overvåker appens ytelse og lastetider
                  </TextMuted>
                  <TextMuted className="text-sm">
                    <strong>Varighet:</strong> 30 dager
                  </TextMuted>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Managing cookies */}
        <section className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Administrere cookies</Heading2>

          <div className="space-y-4">
            <div className="bg-jobblogg-primary/5 border border-jobblogg-primary/10 rounded-xl p-6">
              <h3 className="font-semibold text-jobblogg-primary mb-3">
                I JobbLogg
              </h3>
              <TextMedium className="mb-3">
                Du kan når som helst endre dine cookie-innstillinger ved å klikke på
                "Administrer cookies" nederst på siden eller i innstillingsmenyen.
              </TextMedium>
              <button
                className="inline-flex items-center gap-2 px-4 py-2 bg-jobblogg-primary text-white rounded-lg hover:bg-jobblogg-primary-dark transition-colors min-h-[44px] focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2"
                onClick={showConsentModal}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Administrer cookie-innstillinger
              </button>
            </div>

            <div className="bg-jobblogg-neutral-secondary rounded-xl p-6">
              <h3 className="font-semibold text-jobblogg-text-strong mb-3">
                I nettleseren din
              </h3>
              <TextMedium className="mb-3">
                Du kan også administrere cookies direkte i nettleseren din:
              </TextMedium>
              <ul className="space-y-2 text-jobblogg-text-medium ml-4">
                <li>• <strong>Chrome:</strong> Innstillinger → Personvern og sikkerhet → Cookies</li>
                <li>• <strong>Firefox:</strong> Innstillinger → Personvern og sikkerhet → Cookies</li>
                <li>• <strong>Safari:</strong> Innstillinger → Personvern → Administrer nettstedsdata</li>
                <li>• <strong>Edge:</strong> Innstillinger → Cookies og nettstedstillatelser</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Third party cookies */}
        <section className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Tredjepartscookies</Heading2>

          <TextMedium className="mb-4">
            Noen cookies settes av tredjepartstjenester vi bruker:
          </TextMedium>

          <div className="space-y-4">
            <div className="border border-jobblogg-border rounded-xl p-6">
              <h3 className="font-semibold text-jobblogg-text-strong mb-3">Clerk (Autentisering)</h3>
              <TextMuted className="text-sm mb-2">
                <strong>Personvernerklæring:</strong>{' '}
                <a
                  href="https://clerk.com/privacy"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-jobblogg-primary hover:underline"
                >
                  https://clerk.com/privacy
                </a>
              </TextMuted>
              <TextMuted className="text-sm">
                Clerk setter cookies for å håndtere sikker innlogging og brukeradministrasjon.
              </TextMuted>
            </div>

            <div className="border border-jobblogg-border rounded-xl p-6">
              <h3 className="font-semibold text-jobblogg-text-strong mb-3">Convex (Backend)</h3>
              <TextMuted className="text-sm mb-2">
                <strong>Personvernerklæring:</strong>{' '}
                <a
                  href="https://convex.dev/privacy"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-jobblogg-primary hover:underline"
                >
                  https://convex.dev/privacy
                </a>
              </TextMuted>
              <TextMuted className="text-sm">
                Convex kan sette tekniske cookies for å levere backend-tjenester.
              </TextMuted>
            </div>
          </div>
        </section>

        {/* Contact */}
        <section className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <Heading2>Spørsmål om cookies?</Heading2>

          <div className="bg-jobblogg-primary/5 border border-jobblogg-primary/10 rounded-xl p-6">
            <TextMedium>
              Har du spørsmål om hvordan vi bruker cookies? Kontakt oss på{' '}
              <a
                href="mailto:<EMAIL>"
                className="text-jobblogg-primary hover:underline min-h-[44px] inline-flex items-center focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2 rounded"
              >
                <EMAIL>
              </a>
            </TextMedium>
          </div>
        </section>
      </div>
    </PageLayout>
    </>
  );
};

export default CookiePolicy;
