import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  PageLayout,
  Heading2,
  Heading3,
  BodyText,
  TextStrong,
  TextMedium,
  TextMuted,
  PrimaryButton,
  SecondaryButton,
  ArchiveActions,
  ArchiveStatusBadge
} from '../../components/ui';
import { EmbeddedChatContainer } from '../../components/chat';
import { ProjectTeamSection } from '../../components/team';
import { ShareProjectModal } from '../../components/ShareProjectModal';
import { EditHistoryModal } from '../../components/EditHistoryModal';

const ProjectDetail: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { user } = useUser();

  // Modal states
  const [showShareModal, setShowShareModal] = useState(false);
  const [showEditHistory, setShowEditHistory] = useState<string | null>(null);

  // Get project data with team access validation
  const project = useQuery(
    api.projectsTeam.getByIdWithTeamAccess,
    projectId && user?.id ? {
      projectId: projectId as any,
      userId: user.id
    } : "skip"
  );

  // Loading state
  if (project === undefined) {
    return (
      <PageLayout containerWidth="wide">
        <div className="w-full px-4 sm:px-0 sm:max-w-4xl sm:mx-auto space-y-6 sm:space-y-8">
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                <BodyText className="text-jobblogg-text-muted">
                  Laster prosjektdetaljer...
                </BodyText>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Project not found or no access
  if (project === null || !projectId) {
    return (
      <PageLayout containerWidth="wide">
        <div className="w-full px-4 sm:px-0 sm:max-w-4xl sm:mx-auto space-y-6 sm:space-y-8">
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-jobblogg-error-soft flex items-center justify-center">
                <svg className="w-8 h-8 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <TextStrong className="text-xl text-jobblogg-error mb-2">
                Prosjekt ikke funnet
              </TextStrong>
              <BodyText className="text-jobblogg-text-muted mb-6">
                Du har ikke tilgang til dette prosjektet, eller det eksisterer ikke.
              </BodyText>
              <PrimaryButton onClick={() => navigate('/')}>
                Tilbake til oversikt
              </PrimaryButton>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Format dates
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('nb-NO', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <>
    <PageLayout containerWidth="wide">
      <div className="w-full px-4 sm:px-0 sm:max-w-4xl sm:mx-auto space-y-6 sm:space-y-8">

        {/* Header with navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <SecondaryButton
              onClick={() => navigate(`/project/${projectId}`)}
              variant="ghost"
              size="sm"
              aria-label="Tilbake til prosjekt"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Tilbake til prosjekt
            </SecondaryButton>
          </div>

          <div className="flex items-center gap-2">
            {/* Edit button - only show if user has edit access */}
            {(project.userAccessLevel === 'owner' || project.userAccessLevel === 'editor') && (
              <PrimaryButton
                onClick={() => navigate(`/project/${projectId}/edit`)}
                size="sm"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Rediger prosjekt
              </PrimaryButton>
            )}
          </div>
        </div>

        {/* Project Overview Card */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <Heading2 className="mb-2">{project.name}</Heading2>
              {project.description && (
                <BodyText className="text-jobblogg-text-muted mb-4">
                  {project.description}
                </BodyText>
              )}

              {/* Project Status */}
              <div className="flex items-center gap-4 text-sm flex-wrap">
                <ArchiveStatusBadge isArchived={project.isArchived} />
                <div className="text-jobblogg-text-muted">
                  Opprettet: {formatDate(project.createdAt)}
                </div>
                {project.updatedAt && project.updatedAt !== project.createdAt && (
                  <div className="text-jobblogg-text-muted">
                    Sist oppdatert: {formatDateTime(project.updatedAt)}
                  </div>
                )}
              </div>
            </div>

            {/* Access Level Badge */}
            <div className="flex-shrink-0">
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                project.userAccessLevel === 'owner'
                  ? 'bg-jobblogg-primary-soft text-jobblogg-primary'
                  : project.userAccessLevel === 'editor'
                  ? 'bg-jobblogg-accent-soft text-jobblogg-accent'
                  : 'bg-jobblogg-neutral text-jobblogg-text-muted'
              }`}>
                {project.userAccessLevel === 'owner' && 'Eier'}
                {project.userAccessLevel === 'editor' && 'Redigerer'}
                {project.userAccessLevel === 'viewer' && 'Leser'}
                {project.userAccessLevel === 'subcontractor' && 'Underleverandør'}
              </div>
            </div>
          </div>
        </div>

        {/* Customer Information Card */}
        {project.customer && (
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
            <div className="flex items-center gap-3 mb-4">
              <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <TextStrong className="text-lg">Kundeinformasjon</TextStrong>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <TextMedium className="text-jobblogg-text-muted text-sm mb-1">Kunde</TextMedium>
                  <TextStrong>{project.customer.name}</TextStrong>
                  {project.customer.type === 'bedrift' && project.customer.organizationNumber && (
                    <BodyText className="text-jobblogg-text-muted text-sm">
                      Org.nr: {project.customer.organizationNumber}
                    </BodyText>
                  )}
                </div>

                {project.customer.contactPerson && (
                  <div>
                    <TextMedium className="text-jobblogg-text-muted text-sm mb-1">Kontaktperson</TextMedium>
                    <BodyText>{project.customer.contactPerson}</BodyText>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {project.customer.phone && (
                  <div>
                    <TextMedium className="text-jobblogg-text-muted text-sm mb-1">Telefon</TextMedium>
                    <BodyText>
                      <a
                        href={`tel:${project.customer.phone}`}
                        className="text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors"
                      >
                        {project.customer.phone}
                      </a>
                    </BodyText>
                  </div>
                )}

                {project.customer.email && (
                  <div>
                    <TextMedium className="text-jobblogg-text-muted text-sm mb-1">E-post</TextMedium>
                    <BodyText>
                      <a
                        href={`mailto:${project.customer.email}`}
                        className="text-jobblogg-primary hover:text-jobblogg-primary-dark transition-colors"
                      >
                        {project.customer.email}
                      </a>
                    </BodyText>
                  </div>
                )}
              </div>
            </div>

            {/* Address Information */}
            {(project.customer.address || project.customer.visitingAddress) && (
              <div className="border-t border-jobblogg-border pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {project.customer.address && (
                    <div>
                      <TextMedium className="text-jobblogg-text-muted text-sm mb-2">Adresse</TextMedium>
                      <BodyText className="whitespace-pre-line">{project.customer.address}</BodyText>
                    </div>
                  )}

                  {project.customer.visitingAddress && project.customer.visitingAddress !== project.customer.address && (
                    <div>
                      <TextMedium className="text-jobblogg-text-muted text-sm mb-2">Besøksadresse</TextMedium>
                      <BodyText className="whitespace-pre-line">{project.customer.visitingAddress}</BodyText>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Project Job Data */}
        {project.jobData && (
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8 space-y-6">
            <div className="flex items-center gap-3 mb-4">
              <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              <TextStrong className="text-lg">Prosjektdetaljer</TextStrong>
            </div>

            <div className="space-y-6">
              {project.jobData.jobDescription && (
                <div>
                  <TextMedium className="text-jobblogg-text-muted text-sm mb-2">Jobbeskrivelse</TextMedium>
                  <BodyText className="whitespace-pre-line bg-jobblogg-neutral p-4 rounded-lg">
                    {project.jobData.jobDescription}
                  </BodyText>
                </div>
              )}

              {project.jobData.accessNotes && (
                <div>
                  <TextMedium className="text-jobblogg-text-muted text-sm mb-2">Tilgangsnotater</TextMedium>
                  <BodyText className="whitespace-pre-line bg-jobblogg-neutral p-4 rounded-lg">
                    {project.jobData.accessNotes}
                  </BodyText>
                </div>
              )}

              {project.jobData.equipmentNeeds && (
                <div>
                  <TextMedium className="text-jobblogg-text-muted text-sm mb-2">Utstyrsbehov</TextMedium>
                  <BodyText className="whitespace-pre-line bg-jobblogg-neutral p-4 rounded-lg">
                    {project.jobData.equipmentNeeds}
                  </BodyText>
                </div>
              )}

              {project.jobData.unresolvedQuestions && (
                <div>
                  <TextMedium className="text-jobblogg-text-muted text-sm mb-2">Uløste spørsmål</TextMedium>
                  <BodyText className="whitespace-pre-line bg-jobblogg-warning-soft p-4 rounded-lg border border-jobblogg-warning/20">
                    {project.jobData.unresolvedQuestions}
                  </BodyText>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Team Members Section */}
        {project.userAccessLevel === 'owner' && (
          <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
            <div className="flex items-center gap-3 mb-6">
              <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.196-2.121M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <TextStrong className="text-lg">Teammedlemmer</TextStrong>
            </div>

            <ProjectTeamSection
              projectId={projectId as any}
              userRole={project.userAccessLevel}
              showInviteButton={true}
            />
          </div>
        )}

        {/* Communication Section */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
          <div className="flex items-center gap-3 mb-6">
            <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <TextStrong className="text-lg">Prosjektsamtaler</TextStrong>
          </div>

          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-jobblogg-neutral flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <TextStrong className="mb-2">Samtaler er tilgjengelige i prosjektloggen</TextStrong>
            <BodyText className="text-jobblogg-text-muted mb-4">
              Kommuniser med kunder og teammedlemmer direkte i prosjektloggen hvor alle aktiviteter dokumenteres.
            </BodyText>
            <PrimaryButton
              onClick={() => navigate(`/project/${projectId}`)}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Gå til prosjektlogg
            </PrimaryButton>
          </div>
        </div>

        {/* Project History & Activity Section */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
          <div className="flex items-center gap-3 mb-6">
            <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <TextStrong className="text-lg">Prosjekthistorikk</TextStrong>
          </div>

          <div className="space-y-4">
            {/* Project Edit History Summary */}
            <div className="flex items-center justify-between p-4 bg-jobblogg-neutral rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
                <div>
                  <TextStrong className="text-sm">Prosjektinformasjon</TextStrong>
                  <TextMuted className="text-xs">
                    {project.updatedAt && project.updatedAt !== project.createdAt
                      ? `Sist redigert: ${formatDateTime(project.updatedAt)}`
                      : 'Ikke redigert siden opprettelse'
                    }
                  </TextMuted>
                </div>
              </div>
              {(project.userAccessLevel === 'owner' || project.userAccessLevel === 'editor') && (
                <SecondaryButton
                  onClick={() => navigate(`/project/${projectId}/edit`)}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  Rediger prosjekt
                </SecondaryButton>
              )}
            </div>

            {/* Log Entry History Access */}
            <div className="flex items-center justify-between p-4 bg-jobblogg-neutral rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-jobblogg-accent-soft rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <TextStrong className="text-sm">Loggoppføringer</TextStrong>
                  <TextMuted className="text-xs">
                    Redigeringshistorikk for individuelle loggoppføringer
                  </TextMuted>
                </div>
              </div>
              <SecondaryButton
                onClick={() => navigate(`/project/${projectId}`)}
                size="sm"
                className="flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Se logghistorikk
              </SecondaryButton>
            </div>

            {/* Activity Summary */}
            <div className="p-4 bg-jobblogg-success-soft border border-jobblogg-success/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <TextStrong className="text-sm text-jobblogg-success">Aktivitetsoversikt</TextStrong>
              </div>
              <TextMuted className="text-xs">
                Alle endringer og aktiviteter spores automatisk for full transparens og dokumentasjon.
                Redigeringshistorikk er tilgjengelig for alle loggoppføringer som har blitt endret.
              </TextMuted>
            </div>
          </div>
        </div>

        {/* Project Actions */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
          <div className="flex items-center gap-3 mb-4">
            <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
            </svg>
            <TextStrong className="text-lg">Handlinger</TextStrong>
          </div>

          <div className="flex flex-col sm:flex-row gap-3">
            <PrimaryButton
              onClick={() => navigate(`/project/${projectId}`)}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              Gå til prosjektlogg
            </PrimaryButton>

            {/* Share Project Button */}
            <SecondaryButton
              onClick={() => setShowShareModal(true)}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              Administrer deling
            </SecondaryButton>

            {/* Archive/Restore Actions */}
            {(project.userAccessLevel === 'owner' || project.userAccessLevel === 'editor') && (
              <ArchiveActions
                projectId={projectId as any}
                isArchived={project.isArchived}
                onArchiveComplete={() => {
                  // Refresh will happen automatically via Convex reactivity
                }}
                onRestoreComplete={() => {
                  // Refresh will happen automatically via Convex reactivity
                }}
                variant="button"
              />
            )}
          </div>
        </div>

      </div>
    </PageLayout>

    {/* Share Project Modal */}
    {showShareModal && (
      <ShareProjectModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        project={{
          _id: project._id,
          name: project.name,
          sharedId: project.sharedId,
          isPubliclyShared: project.isPubliclyShared,
          shareSettings: project.shareSettings
        }}
        userId={user?.id || ''}
      />
    )}

    {/* Edit History Modal - for future use if needed */}
    {showEditHistory && (
      <EditHistoryModal
        entryId={showEditHistory}
        isOpen={!!showEditHistory}
        onClose={() => setShowEditHistory(null)}
        isSharedView={false}
      />
    )}
  </>
  );
};

export default ProjectDetail;
