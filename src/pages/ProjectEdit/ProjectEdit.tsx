import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading2, Heading3, TextInput, TextArea, FormError, SubmitButton, PrimaryButton, SelectInput, PhoneInput, AddressAutocomplete } from '../../components/ui';
import type { AddressSuggestion } from '../../components/ui';
import { validateNorwegianPhone } from '../../components/ui/Form/PhoneInput';

const ProjectEdit: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { user } = useUser();

  // State
  const [formData, setFormData] = useState({
    // Project fields
    name: '',
    description: '',
    // Customer fields
    customerName: '',
    customerType: 'privat' as 'privat' | 'bedrift',
    contactPerson: '',
    phone: '',
    email: '',
    address: '',
    orgNumber: '',
    notes: '',
    // Job data fields
    jobDescription: '',
    accessNotes: '',
    equipmentNeeds: '',
    unresolvedQuestions: ''
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [phoneError, setPhoneError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Handle phone validation
  const handlePhoneValidation = (_isValid: boolean, error?: string) => {
    setPhoneError(error || '');
  };

  // Mutations and queries
  const updateProject = useMutation(api.projects.updateProject);
  const updateCustomer = useMutation(api.customers.update);
  const updateProjectJobData = useMutation(api.projects.updateProjectJobData);
  
  // Get project data
  const project = useQuery(
    api.projects.getById,
    projectId ? { projectId: projectId as any } : "skip"
  );

  // Get user's access level for this project
  const userAccess = useQuery(
    api.teamManagement.validateUserProjectAccess,
    projectId && user?.id ? {
      clerkUserId: user.id,
      projectId: projectId as any,
    } : "skip"
  );

  // Load project data into form when available
  useEffect(() => {
    if (project) {
      const projectData = project as any;
      const customer = projectData.customer;
      const jobData = projectData.jobData;

      setFormData({
        // Project fields
        name: projectData.name || '',
        description: projectData.description || '',
        // Customer fields
        customerName: customer?.name || '',
        customerType: customer?.type || 'privat',
        contactPerson: customer?.contactPerson || '',
        phone: customer?.phone || '',
        email: customer?.email || '',
        address: customer?.address || '',
        orgNumber: customer?.orgNumber || '',
        notes: customer?.notes || '',
        // Job data fields
        jobDescription: jobData?.jobDescription || '',
        accessNotes: jobData?.accessNotes || '',
        equipmentNeeds: jobData?.equipmentNeeds || '',
        unresolvedQuestions: jobData?.unresolvedQuestions || ''
      });
    }
  }, [project]);

  // Check if user has edit permissions
  const canEdit = userAccess?.hasAccess && (
    userAccess.accessLevel === "owner" || 
    userAccess.accessLevel === "administrator" || 
    userAccess.accessLevel === "collaborator" ||
    userAccess.accessLevel === "subcontractor"
  );

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.id || !projectId) {
      setErrors({ general: 'Brukerinformasjon mangler' });
      return;
    }

    if (!canEdit) {
      setErrors({ general: 'Du har ikke tillatelse til å redigere dette prosjektet' });
      return;
    }

    // Validate form
    const newErrors: {[key: string]: string} = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Prosjektnavn er påkrevd';
    }

    if (!formData.customerName.trim()) {
      newErrors.customerName = 'Kundenavn er påkrevd';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Telefonnummer er påkrevd';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'E-postadresse er påkrevd';
    }

    if (!formData.address.trim()) {
      newErrors.address = 'Adresse er påkrevd';
    }

    // Phone validation
    if (phoneError) {
      newErrors.phone = phoneError;
    }

    // Email validation
    if (formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = 'Ugyldig e-postadresse';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      setIsLoading(true);
      setErrors({});

      const projectData = project as any;
      const customerId = projectData.customer?._id;

      // Update project basic information
      await updateProject({
        projectId: projectId as any,
        userId: user.id,
        name: formData.name.trim(),
        description: formData.description.trim()
      });

      // Update customer information if customer exists
      if (customerId) {
        await updateCustomer({
          customerId: customerId,
          userId: user.id,
          projectId: projectId as any,
          name: formData.customerName.trim(),
          type: formData.customerType,
          contactPerson: formData.contactPerson.trim() || undefined,
          phone: formData.phone.trim(),
          email: formData.email.trim(),
          address: formData.address.trim(),
          orgNumber: formData.orgNumber.trim() || undefined,
          notes: formData.notes.trim() || undefined
        });
      }

      // Update job data if any job fields have content
      const hasJobData = formData.jobDescription.trim() ||
                        formData.accessNotes.trim() ||
                        formData.equipmentNeeds.trim() ||
                        formData.unresolvedQuestions.trim();

      if (hasJobData) {
        await updateProjectJobData({
          projectId: projectId as any,
          userId: user.id,
          jobData: {
            jobDescription: formData.jobDescription.trim(),
            photos: [], // Keep existing photos, don't modify them in edit form
            accessNotes: formData.accessNotes.trim(),
            equipmentNeeds: formData.equipmentNeeds.trim(),
            unresolvedQuestions: formData.unresolvedQuestions.trim()
          }
        });
      }

      // Show success message
      setShowSuccess(true);
      setTimeout(() => {
        setShowSuccess(false);
        navigate(`/project/${projectId}/details`); // Navigate back to project details
      }, 2000);

    } catch (error) {
      console.error('Error updating project:', error);
      setErrors({
        general: error instanceof Error ? error.message : 'Det oppstod en feil ved oppdatering av prosjektet. Prøv igjen.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state
  if (!project || userAccess === undefined) {
    return (
      <PageLayout
        showBackButton
        backUrl={`/project/${projectId}/details`}
        containerWidth="narrow"
        showFooter={false}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
            <p className="text-jobblogg-text-muted">Laster prosjektinformasjon...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Access denied
  if (!canEdit) {
    return (
      <PageLayout
        showBackButton
        backUrl={`/project/${projectId}/details`}
        containerWidth="narrow"
        showFooter={false}
      >
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <Heading2 className="mb-4">Ingen tilgang</Heading2>
          <p className="text-jobblogg-text-muted mb-6">
            Du har ikke tillatelse til å redigere dette prosjektet.
          </p>
          <PrimaryButton onClick={() => navigate(`/project/${projectId}/details`)}>
            Tilbake til prosjekt
          </PrimaryButton>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout
      showBackButton
      backUrl={`/project/${projectId}/details`}
      containerWidth="narrow"
      showFooter={false}
    >
      <div className="space-y-6">
        <div className="text-center">
          <Heading2>Rediger prosjekt</Heading2>
          <p className="text-jobblogg-text-muted mt-2">
            Oppdater prosjektinformasjon, kundeopplysninger og jobbdetaljer
          </p>
        </div>

        {/* Success Message */}
        {showSuccess && (
          <div className="bg-jobblogg-success-soft border border-jobblogg-success/20 rounded-lg p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <svg className="w-5 h-5 text-jobblogg-success mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <span className="font-medium text-jobblogg-success">Prosjekt oppdatert!</span>
            </div>
            <p className="text-sm text-jobblogg-success">Sender deg tilbake til prosjektdetaljene...</p>
          </div>
        )}

        {/* Edit Form */}
        <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 sm:p-8">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Project Name Field */}
            <TextInput
              label="Prosjektnavn"
              placeholder="F.eks. Kjøkkenrenovering, Terrasse bygging..."
              required
              fullWidth
              size="large"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              error={errors.name}
            />

            {/* Description Field */}
            <TextArea
              label="Beskrivelse"
              placeholder="Beskriv hva prosjektet handler om, mål, eller andre viktige detaljer..."
              fullWidth
              rows={4}
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              helperText="💡 Tips: En god beskrivelse hjelper deg å holde oversikt senere"
            />

            {/* Customer Information Section */}
            <div className="border-t border-jobblogg-border pt-8">
              <Heading3 className="mb-6 flex items-center gap-2">
                <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Kundeinformasjon
              </Heading3>

              <div className="space-y-6">
                {/* Customer Type */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-jobblogg-text-strong">
                    Kundetype <span className="text-jobblogg-error">*</span>
                  </label>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <label className="flex items-center gap-3 cursor-pointer min-h-[44px] p-3 rounded-lg border border-jobblogg-border hover:border-jobblogg-primary transition-colors">
                      <input
                        type="radio"
                        name="customerType"
                        value="privat"
                        checked={formData.customerType === 'privat'}
                        onChange={(e) => setFormData(prev => ({ ...prev, customerType: e.target.value as 'privat' | 'bedrift' }))}
                        className="w-5 h-5 text-jobblogg-primary focus:ring-jobblogg-primary border-jobblogg-border"
                      />
                      <span className="text-sm font-medium text-jobblogg-text-strong">Privatkunde</span>
                    </label>
                    <label className="flex items-center gap-3 cursor-pointer min-h-[44px] p-3 rounded-lg border border-jobblogg-border hover:border-jobblogg-primary transition-colors">
                      <input
                        type="radio"
                        name="customerType"
                        value="bedrift"
                        checked={formData.customerType === 'bedrift'}
                        onChange={(e) => setFormData(prev => ({ ...prev, customerType: e.target.value as 'privat' | 'bedrift' }))}
                        className="w-5 h-5 text-jobblogg-primary focus:ring-jobblogg-primary border-jobblogg-border"
                      />
                      <span className="text-sm font-medium text-jobblogg-text-strong">Bedriftskunde</span>
                    </label>
                  </div>
                </div>

                {/* Customer Name */}
                <TextInput
                  label={formData.customerType === 'bedrift' ? 'Bedriftsnavn' : 'Kundenavn'}
                  placeholder={formData.customerType === 'bedrift' ? 'F.eks. Acme AS' : 'F.eks. Ola Nordmann'}
                  required
                  fullWidth
                  size="large"
                  value={formData.customerName}
                  onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                  error={errors.customerName}
                />

                {/* Organization Number (for bedrift) */}
                {formData.customerType === 'bedrift' && (
                  <TextInput
                    label="Organisasjonsnummer"
                    placeholder="F.eks. 123456789"
                    fullWidth
                    size="large"
                    value={formData.orgNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, orgNumber: e.target.value }))}
                    error={errors.orgNumber}
                    helperText="9-sifret organisasjonsnummer"
                  />
                )}

                {/* Contact Person */}
                <TextInput
                  label="Kontaktperson"
                  placeholder="F.eks. Ola Nordmann"
                  fullWidth
                  value={formData.contactPerson}
                  onChange={(e) => setFormData(prev => ({ ...prev, contactPerson: e.target.value }))}
                  helperText="Navn på kontaktperson for dette prosjektet"
                />

                {/* Phone and Email */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <PhoneInput
                    label={formData.customerType === 'bedrift' ? "Telefon (Kontaktperson)" : "Telefonnummer"}
                    required
                    fullWidth
                    value={formData.phone}
                    onChange={(value) => setFormData(prev => ({ ...prev, phone: value }))}
                    error={phoneError || errors.phone}
                    helperText={formData.customerType === 'bedrift' ? "Telefonnummer til kontaktperson (påkrevd)" : "For rask kontakt (påkrevd)"}
                    phoneType="mobile"
                    enableValidation={true}
                    onValidation={handlePhoneValidation}
                  />
                  <TextInput
                    label={formData.customerType === 'bedrift' ? "E-post (Kontaktperson)" : "E-postadresse"}
                    type="email"
                    placeholder="F.eks. <EMAIL>"
                    required
                    fullWidth
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    error={errors.email}
                    helperText={formData.customerType === 'bedrift' ? "E-postadresse til kontaktperson (påkrevd)" : "For rapporter og varsling (påkrevd)"}
                  />
                </div>

                {/* Project Address */}
                <AddressAutocomplete
                  label="Prosjektadresse"
                  placeholder="F.eks. Storgata 15"
                  required
                  fullWidth
                  value={formData.address}
                  onChange={(value) => setFormData(prev => ({ ...prev, address: value }))}
                  onAddressSelect={(suggestion: AddressSuggestion) => {
                    setFormData(prev => ({
                      ...prev,
                      address: suggestion.fullAddress
                    }));
                  }}
                  error={errors.address}
                  helperText="Start å skrive for å få forslag til adresser"
                />

                {/* Customer Notes */}
                <TextArea
                  label="Notater"
                  placeholder="F.eks. portkode, tilgjengelighet, spesielle ønsker..."
                  fullWidth
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  helperText="Notater som kan være nyttige under prosjektet"
                />
              </div>
            </div>

            {/* Job Data Section */}
            <div className="border-t border-jobblogg-border pt-8">
              <Heading3 className="mb-6 flex items-center gap-2">
                <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v11a2 2 0 002 2h9.586a1 1 0 00.707-.293l5.414-5.414a1 1 0 00.293-.707V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                Jobbinformasjon
              </Heading3>

              <div className="space-y-6">
                {/* Job Description */}
                <TextArea
                  label="Hva skal gjøres?"
                  placeholder="Beskriv detaljert hva som skal utføres i dette prosjektet..."
                  fullWidth
                  rows={4}
                  value={formData.jobDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, jobDescription: e.target.value }))}
                  helperText="Detaljert beskrivelse av arbeidet som skal utføres"
                />

                {/* Access Notes */}
                <TextArea
                  label="Tilkomst og forhold"
                  placeholder="Beskriv tilkomst til arbeidsstedet, parkeringsmuligheter, spesielle forhold..."
                  fullWidth
                  rows={3}
                  value={formData.accessNotes}
                  onChange={(e) => setFormData(prev => ({ ...prev, accessNotes: e.target.value }))}
                  helperText="Informasjon om tilkomst og arbeidsforhold"
                />

                {/* Equipment Needs */}
                <TextArea
                  label="Hva må medbringes?"
                  placeholder="Liste over verktøy, materialer og utstyr som trengs..."
                  fullWidth
                  rows={3}
                  value={formData.equipmentNeeds}
                  onChange={(e) => setFormData(prev => ({ ...prev, equipmentNeeds: e.target.value }))}
                  helperText="Verktøy, materialer og utstyr som må medbringes"
                />

                {/* Unresolved Questions */}
                <TextArea
                  label="Hva må avklares?"
                  placeholder="Spørsmål som må avklares før eller under arbeidet..."
                  fullWidth
                  rows={3}
                  value={formData.unresolvedQuestions}
                  onChange={(e) => setFormData(prev => ({ ...prev, unresolvedQuestions: e.target.value }))}
                  helperText="Åpne spørsmål som trenger avklaring"
                />
              </div>
            </div>

            {/* General Error */}
            {errors.general && <FormError>{errors.general}</FormError>}

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <SubmitButton
                loading={isLoading}
                loadingText="Lagrer endringer..."
                className="flex-1"
              >
                Lagre endringer
              </SubmitButton>
              
              <PrimaryButton
                type="button"
                variant="secondary"
                onClick={() => navigate(`/project/${projectId}/details`)}
                className="flex-1"
              >
                Avbryt
              </PrimaryButton>
            </div>
          </form>
        </div>
      </div>
    </PageLayout>
  );
};

export default ProjectEdit;
