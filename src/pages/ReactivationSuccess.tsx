import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useUser } from '@clerk/clerk-react';
import { useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';
import { PageLayout } from '../components/layout/PageLayout';
import { Heading1, Heading2 } from '../components/ui/Typography';
import { PrimaryButton, SecondaryButton } from '../components/ui/Button';

export const ReactivationSuccess: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useUser();
  
  const reactivationId = searchParams.get('reactivation_id');
  const sessionId = searchParams.get('session_id');

  // Get reactivation details if available
  const reactivationHistory = useQuery(
    api.subscriptionReactivation?.getReactivationHistory,
    user ? { userId: user.id } : "skip"
  );

  const latestReactivation = reactivationHistory?.[0]; // Most recent reactivation

  useEffect(() => {
    // Auto-redirect to dashboard after 10 seconds
    const timer = setTimeout(() => {
      navigate('/dashboard');
    }, 10000);

    return () => clearTimeout(timer);
  }, [navigate]);

  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  const handleViewSubscription = () => {
    navigate('/subscription');
  };

  return (
    <PageLayout 
      title="Velkommen tilbake!" 
      containerWidth="medium" 
      showFooter={false}
      className="bg-gradient-to-br from-jobblogg-success-light to-jobblogg-primary-light min-h-screen"
    >
      <div className="max-w-2xl mx-auto">
        {/* Success Header */}
        <div className="text-center py-12 space-y-6">
          <div className="w-20 h-20 bg-jobblogg-success rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          
          <div>
            <Heading1 className="mb-4 text-jobblogg-text">Velkommen tilbake til JobbLogg! 🎉</Heading1>
            <p className="text-lg text-jobblogg-text-muted max-w-md mx-auto">
              Ditt abonnement er reaktivert og du har full tilgang til alle funksjoner igjen.
            </p>
          </div>
        </div>

        {/* Reactivation Details */}
        {latestReactivation && (
          <div className="bg-white rounded-xl shadow-soft p-6 mb-8 space-y-4">
            <Heading2 className="text-center">Abonnementsdetaljer</Heading2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm text-jobblogg-text-muted">Plan:</div>
                <div className="font-medium text-jobblogg-text capitalize">
                  {latestReactivation.newPlanId}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm text-jobblogg-text-muted">Fakturering:</div>
                <div className="font-medium text-jobblogg-text">
                  {latestReactivation.newBillingInterval === 'year' ? 'Årlig' : 'Månedlig'}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm text-jobblogg-text-muted">Reaktivert:</div>
                <div className="font-medium text-jobblogg-text">
                  {new Date(latestReactivation.createdAt).toLocaleDateString('nb-NO')}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm text-jobblogg-text-muted">Spesialtilbud:</div>
                <div className="font-medium text-jobblogg-success">
                  {latestReactivation.welcomeBackDiscount}% rabatt første måned! 🎁
                </div>
              </div>
            </div>
          </div>
        )}

        {/* What's Next */}
        <div className="bg-white rounded-xl shadow-soft p-6 mb-8">
          <Heading2 className="mb-4">Hva skjer nå?</Heading2>
          
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-jobblogg-success rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-jobblogg-text">Umiddelbar tilgang</h4>
                <p className="text-sm text-jobblogg-text-muted">
                  Du har nå full tilgang til alle funksjoner i din plan
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-jobblogg-success rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-jobblogg-text">Dataene dine er tilbake</h4>
                <p className="text-sm text-jobblogg-text-muted">
                  Alle prosjektene og dataene dine er tilgjengelige som før
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-jobblogg-success rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-jobblogg-text">Teamtilgang gjenopprettet</h4>
                <p className="text-sm text-jobblogg-text-muted">
                  Teammedlemmene dine får automatisk tilgang igjen
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-jobblogg-warning rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-jobblogg-text">Bekreftelse på e-post</h4>
                <p className="text-sm text-jobblogg-text-muted">
                  Du vil motta en e-post med alle detaljene om reaktiveringen
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Welcome Back Offer */}
        <div className="bg-gradient-to-r from-jobblogg-primary-light to-jobblogg-success-light rounded-xl p-6 mb-8">
          <div className="text-center space-y-3">
            <h3 className="text-lg font-semibold text-jobblogg-text">🎁 Velkommen tilbake-tilbud aktivert!</h3>
            <p className="text-jobblogg-text-muted">
              Du får 15% rabatt på første faktureringsperiode som takk for at du kom tilbake til JobbLogg.
            </p>
            <div className="text-xs text-jobblogg-text-muted">
              Rabatten vil vises på neste faktura
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <PrimaryButton onClick={handleGoToDashboard} className="flex-1">
            Gå til dashbordet
          </PrimaryButton>
          <SecondaryButton onClick={handleViewSubscription} className="flex-1">
            Se abonnementsdetaljer
          </SecondaryButton>
        </div>

        {/* Auto-redirect Notice */}
        <div className="text-center text-sm text-jobblogg-text-muted">
          <p>Du blir automatisk omdirigert til dashbordet om 10 sekunder</p>
        </div>

        {/* Tips for Getting Started Again */}
        <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-xl p-6 mt-8">
          <h3 className="font-medium text-jobblogg-text mb-3">💡 Tips for å komme i gang igjen</h3>
          <ul className="space-y-2 text-sm text-jobblogg-text-muted">
            <li>• Sjekk at alle prosjektene dine er som forventet</li>
            <li>• Inviter teammedlemmer som måtte ha mistet tilgang</li>
            <li>• Utforsk nye funksjoner som er lagt til siden sist</li>
            <li>• Oppdater prosjektinformasjon hvis nødvendig</li>
            <li>• Kontakt oss på <EMAIL> hvis du trenger hjelp</li>
          </ul>
        </div>
      </div>
    </PageLayout>
  );
};
