import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { PageLayout, Heading1, Heading2, Heading3, TextMedium, TextMuted, EmptyState, ImageModal } from '../../components/ui';

import { EditHistoryModal } from '../../components/EditHistoryModal';
import { CustomerInformationCard } from '../../components/CustomerInformationCard';
import { ContractorInformationCard } from '../../components/ContractorInformationCard';

import { CustomerLikeButton, useCustomerSession } from '../../components/CustomerLikeButton';
import { debugLog } from '../../utils/featureFlags';
// import { useNavigate } from 'react-router-dom'; // TODO: Use for navigation
import { EmbeddedChatContainer } from '../../components/chat';
import { getChatUserRole } from '../../utils/userRole';


const SharedProject: React.FC = () => {
  const { sharedId } = useParams<{ sharedId: string; logId?: string }>();
  const [showEditHistory, setShowEditHistory] = useState<string | null>(null);
  const [hasTrackedAccess, setHasTrackedAccess] = useState(false);
  const [hasInitialScrolled, setHasInitialScrolled] = useState(false);
  const logEntriesContainerRef = useRef<HTMLDivElement>(null);

  // Remove chat mode detection - no longer needed for embedded chat




  // Image modal state
  const [selectedImage, setSelectedImage] = useState<{
    url: string;
    alt: string;
    title?: string;
    description?: string;
    date?: string;
  } | null>(null);

  // Image modal handlers
  const openImageModal = (imageData: {
    url: string;
    alt: string;
    title?: string;
    description?: string;
    date?: string;
  }) => {
    setSelectedImage(imageData);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  // Helper function to get customer display name for likes (consistent with chat system)
  const getCustomerDisplayName = (customer: any): string => {
    if (!customer) return '';

    // For business customers, use contact person if available, otherwise company name
    if (customer.type === 'bedrift' && customer.contactPerson) {
      return customer.contactPerson;
    }

    // For private customers or business without contact person, use customer name
    return customer.name || '';
  };

  // Fetch shared project data
  const project = useQuery(
    api.projects.getBySharedId,
    sharedId ? { sharedId } : "skip"
  );

  // Customer session for likes (after project is defined)
  const customerSessionId = useCustomerSession(sharedId, project?._id);

  // Track access mutation
  const trackAccess = useMutation(api.projects.trackSharedProjectAccess);

  // Fetch log entries for shared project
  const logEntries = useQuery(
    api.logEntries.getBySharedProject,
    sharedId ? { sharedId } : "skip"
  );

  // Remove specific log entry query - no longer needed for embedded chat





  // Fetch like counts for all images in shared project
  const imageLikes = useQuery(
    api.imageLikes.getLikesForSharedProject,
    project && sharedId ? {
      projectId: project._id,
      sharedId
    } : "skip"
  );

  // Edit history handlers
  const handleShowEditHistory = (entryId: string) => {
    setShowEditHistory(entryId);
  };

  const handleCloseEditHistory = () => {
    setShowEditHistory(null);
  };



  // Track access when project is loaded (only once per session)
  useEffect(() => {
    if (project && sharedId && project.isPubliclyShared && !hasTrackedAccess) {
      // Track access asynchronously without blocking UI
      trackAccess({ sharedId })
        .then(() => {
          setHasTrackedAccess(true);
          console.log('Project access tracked successfully');
        })
        .catch(error => {
          console.warn('Failed to track project access:', error);
        });
    }
  }, [project?._id, sharedId, hasTrackedAccess]); // Track only once per session

  // Clear customer data when navigating away from shared project
  useEffect(() => {
    return () => {
      // Cleanup function runs when component unmounts
      // Customer data persistence removed with comment system
    };
  }, []);

  // Auto-scroll to most recent log entry on initial page load
  useEffect(() => {
    if (logEntries && logEntries.length > 0 && !hasInitialScrolled && logEntriesContainerRef.current) {
      // Small delay to ensure DOM is fully rendered
      const timer = setTimeout(() => {
        // Find the most recent log entry (last in the array)
        const mostRecentEntry = logEntries[logEntries.length - 1];
        if (mostRecentEntry) {
          // Find the DOM element for this entry
          const entryElement = document.querySelector(`[data-entry-id="${mostRecentEntry._id}"]`);
          if (entryElement) {
            entryElement.scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            });
          } else {
            // Fallback: scroll to bottom of container
            logEntriesContainerRef.current?.scrollIntoView({
              behavior: 'smooth',
              block: 'end',
              inline: 'nearest'
            });
          }
        }
        setHasInitialScrolled(true);
      }, 300); // Allow time for animations to start

      return () => clearTimeout(timer);
    }
  }, [logEntries, hasInitialScrolled]);

  // Loading state
  if (project === undefined || logEntries === undefined) {
    return (
      <PageLayout containerWidth="wide" minimalFooter>
        <div className="animate-pulse space-y-4 sm:space-y-6 px-4">
          <div className="h-6 sm:h-8 bg-jobblogg-neutral rounded-lg w-1/2 mx-auto"></div>
          <div className="h-3 sm:h-4 bg-jobblogg-neutral rounded w-3/4 mx-auto"></div>
          <div className="space-y-3 sm:space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 sm:h-32 bg-jobblogg-neutral rounded-xl"></div>
            ))}
          </div>
        </div>
      </PageLayout>
    );
  }

  // Debug logging for shared project
  if (project) {
    debugLog('SharedProject render', {
      projectId: project._id,
      hasJobData: !!project.jobData,
      jobDataKeys: project.jobData ? Object.keys(project.jobData) : []
    });
  }

  // Project not found or not shared
  if (!project) {
    return (
      <PageLayout containerWidth="wide" minimalFooter>
        <div className="text-center py-8 sm:py-12 px-4">
          <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-4 sm:mb-6 bg-jobblogg-error-soft rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 sm:w-8 sm:h-8 text-jobblogg-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <Heading2 className="mb-4">Prosjekt ikke funnet</Heading2>
          <TextMuted className="text-lg">
            Dette prosjektet eksisterer ikke eller er ikke lenger delt offentlig.
          </TextMuted>
        </div>
      </PageLayout>
    );
  }

  // Remove chat mode rendering - now using embedded chat

  return (
    <PageLayout containerWidth="wide" minimalFooter>
      <div className="w-full px-4 sm:px-0 sm:max-w-4xl sm:mx-auto space-y-6 sm:space-y-8">
        {/* Project Header */}
        <div className="text-center space-y-3 sm:space-y-4 animate-slide-up">
          <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 sm:w-8 sm:h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 8h5a2 2 0 002-2V9a2 2 0 00-2-2H9a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <Heading1 className="bg-gradient-to-r from-jobblogg-primary to-jobblogg-accent bg-clip-text text-transparent">
            {project.name}
          </Heading1>
          <TextMedium className="text-base sm:text-lg max-w-2xl mx-auto px-2 sm:px-0">
            {project.description}
          </TextMedium>
        </div>

        {/* Customer and Contractor Information Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Information Card */}
          {project.customer ? (
            <CustomerInformationCard
              customer={project.customer}
              readOnly={true}
              showMap={false}
              showNavigation={false}
              defaultCollapsed={true}
            />
          ) : (
            <div className="animate-slide-up">
              <div className="mb-4">
                <Heading3>Kundeinformasjon</Heading3>
              </div>
              <div className="bg-jobblogg-neutral rounded-lg border border-jobblogg-border p-4 sm:p-6">
                <div className="text-center py-4">
                  <div className="w-12 h-12 mx-auto mb-3 bg-jobblogg-warning-soft rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <TextMuted className="text-sm">
                    Kundeinformasjon ikke tilgjengelig
                  </TextMuted>
                </div>
              </div>
            </div>
          )}

          {/* Contractor Information Card */}
          <div className="animate-slide-up" style={{ animationDelay: '100ms' }}>
            <ContractorInformationCard
              contractorUserId={project.userId}
              projectId={project._id as any}
              defaultCollapsed={true}
            />
          </div>
        </div>



        {/* Project Log Entries */}
        <div className="space-y-4 sm:space-y-6">
          <div>
            <Heading2>Prosjektlogg</Heading2>
            <div className="flex items-center gap-1 sm:gap-2 text-jobblogg-text-muted mt-1">
              <svg className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span className="text-sm">{logEntries?.length || 0} oppføringer</span>
            </div>
          </div>

          {logEntries && logEntries.length > 0 ? (
            <div ref={logEntriesContainerRef} className="space-y-4 sm:space-y-6">
              {logEntries.map((entry, index) => (
                <div
                  key={entry._id}
                  data-entry-id={entry._id}
                  className="bg-jobblogg-neutral w-full rounded-xl p-4 sm:p-6 card-hover animate-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex flex-col gap-4">
                    <div className="flex justify-between items-start gap-4">
                      <div className="flex-1">
                        <div className="flex items-start gap-2 mb-2">
                          <TextMedium className="leading-relaxed flex-1">
                            {entry.entryType === 'system' && entry.description === 'Prosjekt startet'
                              ? 'Prosjekt registrert'
                              : entry.description}
                          </TextMedium>
                          {entry.isEdited && (
                            <span className="px-2 py-1 bg-jobblogg-primary-soft text-jobblogg-primary text-xs font-medium rounded-full flex-shrink-0">
                              Redigert
                            </span>
                          )}
                        </div>


                        {entry.isEdited && (
                          <button
                            onClick={() => handleShowEditHistory(entry._id)}
                            className="text-jobblogg-primary hover:text-jobblogg-primary-dark text-sm font-medium transition-colors duration-200 flex items-center gap-1 sm:gap-2 min-h-[44px] p-2 -m-2 rounded-lg hover:bg-jobblogg-primary-soft/20"
                          >
                            <svg className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span className="text-xs sm:text-sm">Se endringshistorikk</span>
                          </button>
                        )}
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2 min-w-0">
                        <svg className="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span className="text-xs sm:text-sm truncate">
                          {new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                    </div>
                    {entry.imageUrl && (
                      <div className="mt-4">
                        <div className="relative">
                          <button
                            onClick={() => openImageModal({
                              url: entry.imageUrl!,
                              alt: "Prosjektbilde",
                              title: entry.description || "Prosjektbilde",
                              description: entry.description,
                              date: new Date(entry.createdAt).toLocaleDateString('nb-NO', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })
                            })}
                            className="relative block w-full text-left focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-opacity-50 rounded-xl group"
                            aria-label="Klikk for å se bildet i full størrelse"
                          >
                            <img
                              src={entry.imageUrl}
                              alt="Prosjektbilde"
                              className="rounded-xl w-full h-auto max-h-96 object-cover shadow-md hover:shadow-lg transition-all duration-200 group-hover:scale-[1.02] cursor-pointer"
                            />
                            {/* Click indicator overlay */}
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-xl flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <div className="bg-white bg-opacity-90 rounded-full p-3 shadow-lg">
                                <svg className="w-6 h-6 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                </svg>
                              </div>
                            </div>
                          </button>

                        </div>

                        {/* Like Button - positioned below the image (Instagram style) */}
                        {customerSessionId && project && sharedId && (
                          <div className="mt-3">
                            <CustomerLikeButton
                              logEntryId={entry._id}
                              projectId={project._id}
                              sharedId={sharedId}
                              customerSessionId={customerSessionId}
                              isArchived={project.isArchived}
                              showCount={true}
                              projectCustomerName={getCustomerDisplayName(project.customer)}
                              likeDetails={imageLikes?.[entry._id]?.likes}
                            />
                          </div>
                        )}

                      </div>
                    )}

                    {/* Embedded Chat for Customer - Available for all log entries */}
                    {customerSessionId && project && sharedId && (
                      <>
                        {/* Complex EmbeddedChatContainer component interface - type assertion needed */}
                        <EmbeddedChatContainer
                        logId={entry._id}
                        userId={customerSessionId}
                        userRole={getChatUserRole(null, true)} // true = shared project (customer)
                        maxHeight="250px"
                        className="mt-3"
                        customerInfo={project.customer as any}
                      />
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <EmptyState
              icon={
                <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              }
              title="Ingen logger ennå"
              description="Dette prosjektet har ingen logger ennå. Kom tilbake senere for oppdateringer! 📝"
            />
          )}
        </div>

        {/* Contractor Notes Section - REMOVED: Project notes are now always contractor-only and never visible to customers */}



        {/* Edit History Modal */}
        {showEditHistory && (
          <EditHistoryModal
            entryId={showEditHistory}
            isOpen={true}
            onClose={handleCloseEditHistory}
            isSharedView={true}
            sharedId={sharedId}
          />
        )}
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          isOpen={!!selectedImage}
          onClose={closeImageModal}
          imageUrl={selectedImage.url}
          imageAlt={selectedImage.alt}
          imageTitle={selectedImage.title}
          imageDescription={selectedImage.description}
          imageDate={selectedImage.date}
        />
      )}
    </PageLayout>
  );
};

export default SharedProject;
