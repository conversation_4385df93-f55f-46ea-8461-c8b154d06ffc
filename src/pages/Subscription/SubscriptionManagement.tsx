import { useUser } from '@clerk/clerk-react';
import { useAction, useMutation, useQuery } from 'convex/react';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../../convex/_generated/api';
import { EnhancedBillingPortal, NotificationPreferences, PlanChangeConfirmationModal, PlanComparison, PlanUpgradeDowngradeButtons, PortalReturnHandler, SubscriptionCancellationModal, SubscriptionReactivationModal, usePortalReturn } from '../../components/subscription';
import { TrialPlanSelectionModal } from '../../components/subscription/TrialPlanSelectionModal';
import { BodyText, Heading2, PageLayout, SecondaryButton, TextMuted } from '../../components/ui';
import { CheckoutButton, CheckoutError, CheckoutLoading } from '../../components/ui/CheckoutButton';
import { createPlanCheckoutParams, useCheckout } from '../../hooks/useCheckout';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useSubscriptionReactivation } from '../../hooks/useSubscriptionReactivation';
import { useUserRole } from '../../hooks/useUserRole';
import { getSimpleStatusText, getSubscriptionStatusInfo } from '../../utils/subscriptionStatusFormatting';
import { formatTimeRemaining, getDaysLeft } from '../../utils/timeFormatting';

const SubscriptionManagementFixed: React.FC = () => {
  console.log('🔥 Frontend: SubscriptionManagement component loaded');
  const { user } = useUser();
  const navigate = useNavigate();
  const { isAdministrator, user: userWithRole, isLoading: roleLoading } = useUserRole();
  const { subscription, isInTrial, isLoading: subLoading } = useSubscriptionAccess();


  const createPortalSession = useAction(api.subscriptions.createPortalSession);
  const createTrialSubscription = useMutation(api.subscriptions.createTrialSubscription);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [showTrialPlanModal, setShowTrialPlanModal] = useState(false);

  // Handle proceeding to checkout from trial plan selection modal
  const handleProceedToCheckout = async (planLevel: string, billingInterval: 'month' | 'year') => {
    if (!user) {
      console.error('No user found');
      return;
    }

    console.log('🔥 Frontend: Proceeding to checkout with plan:', planLevel, billingInterval);
    setShowTrialPlanModal(false);
    setIsUpgrading(true);

    try {
      // Create checkout parameters for selected plan
      const checkoutParams = createPlanCheckoutParams(planLevel, billingInterval, {
        trialDays: 0, // No trial for upgrade
      });

      if (checkoutParams.priceId) {
        await checkout.initiateCheckout({
          priceId: checkoutParams.priceId,
          planLevel: checkoutParams.planLevel!,
          billingInterval: checkoutParams.billingInterval!,
          quantity: checkoutParams.quantity,
          successUrl: checkoutParams.successUrl,
          cancelUrl: checkoutParams.cancelUrl,
          allowPromotionCodes: checkoutParams.allowPromotionCodes,
          automaticTax: checkoutParams.automaticTax,
        });
      }
    } catch (error) {
      console.error('Checkout failed:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  // Plan change functionality
  const [showPlanComparison, setShowPlanComparison] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [planChangeDetails, setPlanChangeDetails] = useState<any>(null);
  const [isProcessingPlanChange, setIsProcessingPlanChange] = useState(false);

  const calculateProration = useQuery(api.planChanges?.calculatePlanChangeProration, "skip");
  const initiatePlanChange = useMutation(api.planChanges?.initiatePlanChange);
  const canChangeToPlan = useQuery(api.planChanges?.canChangeToPlan, "skip");

  // Cancellation functionality
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [isProcessingCancellation, setIsProcessingCancellation] = useState(false);

  const canCancelSubscription = useQuery(
    api.subscriptionCancellation?.canCancelSubscription,
    user ? { userId: user.id } : "skip"
  );
  const initiateCancellation = useMutation(api.subscriptionCancellation?.initiateSubscriptionCancellation);

  // Reactivation functionality
  const reactivation = useSubscriptionReactivation();
  const canReactivate = useQuery(
    api.subscriptionReactivation?.canReactivateSubscription,
    user ? { userId: user.id } : "skip"
  );
  const availablePlans = useQuery(
    api.subscriptionReactivation?.getReactivationPlans,
    user ? { userId: user.id } : "skip"
  );

  // Portal return handling
  const portalReturn = usePortalReturn();

  // Checkout hook for direct Stripe integration
  const checkout = useCheckout();



  // Show loading state while checking permissions
  if (roleLoading || subLoading) {
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                <TextMuted>Sjekker tilganger...</TextMuted>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Debug information (remove in production)
  console.log('Debug - Subscription Management State:', {
    user: user?.id,
    isAdministrator,
    subscription: subscription ? 'Found' : 'Not found',
    isInTrial,
    subscriptionDetails: subscription ? {
      id: (subscription as any)?._id,
      userId: (subscription as any)?.userId,
      stripeCustomerId: (subscription as any)?.stripeCustomerId,
      status: (subscription as any)?.status,
      planLevel: (subscription as any)?.planLevel
    } : null
  });

  // Check administrator access
  if (!isAdministrator) {
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="text-center py-16">
              <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <Heading2 className="mb-4">Kun for administratorer</Heading2>
              <BodyText className="mb-8 text-jobblogg-text-medium max-w-md mx-auto">
                Denne siden er kun tilgjengelig for brukere med administratorrettigheter. 
                Kontakt din administrator hvis du trenger tilgang.
              </BodyText>
              <BodyText className="text-sm text-jobblogg-text-muted">
                Du er logget inn som {(userWithRole as any)?.role === 'prosjektleder' ? 'prosjektleder' : 'utførende bruker'}
              </BodyText>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Define handleUpgrade function before it's used
  const handleUpgrade = async () => {
    if (!user) {
      console.error('No user found');
      return;
    }

    console.log('🔥 Frontend: Starting upgrade process for user:', user.id);
    setIsUpgrading(true);

    try {
      // For trial users, open plan selection modal for better UX
      if (isInTrial && subscription) {
        console.log('🔥 Frontend: Trial user - opening plan selection modal');
        setShowTrialPlanModal(true);
        setIsUpgrading(false);
        return;
      }

      // For existing subscribers, use Customer Portal
      console.log('🔥 Frontend: Existing subscriber - using Customer Portal');
      const result = await createPortalSession({
        userId: user.id,
        returnUrl: window.location.href
      });

      console.log('Portal session result:', result);

      if (result?.url) {
        console.log('Redirecting to:', result.url);
        window.location.href = result.url;
      } else {
        console.error('No URL returned from createPortalSession');
        setIsUpgrading(false);
      }
    } catch (error: unknown) {
      console.error('Failed to create upgrade session:', error);

      // If no subscription found, try to create a trial subscription first
      if ((error as any).message?.includes('No subscription found')) {
        console.log('No subscription found, creating trial subscription...');
        try {
          const trialResult = await createTrialSubscription({
            userId: user.id,
            name: user.fullName || `${user.firstName} ${user.lastName}` || 'Bruker',
            email: user.primaryEmailAddress?.emailAddress || '',
            selectedPlan: 'basic'
          });

          console.log('Trial subscription created:', trialResult);

          // Refresh the page to update subscription status
          window.location.reload();
        } catch (trialError) {
          console.error('Failed to create trial subscription:', trialError);
          setIsUpgrading(false);
          alert(`Test failed: ${(trialError as any).message}`);
        }
      } else {
        setIsUpgrading(false);
        alert('Det oppstod en feil. Prøv igjen senere.');
      }
    }
  };

  // Plan change handlers
  const handlePlanSelect = async (planId: string, billingInterval: 'month' | 'year') => {
    if (!user || !api.planChanges?.calculatePlanChangeProration) return;

    try {
      const prorationDetails = await calculateProration({
        userId: user.id,
        newPlanId: planId,
        newBillingInterval: billingInterval,
      });

      setPlanChangeDetails(prorationDetails);
      setShowConfirmationModal(true);
      setShowPlanComparison(false);
    } catch (error) {
      console.error('Failed to calculate proration:', error);
      alert('Kunne ikke beregne prisendring. Prøv igjen senere.');
    }
  };

  const handleConfirmPlanChange = async () => {
    if (!user || !planChangeDetails || !api.planChanges?.initiatePlanChange) return;

    setIsProcessingPlanChange(true);

    try {
      const result = await initiatePlanChange({
        userId: user.id,
        newPlanId: planChangeDetails.newPlan.id,
        newBillingInterval: planChangeDetails.newPlan.billingInterval,
      });

      if (result.status === 'initiated') {
        setShowConfirmationModal(false);
        setPlanChangeDetails(null);

        // Show success message and refresh after a delay
        alert('Planendring er startet! Siden vil oppdateres om et øyeblikk.');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to initiate plan change:', error);
      alert('Planendring feilet. Prøv igjen senere.');
    } finally {
      setIsProcessingPlanChange(false);
    }
  };

  const handleQuickUpgrade = () => {
    setShowPlanComparison(true);
  };

  const handleQuickDowngrade = () => {
    setShowPlanComparison(true);
  };

  // Cancellation handlers
  const handleCancelSubscription = async (feedback: any, cancelImmediately: boolean) => {
    if (!user || !api.subscriptionCancellation?.initiateSubscriptionCancellation) return;

    setIsProcessingCancellation(true);

    try {
      const result = await initiateCancellation({
        userId: user.id,
        feedback,
        cancelImmediately,
      });

      if (result.status === 'initiated') {
        setShowCancellationModal(false);

        // Show success message and refresh after a delay
        alert(result.message + ' Siden vil oppdateres om et øyeblikk.');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
      alert('Avbryting feilet. Prøv igjen senere.');
    } finally {
      setIsProcessingCancellation(false);
    }
  };

  // No subscription state
  if (!subscription) {
    // Check if user can reactivate a cancelled subscription
    if (canReactivate?.canReactivate && canReactivate.subscription) {
      const cancelledSub = canReactivate.subscription;
      const isDataExpired = cancelledSub.isDataExpired;
      const daysUntilExpiry = Math.max(0, Math.ceil(
        (new Date(cancelledSub.dataRetentionUntil).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
      ));

      return (
        <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
          <div className="bg-white rounded-xl shadow-soft p-8">
            <div className="max-w-2xl mx-auto">
              <div className="text-center py-16 space-y-6">
                <div className="w-16 h-16 bg-jobblogg-success-soft rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>

                <div>
                  <Heading2 className="mb-4">Velkommen tilbake!</Heading2>
                  <BodyText className="mb-6 text-jobblogg-text-medium max-w-md mx-auto">
                    Vi ser at du tidligere hadde et {cancelledSub.planLevel}-abonnement.
                    Reaktiver abonnementet ditt og få 15% rabatt på første måned!
                  </BodyText>
                </div>

                {/* Data retention status */}
                {isDataExpired ? (
                  <div className="bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-4 mb-6">
                    <p className="text-sm text-jobblogg-text">
                      ⚠️ Dataene dine er slettet, men du kan fortsatt reaktivere abonnementet og starte på nytt.
                    </p>
                  </div>
                ) : daysUntilExpiry <= 7 ? (
                  <div className="bg-jobblogg-error-light border border-jobblogg-error rounded-lg p-4 mb-6">
                    <p className="text-sm text-jobblogg-text">
                      🚨 Dataene dine slettes om {daysUntilExpiry} dager! Reaktiver nå for å beholde alle prosjektene dine.
                    </p>
                  </div>
                ) : (
                  <div className="bg-jobblogg-success-light border border-jobblogg-success rounded-lg p-4 mb-6">
                    <p className="text-sm text-jobblogg-text">
                      ✅ Alle prosjektene dine er trygge og vil være tilgjengelige umiddelbart etter reaktivering.
                    </p>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <PrimaryButton
                    onClick={reactivation.actions.openReactivationFlow}
                    className="px-8"
                    disabled={reactivation.state.isProcessing}
                  >
                    {reactivation.state.isProcessing ? 'Behandler...' : 'Reaktiver abonnement'}
                  </PrimaryButton>
                  <SecondaryButton onClick={handleUpgrade} className="px-8">
                    Start nytt abonnement
                  </SecondaryButton>
                </div>

                <div className="text-xs text-jobblogg-text-muted">
                  Avbrutt: {new Date(cancelledSub.canceledAt || cancelledSub.updatedAt).toLocaleDateString('nb-NO')}
                </div>
              </div>
            </div>
          </div>

          {/* Reactivation Modal */}
          <SubscriptionReactivationModal
            isOpen={reactivation.state.showModal}
            onClose={reactivation.actions.closeReactivationFlow}
            onReactivate={reactivation.actions.submitReactivation}
            cancelledSubscription={{
              planLevel: cancelledSub.planLevel,
              cancelledAt: cancelledSub.canceledAt || cancelledSub.updatedAt,
              dataRetentionUntil: cancelledSub.dataRetentionUntil,
            }}
            availablePlans={availablePlans || []}
            isProcessing={reactivation.state.isProcessing}
            hasValidPaymentMethod={false} // Will be handled in checkout flow
          />
        </PageLayout>
      );
    }

    // No subscription and no reactivation available
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="text-center py-16">
              <div className="w-16 h-16 bg-jobblogg-primary-soft rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <Heading2 className="mb-4">Ingen aktiv plan</Heading2>
              <BodyText className="mb-8 text-jobblogg-text-medium max-w-md mx-auto">
                Du har ikke et aktivt abonnement. Start en gratis prøveperiode for å få tilgang til alle funksjoner.
              </BodyText>
              <PrimaryButton onClick={handleUpgrade} className="px-8">
                Start gratis prøveperiode
              </PrimaryButton>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }







  // Calculate trial progress
  const trialStart = (subscription as any)?.trialStart || 0;
  const trialEnd = (subscription as any)?.trialEnd || 0;
  const now = Date.now();
  const totalTrialDuration = trialEnd - trialStart;
  const elapsed = now - trialStart;
  const trialProgress = Math.min(100, Math.max(0, (elapsed / totalTrialDuration) * 100));
  const timeRemaining = formatTimeRemaining(trialEnd);
  const daysLeft = getDaysLeft(trialEnd); // For backward compatibility
  const isExpiringSoon = timeRemaining.isUrgent || daysLeft <= 2;

  // Get dynamic subscription status information
  const subscriptionStatusInfo = getSubscriptionStatusInfo(subscription);

  // Plan information with pricing and user limits
  const planNames = {
    basic: 'Liten bedrift',
    professional: 'Mellomstor bedrift',
    enterprise: 'Stor bedrift'
  };

  const planPrices = {
    basic: { monthly: 299, yearly: 2870 },
    professional: { monthly: 999, yearly: 9590 },
    enterprise: { monthly: 2999, yearly: 28790 }
  };

  const planUserLimits = {
    basic: 9,
    professional: 49,
    enterprise: 249
  };

  const billingLabels = {
    month: 'månedlig',
    year: 'årlig'
  };

  const currentPlan = {
    name: planNames[(subscription as any)?.planLevel as keyof typeof planNames] || (subscription as any)?.planLevel,
    level: (subscription as any)?.planLevel,
    billing: (subscription as any)?.billingInterval,
    price: planPrices[(subscription as any)?.planLevel as keyof typeof planPrices]?.[(subscription as any)?.billingInterval === 'year' ? 'yearly' : 'monthly'] || 0,
    billingLabel: billingLabels[(subscription as any)?.billingInterval as keyof typeof billingLabels] || 'månedlig',
    userLimit: planUserLimits[(subscription as any)?.planLevel as keyof typeof planUserLimits] || 9
  };

  return (
    <>
      {/* Checkout Loading Overlay */}
      <CheckoutLoading
        isVisible={checkout.state.isLoading || checkout.state.isRetrying}
        message="Starter oppgradering..."
        isRetrying={checkout.state.isRetrying}
      />

      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
          <div className="space-y-16">
            {/* Hero Section */}
            <section className="text-center space-y-8">
              {isInTrial ? (
                <>
                  <div className="space-y-4">
                    <Heading2>
                      {timeRemaining.text === 'Prøveperioden er utløpt'
                        ? 'Prøveperioden er utløpt'
                        : `${timeRemaining.text} av prøveperioden`
                      }
                    </Heading2>
                    <BodyText className="text-jobblogg-text-medium max-w-lg mx-auto">
                      {subscriptionStatusInfo.statusText}
                    </BodyText>

                    {/* Dynamic renewal/expiration information */}
                    <div className="space-y-2">
                      <BodyText className={`text-sm max-w-lg mx-auto ${subscriptionStatusInfo.isUrgent ? 'text-jobblogg-warning' : 'text-jobblogg-text-medium'}`}>
                        {subscriptionStatusInfo.renewalText}
                      </BodyText>

                      {subscriptionStatusInfo.expirationDate && (
                        <BodyText className="text-xs text-jobblogg-text-muted max-w-lg mx-auto">
                          {subscriptionStatusInfo.expirationDate}
                        </BodyText>
                      )}
                    </div>
                  </div>

                  {/* Enhanced Trial Status */}
                  <div className="max-w-md mx-auto space-y-6">
                    <div className={`p-6 rounded-xl border ${
                      isExpiringSoon
                        ? 'bg-jobblogg-warning-soft border-jobblogg-warning'
                        : 'bg-jobblogg-primary-soft border-jobblogg-primary'
                    }`}>
                      <div className="flex items-center justify-center gap-3 mb-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
                        }`}>
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <div className="text-left">
                          <div className={`text-lg font-semibold ${
                            isExpiringSoon ? 'text-jobblogg-warning' : 'text-jobblogg-primary'
                          }`}>
                            {timeRemaining.text === 'Prøveperioden er utløpt'
                              ? 'Prøveperioden er utløpt'
                              : `${timeRemaining.text} igjen`
                            }
                          </div>
                          <div className="text-sm text-jobblogg-text-medium">
                            {subscriptionStatusInfo.expirationDate || 'Etter dette trenger du et abonnement for å fortsette med full tilgang.'}
                          </div>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-white/50 rounded-full h-3 mb-4">
                        <div
                          className={`h-3 rounded-full transition-all duration-300 ${
                            isExpiringSoon ? 'bg-jobblogg-warning' : 'bg-jobblogg-primary'
                          }`}
                          style={{ width: `${trialProgress}%` }}
                        />
                      </div>


                    </div>
                  </div>
                </>
              ) : (
                <div className="space-y-4">
                  <Heading2>{getSimpleStatusText(subscription)}</Heading2>
                  <BodyText className="text-jobblogg-text-medium max-w-lg mx-auto">
                    {subscriptionStatusInfo.statusText}
                  </BodyText>

                  {/* Dynamic renewal/expiration information for active subscriptions */}
                  <div className="space-y-2">
                    <BodyText className={`text-sm max-w-lg mx-auto ${subscriptionStatusInfo.isUrgent ? 'text-jobblogg-warning' : 'text-jobblogg-text-medium'}`}>
                      {subscriptionStatusInfo.renewalText}
                    </BodyText>

                    {subscriptionStatusInfo.expirationDate && (
                      <BodyText className="text-xs text-jobblogg-text-muted max-w-lg mx-auto">
                        {subscriptionStatusInfo.expirationDate}
                      </BodyText>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="space-y-6">
                {isInTrial ? (
                  <>
                    {/* Primary CTA for Trial Users */}
                    <div className="space-y-4">
                      <CheckoutButton
                        onClick={handleUpgrade}
                        isLoading={checkout.state.isLoading}
                        isRetrying={checkout.state.isRetrying}
                        disabled={isUpgrading}
                        loadingText="Starter oppgradering..."
                        className="min-w-[240px] px-8 py-4 text-lg font-semibold"
                      >
                        Velg abonnement
                      </CheckoutButton>

                      <div className="text-sm text-jobblogg-text-medium">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>Velg månedlig eller årlig betaling</span>
                        </div>
                        <div className="flex items-center justify-center gap-2">
                          <svg className="w-4 h-4 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>Endre eller avslutt ved slutten av perioden</span>
                        </div>
                      </div>
                    </div>

                    {/* Trial Benefits */}
                    <div className="bg-jobblogg-surface rounded-lg p-6 max-w-md mx-auto">
                      <h3 className="font-semibold text-jobblogg-text-strong mb-4 text-center">
                        Hva får du tilgang til?
                      </h3>
                      <div className="space-y-3 text-sm">
                        <div className="flex items-center gap-3">
                          <svg className="w-5 h-5 text-jobblogg-success flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>Ubegrenset prosjektdokumentasjon</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <svg className="w-5 h-5 text-jobblogg-success flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>Teamsamarbeid og invitasjoner</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <svg className="w-5 h-5 text-jobblogg-success flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          <span>Kundesamtaler og deling</span>
                        </div>

                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex justify-center">
                    <CheckoutButton
                      onClick={handleUpgrade}
                      isLoading={checkout.state.isLoading}
                      isRetrying={checkout.state.isRetrying}
                      disabled={isUpgrading}
                      loadingText="Starter oppgradering..."
                      className="min-w-[200px] px-8"
                    >
                      Administrer abonnement
                    </CheckoutButton>
                  </div>
                )}

                {/* Checkout Error Display */}
                <CheckoutError
                  error={checkout.state.error}
                  norwegianError={checkout.state.norwegianError}
                  canRetry={checkout.state.canRetry}
                  onRetry={checkout.retryCheckout}
                  onClear={checkout.clearError}
                  retryCount={checkout.state.retryCount}
                  maxRetries={3}
                />
              </div>
            </section>



            {/* Plan Management Section - Only for paying subscribers */}
            {!isInTrial && (
              <section className="space-y-6">
                <div className="bg-white rounded-xl shadow-soft p-6 border border-jobblogg-border">
                  <Heading2 className="mb-6">Planendringer</Heading2>
                  <PlanUpgradeDowngradeButtons
                    currentPlan={subscription.planLevel || 'basic'}
                    currentBillingInterval={subscription.billingInterval || 'month'}
                    onUpgrade={handleQuickUpgrade}
                    onDowngrade={handleQuickDowngrade}
                    onViewPlans={() => setShowPlanComparison(true)}
                    isLoading={isProcessingPlanChange}
                  />
                </div>
              </section>
            )}

            {/* Subscription Management Section - Only for paying subscribers */}
            {!isInTrial && (
              <section className="space-y-6">
                <div className="bg-white rounded-xl shadow-soft p-6 border border-jobblogg-border">
                  <Heading2 className="mb-6">Abonnementshåndtering</Heading2>

                <div className="space-y-4">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                      <h3 className="font-medium text-jobblogg-text">Avbryt abonnement</h3>
                      <p className="text-sm text-jobblogg-text-muted">
                        Avbryt abonnementet ditt når som helst. Du kan velge å avbryte umiddelbart eller ved slutten av faktureringsperioden.
                      </p>
                    </div>
                    <SecondaryButton
                      onClick={() => setShowCancellationModal(true)}
                      disabled={!canCancelSubscription?.canCancel || isProcessingCancellation}
                      className="whitespace-nowrap"
                    >
                      {isProcessingCancellation ? 'Behandler...' : 'Avbryt abonnement'}
                    </SecondaryButton>
                  </div>

                  {!canCancelSubscription?.canCancel && canCancelSubscription?.reason && (
                    <div className="bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-3">
                      <p className="text-sm text-jobblogg-text">
                        {canCancelSubscription.reason}
                      </p>
                    </div>
                  )}

                  {subscription.cancelAtPeriodEnd && (
                    <div className="bg-jobblogg-info-light border border-jobblogg-info rounded-lg p-4">
                      <h4 className="font-medium text-jobblogg-text mb-2">Abonnement planlagt for avbryting</h4>
                      <p className="text-sm text-jobblogg-text-muted">
                        Ditt abonnement vil bli avbrutt {subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd).toLocaleDateString('nb-NO') : 'ved slutten av faktureringsperioden'}.
                        Du beholder full tilgang til den datoen.
                      </p>
                    </div>
                  )}
                </div>
              </div>
              </section>
            )}

            {/* Enhanced Billing Portal Section - Only for paying subscribers */}
            {!isInTrial && (
              <section className="space-y-6">
                <div className="bg-white rounded-xl shadow-soft p-6 border border-jobblogg-border">
                  <EnhancedBillingPortal
                    returnUrl={`${window.location.origin}/subscription`}
                    onPortalOpen={() => console.log('Portal opened')}
                    onPortalReturn={(flow) => {
                      console.log('Portal returned with flow:', flow);
                      // Refresh subscription data
                      window.location.reload();
                    }}
                  />
                </div>
              </section>
            )}

            {/* Trial Information Section - Only for trial users */}
            {isInTrial && (
              <section className="space-y-6">
                <div className="bg-white rounded-xl shadow-soft p-6 border border-jobblogg-border">
                  <Heading2 className="mb-6">Ofte stilte spørsmål</Heading2>

                  <div className="space-y-6">
                    <div>
                      <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                        Hva skjer når prøveperioden utløper?
                      </h3>
                      <p className="text-jobblogg-text-medium">
                        Du mister tilgang til JobbLogg til du velger en betalingsplan. Alle data lagres trygt og blir tilgjengelige igjen når du oppgraderer.
                      </p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                        Kan jeg si opp når som helst?
                      </h3>
                      <p className="text-jobblogg-text-medium">
                        Ja, du kan si opp abonnementet ditt. Oppsigelsen gjelder fra slutten av inneværende betalingsperiode – enten månedlig eller årlig, avhengig av hvilken plan ({currentPlan.name}) du har valgt.
                      </p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                        Hvilken plan passer best for meg?
                      </h3>
                      <p className="text-jobblogg-text-medium">
                        Du har valgt {currentPlan.name} med {currentPlan.billingLabel} fakturering. Planen inkluderer {currentPlan.userLimit} brukere. Etter prøveperioden vil prisen være {currentPlan.price.toLocaleString('nb-NO')} NOK ekskl. MVA. Du kan oppgradere eller nedgradere til en annen plan når som helst dersom du trenger flere eller færre brukere.
                      </p>
                    </div>

                    <div>
                      <h3 className="font-semibold text-jobblogg-text-strong mb-2">
                        Er mine data trygge?
                      </h3>
                      <p className="text-jobblogg-text-medium">
                        Ja. Alle data lagres sikkert og er kryptert. Vi følger GDPR og norske personvernregler.
                      </p>
                    </div>
                  </div>
                </div>
              </section>
            )}

            {/* Notification Preferences Section - Only for paying subscribers */}
            {!isInTrial && (
              <section className="space-y-6">
                <NotificationPreferences />
              </section>
            )}
          </div>
        </div>
      </div>

      {/* Plan Comparison Modal */}
      {showPlanComparison && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <Heading2>Velg ny plan</Heading2>
                <button
                  onClick={() => setShowPlanComparison(false)}
                  className="text-jobblogg-text-muted hover:text-jobblogg-text"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <PlanComparison
                currentPlan={subscription.planLevel || 'basic'}
                currentBillingInterval={subscription.billingInterval || 'month'}
                onPlanSelect={handlePlanSelect}
                isLoading={isProcessingPlanChange}
              />
            </div>
          </div>
        </div>
      )}

      {/* Trial Plan Selection Modal */}
      <TrialPlanSelectionModal
        isOpen={showTrialPlanModal}
        onClose={() => setShowTrialPlanModal(false)}
        onProceedToCheckout={handleProceedToCheckout}
        currentPlan={(subscription as any)?.planLevel || 'professional'}
        currentBilling={(subscription as any)?.billingInterval || 'month'}
        isLoading={isUpgrading}
      />

      {/* Plan Change Confirmation Modal */}
      <PlanChangeConfirmationModal
        isOpen={showConfirmationModal}
        onClose={() => {
          setShowConfirmationModal(false);
          setPlanChangeDetails(null);
        }}
        onConfirm={handleConfirmPlanChange}
        planChangeDetails={planChangeDetails}
        isProcessing={isProcessingPlanChange}
      />

      {/* Subscription Cancellation Modal */}
      <SubscriptionCancellationModal
        isOpen={showCancellationModal}
        onClose={() => setShowCancellationModal(false)}
        onCancel={handleCancelSubscription}
        subscriptionDetails={subscription ? {
          planName: subscription.planLevel || 'Basic',
          nextBillingDate: new Date(subscription.currentPeriodEnd || Date.now()).toISOString(),
          amount: subscription.amount || 299,
          billingInterval: subscription.billingInterval || 'month',
        } : null}
        isProcessing={isProcessingCancellation}
      />

      {/* Portal Return Handler */}
      {portalReturn.hasReturnData && (
        <PortalReturnHandler
          onReturnProcessed={(success, flow) => {
            console.log('Portal return processed:', success, flow);
            if (success) {
              // Refresh the page to show updated subscription data
              setTimeout(() => window.location.reload(), 1000);
            }
          }}
          autoRedirect={false} // We handle redirect manually
        />
      )}
    </PageLayout>
    </>
  );
};

export default SubscriptionManagementFixed;
