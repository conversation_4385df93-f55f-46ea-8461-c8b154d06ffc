import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import {
  DashboardLayout,
  StatsCard,
  PrimaryButton,
  SecondaryButton,
  Heading2,
  BodyText,
  EmptyState
} from '../../components/ui';
import { useUserRole, useTeamMembers, usePendingInvitations, useTeamWorkload } from '../../hooks/useUserRole';
import { TeamMemberCard } from '../../components/team/TeamMemberCard';
import { InvitationCard } from '../../components/team/InvitationCard';
import { InviteTeamMemberModal } from '../../components/team/InviteTeamMemberModal';

import { useAutoTrackActivity } from '../../hooks/useUserActivity';
import { SeatUsageIndicator, SubscriptionGate } from '../../components/subscription';

const TeamManagement: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  
  // State for modals
  const [showInviteModal, setShowInviteModal] = useState(false);
  
  // Hooks for team data
  const { isAdministrator, isProsjektleder, isLoading: roleLoading } = useUserRole();
  const { teamMembers, isLoading: membersLoading } = useTeamMembers();
  const { invitations, isLoading: invitationsLoading } = usePendingInvitations();
  const { isLoading: workloadLoading } = useTeamWorkload(); // workload unused due to disabled functionality

  // Track user activity on team management visit
  useAutoTrackActivity('team_management_visit');

  // Mutations
  const revokeInvitation = useMutation(api.teamManagement.revokeInvitation);
  const resendInvitation = useMutation(api.teamManagement.resendInvitation);

  // Loading state
  const isLoading = roleLoading || membersLoading || invitationsLoading || workloadLoading;

  // Redirect if not administrator or prosjektleder
  if (!roleLoading && !isAdministrator && !isProsjektleder) {
    return (
      <DashboardLayout title="Ingen tilgang">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <Heading2 className="mb-4">Ingen tilgang</Heading2>
          <BodyText className="mb-6">
            Kun administratorer og prosjektledere kan administrere teammedlemmer.
          </BodyText>
          <SecondaryButton onClick={() => navigate('/')}>
            Tilbake til dashboard
          </SecondaryButton>
        </div>
      </DashboardLayout>
    );
  }

  // Handle invitation actions
  const handleRevokeInvitation = async (invitationId: string) => {
    if (!user?.id) return;
    
    try {
      await revokeInvitation({
        invitationId: invitationId as any,
        revokedBy: user.id,
      });
    } catch (error) {
      console.error('Failed to revoke invitation:', error);
    }
  };

  const handleResendInvitation = async (invitationId: string) => {
    if (!user?.id) return;
    
    try {
      await resendInvitation({
        invitationId: invitationId as any,
        resentBy: user.id,
      });
    } catch (error) {
      console.error('Failed to resend invitation:', error);
    }
  };

  return (
    <>
      <DashboardLayout
        title="Teamadministrasjon"
        subtitle="Administrer teammedlemmer og invitasjoner"
        headerActions={
          <div className="flex items-center gap-3">
            <SecondaryButton
              onClick={() => navigate('/')}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Tilbake til dashboard
            </SecondaryButton>

            <SecondaryButton
              onClick={() => navigate('/team/projects')}
              className="flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              Prosjektoversikt
            </SecondaryButton>

            <SubscriptionGate feature="team_management" variant="button">
              <PrimaryButton
                onClick={() => setShowInviteModal(true)}
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                }
              >
                Inviter teammedlem
              </PrimaryButton>
            </SubscriptionGate>
          </div>
        }
        statsSection={
          // TODO: Re-enable when workload data is available
          // !isLoading && workload && (
          false && (
            <div className="grid-stats">
              <StatsCard
                title="Teammedlemmer"
                value={0} // workload.teamSize - disabled due to type issues
                variant="primary"
                animationDelay="0s"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 015.196-2.121m0 0a5.002 5.002 0 019.608 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                }
              />
              <StatsCard
                title="Aktive prosjekter"
                value={0} // workload.summary.totalActiveProjects - disabled due to type issues
                variant="accent"
                animationDelay="0.1s"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                }
              />
              <StatsCard
                title="Snitt per medlem"
                value={0} // workload.summary.averageProjectsPerMember - disabled due to type issues
                variant="primary"
                animationDelay="0.2s"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                }
              />
              <StatsCard
                title="Ventende invitasjoner"
                value={invitations.length}
                variant="warning"
                animationDelay="0.3s"
                icon={
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                }
              />
            </div>
          )
        }
      >
        {isLoading ? (
          <div className="space-y-6">
            <div className="animate-pulse">
              <div className="h-8 bg-jobblogg-neutral rounded w-1/4 mb-4"></div>
              <div className="grid gap-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="h-24 bg-jobblogg-neutral rounded"></div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            {/* Seat Usage Indicator */}
            <SeatUsageIndicator />

            {/* Team Members Section */}
            <section>
              <div className="flex items-center justify-between mb-6">
                <Heading2>Teammedlemmer ({teamMembers.length})</Heading2>
              </div>
              
              {teamMembers.length === 0 ? (
                <SubscriptionGate feature="team_management" variant="disable">
                  <EmptyState
                    title="👥 Ingen teammedlemmer ennå"
                    description="Inviter kolleger til å bli med i teamet ditt. De kan hjelpe med å administrere prosjekter og dokumentere arbeid."
                    actionLabel="Inviter første teammedlem"
                    onAction={() => setShowInviteModal(true)}
                  />
                </SubscriptionGate>
              ) : (
                <div className="grid gap-4">
                  {teamMembers.map((member: any) => (
                    <TeamMemberCard
                      key={member._id}
                      member={member}
                      currentUserId={user?.id}
                      onMemberUpdate={() => {
                        // Data will refresh automatically via Convex reactivity
                      }}
                    />
                  ))}
                </div>
              )}
            </section>

            {/* Pending Invitations Section */}
            {invitations.length > 0 && (
              <section>
                <div className="flex items-center justify-between mb-6">
                  <Heading2>Ventende invitasjoner ({invitations.length})</Heading2>
                </div>
                
                <div className="grid gap-4">
                  {invitations.map((invitation: any) => (
                    <InvitationCard
                      key={invitation._id}
                      invitation={invitation as any}
                      onRevoke={() => handleRevokeInvitation(invitation._id)}
                      onResend={() => handleResendInvitation(invitation._id)}
                    />
                  ))}
                </div>
              </section>
            )}
          </div>
        )}
      </DashboardLayout>

      {/* Invite Team Member Modal */}
      <InviteTeamMemberModal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        onSuccess={() => {
          setShowInviteModal(false);
          // Data will refresh automatically via Convex reactivity
        }}
      />
    </>
  );
};

export default TeamManagement;
