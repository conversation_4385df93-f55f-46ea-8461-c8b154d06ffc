import React, { useState } from 'react';
import { EnhancedTrialBanner } from '../components/subscription/EnhancedTrialBanner';
import { Heading2, BodyText, Card, PrimaryButton, SecondaryButton } from '../components/ui';

/**
 * Demo page to showcase the new enhanced trial banner variants
 */
export const TrialBannerDemo: React.FC = () => {
  const [selectedVariant, setSelectedVariant] = useState<'compact' | 'card' | 'smart'>('smart');

  return (
    <div className="min-h-screen bg-jobblogg-surface p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <Heading2 className="mb-4">🎨 Enhanced Trial Banner Demo</Heading2>
          <BodyText className="text-jobblogg-text-medium">
            Sammenligning av de tre nye prøveperiode banner-variantene med live countdown.
          </BodyText>
        </div>

        {/* Variant Selector */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Velg variant å forhåndsvise:</h3>
          <div className="flex gap-3 mb-6">
            {[
              { key: 'smart', label: 'Smart (Adaptiv)', desc: 'Endrer seg basert på gjenværende tid' },
              { key: 'compact', label: 'Kompakt', desc: 'Minimalistisk med progress bar' },
              { key: 'card', label: 'Kort', desc: 'Detaljert med fordeler og handlinger' }
            ].map(({ key, label, desc }) => (
              <button
                key={key}
                onClick={() => setSelectedVariant(key as any)}
                className={`flex-1 p-4 rounded-lg border-2 text-left transition-all ${
                  selectedVariant === key
                    ? 'border-jobblogg-primary bg-jobblogg-primary-soft'
                    : 'border-jobblogg-border hover:border-jobblogg-primary/50'
                }`}
              >
                <div className="font-semibold text-jobblogg-text-strong">{label}</div>
                <div className="text-sm text-jobblogg-text-medium mt-1">{desc}</div>
              </button>
            ))}
          </div>
          
          {/* Live Preview */}
          <div className="bg-jobblogg-surface rounded-lg p-6">
            <h4 className="font-medium mb-4">Live Forhåndsvisning:</h4>
            <EnhancedTrialBanner 
              variant={selectedVariant}
              showDismiss={true}
              onDismiss={() => alert('Banner lukket!')}
            />
          </div>
        </Card>

        {/* Feature Comparison */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">📊 Funksjonssammenligning</h3>
          
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-jobblogg-border">
                  <th className="text-left py-2 font-medium text-jobblogg-text-strong">Funksjon</th>
                  <th className="text-center py-2 font-medium text-jobblogg-text-strong">Smart</th>
                  <th className="text-center py-2 font-medium text-jobblogg-text-strong">Kompakt</th>
                  <th className="text-center py-2 font-medium text-jobblogg-text-strong">Kort</th>
                </tr>
              </thead>
              <tbody className="text-jobblogg-text-medium">
                <tr className="border-b border-jobblogg-border/50">
                  <td className="py-3">Live countdown (timer/minutter)</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">✅</td>
                </tr>
                <tr className="border-b border-jobblogg-border/50">
                  <td className="py-3">Progress bar</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">✅</td>
                </tr>
                <tr className="border-b border-jobblogg-border/50">
                  <td className="py-3">Adaptiv design (farger basert på tid)</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">❌</td>
                  <td className="text-center">❌</td>
                </tr>
                <tr className="border-b border-jobblogg-border/50">
                  <td className="py-3">Fordeler/funsjoner liste</td>
                  <td className="text-center">❌</td>
                  <td className="text-center">❌</td>
                  <td className="text-center">✅</td>
                </tr>
                <tr className="border-b border-jobblogg-border/50">
                  <td className="py-3">Handlingsknapper</td>
                  <td className="text-center">⚪</td>
                  <td className="text-center">⚪</td>
                  <td className="text-center">✅</td>
                </tr>
                <tr className="border-b border-jobblogg-border/50">
                  <td className="py-3">Plassbesparende</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">❌</td>
                </tr>
                <tr>
                  <td className="py-3">Mobile-optimalisert</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">✅</td>
                  <td className="text-center">✅</td>
                </tr>
              </tbody>
            </table>
          </div>
        </Card>

        {/* All Variants Side by Side */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">🔄 Alle varianter side-ved-side</h3>
          
          <div className="space-y-6">
            <div>
              <h4 className="font-medium mb-3 text-jobblogg-text-strong">Smart Variant (Anbefalt)</h4>
              <EnhancedTrialBanner variant="smart" />
              <BodyText className="text-xs text-jobblogg-text-muted mt-2">
                → Endrer farge og melding automatisk basert på gjenværende tid. Rød når kritisk (&lt;1t), gul når hastegrad (&lt;1d), blå ellers.
              </BodyText>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 text-jobblogg-text-strong">Kompakt Variant</h4>
              <EnhancedTrialBanner variant="compact" />
              <BodyText className="text-xs text-jobblogg-text-muted mt-2">
                → Minimal og diskret. Perfekt for topp av sider uten å ta for mye plass.
              </BodyText>
            </div>
            
            <div>
              <h4 className="font-medium mb-3 text-jobblogg-text-strong">Kort Variant</h4>
              <EnhancedTrialBanner variant="card" />
              <BodyText className="text-xs text-jobblogg-text-muted mt-2">
                → Detaljert med fordeler og call-to-action knapper. Perfekt for dashboard eller dedikerte sider.
              </BodyText>
            </div>
          </div>
        </Card>

        {/* Time Simulation */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">⏱️ Tidsbasert Oppførsel (Smart Variant)</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 rounded-lg bg-jobblogg-primary-soft border border-jobblogg-primary/20">
              <h5 className="font-medium text-jobblogg-primary mb-2">Tidlig fase (3+ dager)</h5>
              <ul className="text-sm text-jobblogg-text-medium space-y-1">
                <li>• Blå gradient bakgrunn</li>
                <li>• Oppmuntrende melding</li>
                <li>• "Se hva som er inkludert"</li>
              </ul>
            </div>
            
            <div className="p-4 rounded-lg bg-jobblogg-warning-soft border border-jobblogg-warning/20">
              <h5 className="font-medium text-jobblogg-warning mb-2">Hastegrad (&lt;1 dag)</h5>
              <ul className="text-sm text-jobblogg-text-medium space-y-1">
                <li>• Gul gradient bakgrunn</li>
                <li>• Timer og minutter</li>
                <li>• "Se planer"</li>
              </ul>
            </div>
            
            <div className="p-4 rounded-lg bg-red-50 border border-red-200">
              <h5 className="font-medium text-red-600 mb-2">Kritisk (&lt;1 time)</h5>
              <ul className="text-sm text-jobblogg-text-medium space-y-1">
                <li>• Rød gradient bakgrunn</li>
                <li>• Kun minutter igjen</li>
                <li>• "Oppgrader nå!"</li>
                <li>• Pulserende ikon</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Implementation Guide */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">🔧 Implementeringsguide</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-jobblogg-text-strong">Anbefalt bruk:</h4>
              <div className="bg-jobblogg-surface rounded-lg p-4 mt-2">
                <code className="text-sm">
                  {`// I layout eller dashboard\n<EnhancedTrialBanner variant="smart" showDismiss={true} />\n\n// I sidebar eller header\n<EnhancedTrialBanner variant="compact" />\n\n// På dedikerte subscription-sider\n<EnhancedTrialBanner variant="card" />`}
                </code>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-jobblogg-text-strong">Hovedfunksjoner:</h4>
              <ul className="text-sm text-jobblogg-text-medium space-y-1 mt-2">
                <li>✅ Automatisk oppdatering hvert minutt for live countdown</li>
                <li>✅ Viser timer og minutter når det gjenstår mindre enn 1 døgn</li>
                <li>✅ Responsiv design for alle skjermstørrelser</li>
                <li>✅ Progress bar som viser fremgang gjennom prøveperioden</li>
                <li>✅ Rolle-baserte handlinger (kun administratorer ser "oppgrader" knapper)</li>
                <li>✅ Lokaliserte tekster på norsk</li>
              </ul>
            </div>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4">
          <PrimaryButton onClick={() => window.history.back()}>
            ← Tilbake til applikasjonen
          </PrimaryButton>
          <SecondaryButton onClick={() => window.location.reload()}>
            🔄 Oppdater demo
          </SecondaryButton>
        </div>
      </div>
    </div>
  );
};
