/* JobbLogg Mobile-First Subscription UX Styles */

/* Touch-friendly button utilities */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

.touch-target-large {
  min-height: 56px;
  min-width: 56px;
}

/* Touch manipulation optimization */
.touch-optimized {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

/* Mobile-specific hover states */
@media (hover: hover) and (pointer: fine) {
  .hover-desktop:hover {
    /* Desktop hover styles */
  }
}

@media (hover: none) and (pointer: coarse) {
  .hover-mobile:active {
    /* Mobile active/touch styles */
  }
}

/* Subscription overlay styles */
.subscription-overlay {
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.subscription-overlay-mobile {
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
}

/* Bottom sheet animation utilities */
.bottom-sheet-enter {
  transform: translateY(100%);
  opacity: 0;
}

.bottom-sheet-enter-active {
  transform: translateY(0);
  opacity: 1;
  transition: transform 300ms ease-out, opacity 300ms ease-out;
}

.bottom-sheet-exit {
  transform: translateY(0);
  opacity: 1;
}

.bottom-sheet-exit-active {
  transform: translateY(100%);
  opacity: 0;
  transition: transform 300ms ease-in, opacity 300ms ease-in;
}

/* Mobile-optimized text sizes */
.text-mobile-xs {
  font-size: 0.75rem; /* 12px */
  line-height: 1rem; /* 16px */
}

.text-mobile-sm {
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
}

.text-mobile-base {
  font-size: 1rem; /* 16px */
  line-height: 1.5rem; /* 24px */
}

.text-mobile-lg {
  font-size: 1.125rem; /* 18px */
  line-height: 1.75rem; /* 28px */
}

/* Thumb-friendly positioning */
.thumb-zone-right {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  z-index: 50;
}

.thumb-zone-left {
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  z-index: 50;
}

.thumb-zone-center {
  position: fixed;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 50;
}

/* Mobile-specific spacing */
.mobile-padding {
  padding: 1rem;
}

.mobile-padding-sm {
  padding: 0.75rem;
}

.mobile-padding-lg {
  padding: 1.5rem;
}

/* Safe area handling for mobile devices */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Mobile-optimized subscription button styles */
.subscription-button-mobile {
  @apply touch-target touch-optimized;
  @apply bg-jobblogg-primary text-white font-semibold rounded-xl;
  @apply hover:bg-jobblogg-primary-dark active:bg-jobblogg-primary-dark;
  @apply transition-colors duration-200;
  @apply flex items-center justify-center gap-2;
  @apply shadow-sm hover:shadow-md;
}

.subscription-button-secondary-mobile {
  @apply touch-target touch-optimized;
  @apply bg-gray-100 text-jobblogg-text-medium font-medium rounded-xl;
  @apply hover:bg-gray-200 active:bg-gray-200;
  @apply transition-colors duration-200;
  @apply flex items-center justify-center;
}

/* Mobile-optimized disabled states */
.disabled-overlay-mobile {
  @apply absolute inset-0 bg-white/90 backdrop-blur-sm rounded-lg;
  @apply flex items-center justify-center p-4;
}

.disabled-content-mobile {
  @apply text-center max-w-xs;
}

.disabled-icon-mobile {
  @apply w-12 h-12 bg-jobblogg-warning-soft rounded-full;
  @apply flex items-center justify-center mx-auto mb-3;
}

.disabled-title-mobile {
  @apply font-semibold text-jobblogg-text-strong text-sm mb-1;
}

.disabled-description-mobile {
  @apply text-xs text-jobblogg-text-medium mb-3;
}

/* Mobile-optimized tooltip alternatives */
.mobile-tooltip {
  @apply fixed bottom-4 left-4 right-4 z-50;
  @apply bg-gray-800 text-white text-sm rounded-lg p-4;
  @apply shadow-2xl;
  @apply transform transition-transform duration-300;
}

.mobile-tooltip-hidden {
  @apply translate-y-full opacity-0;
}

.mobile-tooltip-visible {
  @apply translate-y-0 opacity-100;
}

/* Responsive breakpoint utilities */
@media (max-width: 767px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
  
  .mobile-stack {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .mobile-full-width {
    width: 100%;
  }
}

@media (min-width: 768px) {
  .mobile-only {
    display: none;
  }
  
  .desktop-only {
    display: block;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .subscription-overlay {
    backdrop-filter: none;
    background-color: rgba(255, 255, 255, 0.95);
  }
  
  .subscription-button-mobile {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .bottom-sheet-enter-active,
  .bottom-sheet-exit-active,
  .mobile-tooltip {
    transition: none;
  }
  
  .subscription-button-mobile,
  .subscription-button-secondary-mobile {
    transition: none;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .subscription-overlay {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .disabled-overlay-mobile {
    background-color: rgba(0, 0, 0, 0.9);
  }
}
