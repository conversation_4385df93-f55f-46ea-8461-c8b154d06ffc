/**
 * Tests for subscription status formatting utilities
 */

import { 
  getSubscriptionStatusInfo, 
  getSimpleStatusText, 
  getRenewalActionText,
  formatNorwegianDate 
} from '../subscriptionStatusFormatting';

describe('subscriptionStatusFormatting', () => {
  const mockDate = new Date('2024-01-05T10:00:00Z').getTime(); // January 5, 2024, 10:00 UTC
  
  beforeAll(() => {
    // Mock Date.now() to return consistent results
    jest.spyOn(Date, 'now').mockReturnValue(mockDate);
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('formatNorwegianDate', () => {
    it('should format date in Norwegian format without time', () => {
      const timestamp = new Date('2024-10-05T14:30:00Z').getTime();
      const result = formatNorwegianDate(timestamp, false);
      expect(result).toBe('05.10.2024');
    });

    it('should format date in Norwegian format with time', () => {
      const timestamp = new Date('2024-10-05T14:30:00Z').getTime();
      const result = formatNorwegianDate(timestamp, true);
      expect(result).toBe('05.10.2024, 16:30'); // Adjusted for Norwegian timezone
    });
  });

  describe('getSubscriptionStatusInfo', () => {
    it('should handle null subscription', () => {
      const result = getSubscriptionStatusInfo(null);
      expect(result.statusText).toBe('Ingen aktiv plan');
      expect(result.isUrgent).toBe(true);
      expect(result.showRenewalInfo).toBe(false);
    });

    it('should handle active trial subscription', () => {
      const subscription = {
        status: 'trialing',
        planLevel: 'basic',
        billingInterval: 'month',
        trialEnd: mockDate + (5 * 24 * 60 * 60 * 1000), // 5 days from now
      };

      const result = getSubscriptionStatusInfo(subscription);
      expect(result.statusText).toContain('Du tester nå Liten bedrift-planen');
      expect(result.statusText).toContain('månedlig fakturering');
      expect(result.renewalText).toContain('dager igjen av prøveperioden');
      expect(result.isUrgent).toBe(false);
    });

    it('should handle expired trial subscription', () => {
      const subscription = {
        status: 'trialing',
        planLevel: 'professional',
        billingInterval: 'year',
        trialEnd: mockDate - (1 * 24 * 60 * 60 * 1000), // 1 day ago
      };

      const result = getSubscriptionStatusInfo(subscription);
      expect(result.statusText).toContain('Du testet Mellomstor bedrift-planen');
      expect(result.statusText).toContain('Prøveperioden er utløpt');
      expect(result.isUrgent).toBe(true);
    });

    it('should handle active subscription', () => {
      const subscription = {
        status: 'active',
        planLevel: 'enterprise',
        billingInterval: 'month',
        currentPeriodEnd: mockDate + (30 * 24 * 60 * 60 * 1000), // 30 days from now
        cancelAtPeriodEnd: false,
      };

      const result = getSubscriptionStatusInfo(subscription);
      expect(result.statusText).toContain('Du er på Stor bedrift-planen');
      expect(result.renewalText).toContain('Abonnementet fornyes automatisk');
      expect(result.showRenewalInfo).toBe(true);
      expect(result.isUrgent).toBe(false);
    });

    it('should handle cancelled active subscription', () => {
      const subscription = {
        status: 'active',
        planLevel: 'basic',
        billingInterval: 'month',
        currentPeriodEnd: mockDate + (15 * 24 * 60 * 60 * 1000), // 15 days from now
        cancelAtPeriodEnd: true,
      };

      const result = getSubscriptionStatusInfo(subscription);
      expect(result.statusText).toContain('Abonnementet er satt til å avsluttes');
      expect(result.renewalText).toContain('Tilgangen din avsluttes');
      expect(result.renewalText).toContain('vil ikke fornyes automatisk');
      expect(result.showRenewalInfo).toBe(false);
    });

    it('should handle past due subscription', () => {
      const subscription = {
        status: 'past_due',
        planLevel: 'professional',
        billingInterval: 'year',
        currentPeriodEnd: mockDate - (5 * 24 * 60 * 60 * 1000), // 5 days ago
      };

      const result = getSubscriptionStatusInfo(subscription);
      expect(result.statusText).toContain('har forfalt betaling');
      expect(result.renewalText).toContain('Oppdater betalingsmetoden');
      expect(result.isUrgent).toBe(true);
    });

    it('should handle cancelled subscription', () => {
      const subscription = {
        status: 'canceled',
        planLevel: 'basic',
        billingInterval: 'month',
        cancelledAt: mockDate - (10 * 24 * 60 * 60 * 1000), // 10 days ago
      };

      const result = getSubscriptionStatusInfo(subscription);
      expect(result.statusText).toContain('er avbrutt');
      expect(result.renewalText).toContain('reaktivere abonnementet');
      expect(result.isUrgent).toBe(true);
    });
  });

  describe('getSimpleStatusText', () => {
    it('should return simple status for trial', () => {
      const subscription = {
        status: 'trialing',
        planLevel: 'basic',
        trialEnd: mockDate + (5 * 24 * 60 * 60 * 1000),
      };

      const result = getSimpleStatusText(subscription);
      expect(result).toBe('Liten bedrift (prøveperiode)');
    });

    it('should return simple status for expired trial', () => {
      const subscription = {
        status: 'trialing',
        planLevel: 'basic',
        trialEnd: mockDate - (1 * 24 * 60 * 60 * 1000),
      };

      const result = getSimpleStatusText(subscription);
      expect(result).toBe('Prøveperiode utløpt');
    });

    it('should return simple status for active subscription', () => {
      const subscription = {
        status: 'active',
        planLevel: 'professional',
      };

      const result = getSimpleStatusText(subscription);
      expect(result).toBe('Mellomstor bedrift');
    });

    it('should return simple status for cancelled subscription', () => {
      const subscription = {
        status: 'canceled',
        planLevel: 'enterprise',
      };

      const result = getSimpleStatusText(subscription);
      expect(result).toBe('Avbrutt');
    });
  });

  describe('getRenewalActionText', () => {
    it('should return correct action for trial', () => {
      const subscription = {
        status: 'trialing',
        trialEnd: mockDate + (5 * 24 * 60 * 60 * 1000),
      };

      const result = getRenewalActionText(subscription);
      expect(result).toBe('Oppgrader nå');
    });

    it('should return correct action for expired trial', () => {
      const subscription = {
        status: 'trialing',
        trialEnd: mockDate - (1 * 24 * 60 * 60 * 1000),
      };

      const result = getRenewalActionText(subscription);
      expect(result).toBe('Velg plan');
    });

    it('should return correct action for active subscription', () => {
      const subscription = {
        status: 'active',
        cancelAtPeriodEnd: false,
      };

      const result = getRenewalActionText(subscription);
      expect(result).toBe('Administrer');
    });

    it('should return correct action for cancelled active subscription', () => {
      const subscription = {
        status: 'active',
        cancelAtPeriodEnd: true,
      };

      const result = getRenewalActionText(subscription);
      expect(result).toBe('Reaktiver');
    });

    it('should return correct action for past due subscription', () => {
      const subscription = {
        status: 'past_due',
      };

      const result = getRenewalActionText(subscription);
      expect(result).toBe('Oppdater betaling');
    });
  });
});
