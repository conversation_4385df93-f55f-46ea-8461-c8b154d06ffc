/**
 * Address parsing utilities for JobbLogg
 * Handles parsing of Norwegian addresses into structured components
 */

export interface ParsedAddress {
  street: string;
  postalCode: string;
  city: string;
  entrance?: string;
}

/**
 * Parse a Norwegian address string into structured components
 * Handles common Norwegian address formats:
 * - "Storgata 1, 0001 Oslo"
 * - "Storgata 1 0001 Oslo"
 * - "Storgata 1, Oslo"
 * - "Storgata 1 Oslo"
 */
export function parseNorwegianAddress(address: string): ParsedAddress | null {
  if (!address || address.trim() === '') {
    return null;
  }

  const cleanAddress = address.trim();
  
  // Pattern 1: "Street, PostalCode City" or "Street PostalCode City"
  // Norwegian postal codes are always 4 digits
  const pattern1 = /^(.+?)(?:,\s*)?(\d{4})\s+(.+)$/;
  const match1 = cleanAddress.match(pattern1);
  
  if (match1) {
    const [, street, postalCode, city] = match1;
    return {
      street: street.trim(),
      postalCode: postalCode.trim(),
      city: city.trim()
    };
  }
  
  // Pattern 2: "Street, City" (no postal code)
  const pattern2 = /^(.+?),\s*(.+)$/;
  const match2 = cleanAddress.match(pattern2);
  
  if (match2) {
    const [, street, city] = match2;
    return {
      street: street.trim(),
      postalCode: '', // Will need to be filled manually
      city: city.trim()
    };
  }
  
  // Pattern 3: Single string - assume it's all street address
  return {
    street: cleanAddress,
    postalCode: '', // Will need to be filled manually
    city: '' // Will need to be filled manually
  };
}

/**
 * Format structured address back to a single string
 */
export function formatAddress(parsed: ParsedAddress): string {
  const parts = [parsed.street];
  
  if (parsed.postalCode && parsed.city) {
    parts.push(`${parsed.postalCode} ${parsed.city}`);
  } else if (parsed.city) {
    parts.push(parsed.city);
  }
  
  return parts.join(', ');
}

/**
 * Validate that an address has all required components
 */
export function validateStructuredAddress(parsed: ParsedAddress): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!parsed.street || parsed.street.trim() === '') {
    errors.push('Gateadresse er påkrevd');
  }
  
  if (!parsed.postalCode || parsed.postalCode.trim() === '') {
    errors.push('Postnummer er påkrevd');
  } else if (!/^\d{4}$/.test(parsed.postalCode.trim())) {
    errors.push('Postnummer må være 4 siffer');
  }
  
  if (!parsed.city || parsed.city.trim() === '') {
    errors.push('By/sted er påkrevd');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
