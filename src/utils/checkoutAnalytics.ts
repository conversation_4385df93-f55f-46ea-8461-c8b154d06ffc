/**
 * Checkout Analytics Utilities
 * 
 * Provides analytics tracking for checkout events including
 * cancellations, completions, and user behavior patterns.
 */

export interface CheckoutEvent {
  type: 'checkout_started' | 'checkout_completed' | 'checkout_cancelled' | 'checkout_retry';
  userId?: string;
  sessionId?: string;
  planLevel?: 'basic' | 'professional' | 'enterprise';
  billingInterval?: 'month' | 'year';
  amount?: number;
  currency?: string;
  context?: 'trial_setup' | 'trial_conversion' | 'plan_upgrade' | 'unknown';
  cancellationReason?: string;
  timestamp: number;
  userAgent?: string;
  referrer?: string;
}

export interface CheckoutCancellationData {
  type: 'trial_setup' | 'trial_conversion' | 'plan_upgrade' | 'unknown';
  planLevel?: 'basic' | 'professional' | 'enterprise';
  billingInterval?: 'month' | 'year';
  isTrialExpired: boolean;
  daysLeftInTrial: number;
  userId?: string;
  subscriptionStatus?: string;
  reason?: 'user_cancelled' | 'payment_failed' | 'technical_error' | 'unknown';
}

/**
 * Track checkout cancellation event
 */
export function trackCheckoutCancellation(data: CheckoutCancellationData): void {
  const event: CheckoutEvent = {
    type: 'checkout_cancelled',
    userId: data.userId,
    planLevel: data.planLevel,
    billingInterval: data.billingInterval,
    context: data.type,
    cancellationReason: data.reason || 'user_cancelled',
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    referrer: document.referrer,
  };

  // Log to console for development
  console.log('📊 Checkout Cancellation Tracked:', event);

  // In production, this would send to analytics service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to analytics service
    // analyticsService.track('checkout_cancelled', event);
  }

  // Store in localStorage for debugging
  const existingEvents = getStoredCheckoutEvents();
  existingEvents.push(event);
  
  // Keep only last 100 events
  const recentEvents = existingEvents.slice(-100);
  localStorage.setItem('jobblogg_checkout_events', JSON.stringify(recentEvents));
}

/**
 * Track checkout completion event
 */
export function trackCheckoutCompletion(data: {
  userId?: string;
  sessionId: string;
  planLevel: 'basic' | 'professional' | 'enterprise';
  billingInterval: 'month' | 'year';
  amount: number;
  currency: string;
  isTrialConversion: boolean;
}): void {
  const event: CheckoutEvent = {
    type: 'checkout_completed',
    userId: data.userId,
    sessionId: data.sessionId,
    planLevel: data.planLevel,
    billingInterval: data.billingInterval,
    amount: data.amount,
    currency: data.currency,
    context: data.isTrialConversion ? 'trial_conversion' : 'plan_upgrade',
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    referrer: document.referrer,
  };

  console.log('📊 Checkout Completion Tracked:', event);

  if (process.env.NODE_ENV === 'production') {
    // Send to analytics service
  }

  const existingEvents = getStoredCheckoutEvents();
  existingEvents.push(event);
  const recentEvents = existingEvents.slice(-100);
  localStorage.setItem('jobblogg_checkout_events', JSON.stringify(recentEvents));
}

/**
 * Track checkout retry event
 */
export function trackCheckoutRetry(data: {
  userId?: string;
  planLevel: 'basic' | 'professional' | 'enterprise';
  billingInterval: 'month' | 'year';
  context: 'trial_setup' | 'trial_conversion' | 'plan_upgrade';
  previousCancellationReason?: string;
}): void {
  const event: CheckoutEvent = {
    type: 'checkout_retry',
    userId: data.userId,
    planLevel: data.planLevel,
    billingInterval: data.billingInterval,
    context: data.context,
    cancellationReason: data.previousCancellationReason,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    referrer: document.referrer,
  };

  console.log('📊 Checkout Retry Tracked:', event);

  if (process.env.NODE_ENV === 'production') {
    // Send to analytics service
  }

  const existingEvents = getStoredCheckoutEvents();
  existingEvents.push(event);
  const recentEvents = existingEvents.slice(-100);
  localStorage.setItem('jobblogg_checkout_events', JSON.stringify(recentEvents));
}

/**
 * Get stored checkout events from localStorage
 */
export function getStoredCheckoutEvents(): CheckoutEvent[] {
  try {
    const stored = localStorage.getItem('jobblogg_checkout_events');
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.warn('Failed to parse stored checkout events:', error);
    return [];
  }
}

/**
 * Get checkout analytics summary
 */
export function getCheckoutAnalyticsSummary(): {
  totalEvents: number;
  completions: number;
  cancellations: number;
  retries: number;
  completionRate: number;
  cancellationsByType: Record<string, number>;
  popularPlans: Record<string, number>;
} {
  const events = getStoredCheckoutEvents();
  
  const completions = events.filter(e => e.type === 'checkout_completed').length;
  const cancellations = events.filter(e => e.type === 'checkout_cancelled').length;
  const retries = events.filter(e => e.type === 'checkout_retry').length;
  
  const completionRate = completions + cancellations > 0 
    ? (completions / (completions + cancellations)) * 100 
    : 0;

  const cancellationsByType = events
    .filter(e => e.type === 'checkout_cancelled')
    .reduce((acc, event) => {
      const type = event.context || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

  const popularPlans = events
    .filter(e => e.planLevel)
    .reduce((acc, event) => {
      const plan = event.planLevel!;
      acc[plan] = (acc[plan] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

  return {
    totalEvents: events.length,
    completions,
    cancellations,
    retries,
    completionRate: Math.round(completionRate * 100) / 100,
    cancellationsByType,
    popularPlans,
  };
}

/**
 * Clear stored checkout events
 */
export function clearCheckoutEvents(): void {
  localStorage.removeItem('jobblogg_checkout_events');
  console.log('📊 Checkout events cleared');
}

/**
 * Export checkout events for analysis
 */
export function exportCheckoutEvents(): string {
  const events = getStoredCheckoutEvents();
  const summary = getCheckoutAnalyticsSummary();
  
  const exportData = {
    summary,
    events,
    exportedAt: new Date().toISOString(),
    version: '1.0',
  };

  return JSON.stringify(exportData, null, 2);
}

/**
 * Determine cancellation reason based on context
 */
export function determineCancellationReason(
  context: string,
  userAgent: string,
  timeOnPage?: number
): string {
  // Quick exit (less than 10 seconds) might indicate technical issues
  if (timeOnPage && timeOnPage < 10000) {
    return 'quick_exit_possible_technical_issue';
  }

  // Mobile users might have different cancellation patterns
  const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
  
  switch (context) {
    case 'trial_setup':
      return isMobile ? 'trial_setup_mobile_cancel' : 'trial_setup_desktop_cancel';
    case 'trial_conversion':
      return 'trial_conversion_cancel';
    case 'plan_upgrade':
      return 'plan_upgrade_cancel';
    default:
      return 'unknown_cancel';
  }
}
