/**
 * Feature Flags for JobbLogg Application
 * 
 * This module provides a centralized way to control feature visibility
 * and manage the transition from legacy to modern systems.
 */

export interface FeatureFlags {
  // Modern system controls
  enableThreadedComments: boolean;
  enableCustomerDataPersistence: boolean;

  // Development and debugging
  debugMode: boolean;
  showSystemInfo: boolean;
}

// Default feature flag configuration
const DEFAULT_FLAGS: FeatureFlags = {
  // Modern system - fully enabled
  enableThreadedComments: true,    // Enable new threaded conversation system
  enableCustomerDataPersistence: true, // Enable localStorage customer data

  // Development - disabled in production
  debugMode: false,                // Debug logging disabled
  showSystemInfo: false           // System information hidden
};

// Environment-based overrides
const getEnvironmentFlags = (): Partial<FeatureFlags> => {
  const env = import.meta.env.MODE;
  
  switch (env) {
    case 'development':
      return {
        debugMode: true,
        showSystemInfo: true
      };
    
    case 'production':
      return {
        // In production, be more conservative
        debugMode: false,
        showSystemInfo: false
      };
    
    default:
      return {};
  }
};

// URL parameter overrides (for testing)
const getUrlParameterFlags = (): Partial<FeatureFlags> => {
  if (typeof window === 'undefined') return {};
  
  const params = new URLSearchParams(window.location.search);
  const flags: Partial<FeatureFlags> = {};
  
  // Allow URL parameters to override flags for testing
  // Example: ?debug=true&sysinfo=true
  
  if (params.has('debug')) {
    flags.debugMode = params.get('debug') === 'true';
  }
  
  if (params.has('sysinfo')) {
    flags.showSystemInfo = params.get('sysinfo') === 'true';
  }
  
  return flags;
};

// Combine all flag sources
const getFeatureFlags = (): FeatureFlags => {
  return {
    ...DEFAULT_FLAGS,
    ...getEnvironmentFlags(),
    ...getUrlParameterFlags()
  };
};

// Export the current feature flags
export const featureFlags = getFeatureFlags();

// Utility functions for common checks
export const isThreadedCommentsEnabled = (): boolean => {
  return featureFlags.enableThreadedComments;
};

export const isDebugMode = (): boolean => {
  return featureFlags.debugMode;
};

// Debug logging utility
export const debugLog = (message: string, data?: any): void => {
  if (isDebugMode()) {
    console.log(`[JobbLogg Debug] ${message}`, data || '');
  }
};

// Feature flag status for debugging
export const getFeatureFlagStatus = (): string => {
  return Object.entries(featureFlags)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n');
};

// React hook for feature flags (if needed)
export const useFeatureFlag = (flag: keyof FeatureFlags): boolean => {
  return featureFlags[flag] as boolean;
};


