/**
 * Currency formatting utilities for JobbLogg
 * 
 * Provides Norwegian-localized currency formatting for various currencies
 * with proper handling of NOK and international currencies.
 */

/**
 * Format currency amount with proper Norwegian localization
 * 
 * @param amount - The amount to format (in major currency units, e.g., 299 for 299 NOK)
 * @param currency - The currency code (e.g., 'NOK', 'USD', 'EUR')
 * @param options - Additional formatting options
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number, 
  currency: string = 'NOK',
  options: {
    showDecimals?: boolean;
    locale?: string;
  } = {}
): string {
  const {
    showDecimals = false,
    locale = 'nb-NO'
  } = options;

  try {
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: showDecimals ? 2 : 0,
      maximumFractionDigits: showDecimals ? 2 : 0,
    });

    return formatter.format(amount);
  } catch (error) {
    // Fallback for unsupported currencies
    console.warn(`Currency formatting failed for ${currency}:`, error);
    return `${amount.toLocaleString(locale)} ${currency.toUpperCase()}`;
  }
}

/**
 * Format Norwegian currency (NOK) with proper formatting
 * 
 * @param amount - The amount in NOK
 * @param showDecimals - Whether to show decimal places
 * @returns Formatted NOK string
 */
export function formatNOK(amount: number, showDecimals: boolean = false): string {
  return formatCurrency(amount, 'NOK', { showDecimals });
}

/**
 * Format price for display in pricing tables
 * 
 * @param amount - The amount to format
 * @param currency - The currency code
 * @param period - The billing period ('month' | 'year')
 * @returns Formatted price string with period
 */
export function formatPriceWithPeriod(
  amount: number, 
  currency: string = 'NOK',
  period: 'month' | 'year' = 'month'
): string {
  const formattedAmount = formatCurrency(amount, currency);
  const periodText = period === 'month' ? '/md.' : '/år';
  
  return `${formattedAmount}${periodText}`;
}

/**
 * Format subscription price with VAT information
 * 
 * @param amount - The amount excluding VAT
 * @param currency - The currency code
 * @param period - The billing period
 * @param includeVat - Whether to show VAT-inclusive price
 * @returns Formatted price with VAT information
 */
export function formatSubscriptionPrice(
  amount: number,
  currency: string = 'NOK',
  period: 'month' | 'year' = 'month',
  includeVat: boolean = true
): {
  priceExclVat: string;
  priceInclVat: string;
  vatAmount: string;
  displayText: string;
} {
  const vatRate = 0.25; // 25% Norwegian VAT
  const vatAmount = amount * vatRate;
  const amountInclVat = amount + vatAmount;
  
  const periodText = period === 'month' ? '/md.' : '/år';
  
  const priceExclVat = formatCurrency(amount, currency);
  const priceInclVat = formatCurrency(amountInclVat, currency);
  const formattedVatAmount = formatCurrency(vatAmount, currency);
  
  const displayText = includeVat 
    ? `${priceInclVat}${periodText} (inkl. mva.)`
    : `${priceExclVat}${periodText} (ekskl. mva.)`;
  
  return {
    priceExclVat: `${priceExclVat}${periodText}`,
    priceInclVat: `${priceInclVat}${periodText}`,
    vatAmount: formattedVatAmount,
    displayText,
  };
}

/**
 * Format amount for Stripe (convert to minor currency units)
 * 
 * @param amount - The amount in major currency units
 * @param currency - The currency code
 * @returns Amount in minor currency units (e.g., øre for NOK, cents for USD)
 */
export function formatAmountForStripe(amount: number, currency: string = 'NOK'): number {
  // Most currencies use 2 decimal places (100 minor units = 1 major unit)
  // Some currencies like JPY use 0 decimal places
  const zeroDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP'];
  
  if (zeroDecimalCurrencies.includes(currency.toUpperCase())) {
    return Math.round(amount);
  }
  
  return Math.round(amount * 100);
}

/**
 * Format amount from Stripe (convert from minor currency units)
 * 
 * @param amount - The amount in minor currency units (from Stripe)
 * @param currency - The currency code
 * @returns Amount in major currency units
 */
export function formatAmountFromStripe(amount: number, currency: string = 'NOK'): number {
  // Most currencies use 2 decimal places (100 minor units = 1 major unit)
  // Some currencies like JPY use 0 decimal places
  const zeroDecimalCurrencies = ['JPY', 'KRW', 'VND', 'CLP'];
  
  if (zeroDecimalCurrencies.includes(currency.toUpperCase())) {
    return amount;
  }
  
  return amount / 100;
}

/**
 * Calculate annual savings and format for display
 * 
 * @param monthlyPrice - Monthly price
 * @param annualPrice - Annual price
 * @param currency - Currency code
 * @returns Formatted savings information
 */
export function formatAnnualSavings(
  monthlyPrice: number,
  annualPrice: number,
  currency: string = 'NOK'
): {
  savingsAmount: string;
  savingsPercentage: string;
  displayText: string;
} {
  const totalMonthlyPrice = monthlyPrice * 12;
  const savings = totalMonthlyPrice - annualPrice;
  const savingsPercentage = Math.round((savings / totalMonthlyPrice) * 100);
  
  const savingsAmount = formatCurrency(savings, currency);
  const savingsPercentageText = `${savingsPercentage}%`;
  
  return {
    savingsAmount,
    savingsPercentage: savingsPercentageText,
    displayText: `Spar ${savingsAmount} (${savingsPercentageText}) per år`,
  };
}

/**
 * Format payment confirmation details
 * 
 * @param amount - Payment amount in minor currency units (from Stripe)
 * @param currency - Currency code
 * @param paymentDate - Payment date
 * @returns Formatted payment details
 */
export function formatPaymentConfirmation(
  amount: number,
  currency: string,
  paymentDate: Date = new Date()
): {
  amount: string;
  currency: string;
  date: string;
  displayText: string;
} {
  const formattedAmount = formatCurrency(formatAmountFromStripe(amount, currency), currency);
  const formattedDate = paymentDate.toLocaleDateString('nb-NO', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });
  
  return {
    amount: formattedAmount,
    currency: currency.toUpperCase(),
    date: formattedDate,
    displayText: `Betaling på ${formattedAmount} bekreftet ${formattedDate}`,
  };
}
