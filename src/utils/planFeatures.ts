// Plan feature definitions and access control utilities

export interface PlanFeatures {
  maxProjects: number;
  maxTeamMembers: number;
  maxStorageGB: number;
  hasAdvancedReports: boolean;
  hasApiAccess: boolean;
  hasCustomIntegrations: boolean;
  hasPrioritySupport: boolean;
  hasDedicatedSupport: boolean;
  hasSlaGuarantee: boolean;
  hasAdvancedSecurity: boolean;
  hasCustomBranding: boolean;
  hasAdvancedPermissions: boolean;
}

export const PLAN_FEATURES: Record<string, PlanFeatures> = {
  basic: {
    maxProjects: 5,
    maxTeamMembers: 3,
    maxStorageGB: 5,
    hasAdvancedReports: false,
    hasApiAccess: false,
    hasCustomIntegrations: false,
    hasPrioritySupport: false,
    hasDedicatedSupport: false,
    hasSlaGuarantee: false,
    hasAdvancedSecurity: false,
    hasCustomBranding: false,
    hasAdvancedPermissions: false,
  },
  professional: {
    maxProjects: -1, // Unlimited
    maxTeamMembers: 25,
    maxStorageGB: 100,
    hasAdvancedReports: true,
    hasApiAccess: true,
    hasCustomIntegrations: true,
    hasPrioritySupport: true,
    hasDedicatedSupport: false,
    hasSlaGuarantee: false,
    hasAdvancedSecurity: true,
    hasCustomBranding: true,
    hasAdvancedPermissions: true,
  },
  enterprise: {
    maxProjects: -1, // Unlimited
    maxTeamMembers: -1, // Unlimited
    maxStorageGB: -1, // Unlimited
    hasAdvancedReports: true,
    hasApiAccess: true,
    hasCustomIntegrations: true,
    hasPrioritySupport: true,
    hasDedicatedSupport: true,
    hasSlaGuarantee: true,
    hasAdvancedSecurity: true,
    hasCustomBranding: true,
    hasAdvancedPermissions: true,
  },
};

export const PLAN_HIERARCHY = ['basic', 'professional', 'enterprise'];

/**
 * Check if a user has access to a specific feature
 */
export function hasFeatureAccess(userPlan: string, feature: keyof PlanFeatures): boolean {
  const planFeatures = PLAN_FEATURES[userPlan];
  if (!planFeatures) return false;
  
  return planFeatures[feature] as boolean;
}

/**
 * Check if a user can create more projects
 */
export function canCreateProject(userPlan: string, currentProjectCount: number): boolean {
  const planFeatures = PLAN_FEATURES[userPlan];
  if (!planFeatures) return false;
  
  if (planFeatures.maxProjects === -1) return true; // Unlimited
  return currentProjectCount < planFeatures.maxProjects;
}

/**
 * Check if a user can add more team members
 */
export function canAddTeamMember(userPlan: string, currentTeamSize: number): boolean {
  const planFeatures = PLAN_FEATURES[userPlan];
  if (!planFeatures) return false;
  
  if (planFeatures.maxTeamMembers === -1) return true; // Unlimited
  return currentTeamSize < planFeatures.maxTeamMembers;
}

/**
 * Get the minimum plan required for a feature
 */
export function getMinimumPlanForFeature(feature: keyof PlanFeatures): string | null {
  for (const planId of PLAN_HIERARCHY) {
    const planFeatures = PLAN_FEATURES[planId];
    if (planFeatures[feature]) {
      return planId;
    }
  }
  return null;
}

/**
 * Get usage limits for a plan
 */
export function getPlanLimits(planId: string): {
  projects: number | 'unlimited';
  teamMembers: number | 'unlimited';
  storageGB: number | 'unlimited';
} {
  const features = PLAN_FEATURES[planId];
  if (!features) {
    return { projects: 0, teamMembers: 0, storageGB: 0 };
  }
  
  return {
    projects: features.maxProjects === -1 ? 'unlimited' : features.maxProjects,
    teamMembers: features.maxTeamMembers === -1 ? 'unlimited' : features.maxTeamMembers,
    storageGB: features.maxStorageGB === -1 ? 'unlimited' : features.maxStorageGB,
  };
}

/**
 * Check if downgrading would cause feature loss
 */
export function getDowngradeRestrictions(
  currentPlan: string,
  targetPlan: string,
  currentUsage: {
    projectCount: number;
    teamMemberCount: number;
    storageUsedGB: number;
  }
): {
  canDowngrade: boolean;
  restrictions: string[];
  warnings: string[];
} {
  const currentFeatures = PLAN_FEATURES[currentPlan];
  const targetFeatures = PLAN_FEATURES[targetPlan];
  
  if (!currentFeatures || !targetFeatures) {
    return {
      canDowngrade: false,
      restrictions: ['Ugyldig plan'],
      warnings: [],
    };
  }
  
  const restrictions: string[] = [];
  const warnings: string[] = [];
  
  // Check project limits
  if (targetFeatures.maxProjects !== -1 && currentUsage.projectCount > targetFeatures.maxProjects) {
    restrictions.push(
      `Du har ${currentUsage.projectCount} prosjekter, men ${targetPlan} tillater kun ${targetFeatures.maxProjects}`
    );
  }
  
  // Check team member limits
  if (targetFeatures.maxTeamMembers !== -1 && currentUsage.teamMemberCount > targetFeatures.maxTeamMembers) {
    restrictions.push(
      `Du har ${currentUsage.teamMemberCount} teammedlemmer, men ${targetPlan} tillater kun ${targetFeatures.maxTeamMembers}`
    );
  }
  
  // Check storage limits
  if (targetFeatures.maxStorageGB !== -1 && currentUsage.storageUsedGB > targetFeatures.maxStorageGB) {
    restrictions.push(
      `Du bruker ${currentUsage.storageUsedGB}GB lagring, men ${targetPlan} tillater kun ${targetFeatures.maxStorageGB}GB`
    );
  }
  
  // Check feature warnings
  if (currentFeatures.hasAdvancedReports && !targetFeatures.hasAdvancedReports) {
    warnings.push('Du vil miste tilgang til avanserte rapporter');
  }
  
  if (currentFeatures.hasApiAccess && !targetFeatures.hasApiAccess) {
    warnings.push('Du vil miste API-tilgang');
  }
  
  if (currentFeatures.hasCustomIntegrations && !targetFeatures.hasCustomIntegrations) {
    warnings.push('Du vil miste tilpassede integrasjoner');
  }
  
  if (currentFeatures.hasPrioritySupport && !targetFeatures.hasPrioritySupport) {
    warnings.push('Du vil miste prioritert støtte');
  }
  
  if (currentFeatures.hasDedicatedSupport && !targetFeatures.hasDedicatedSupport) {
    warnings.push('Du vil miste dedikert kundesuksessansvarlig');
  }
  
  return {
    canDowngrade: restrictions.length === 0,
    restrictions,
    warnings,
  };
}

/**
 * Get plan comparison data
 */
export function comparePlans(plan1: string, plan2: string): {
  upgrades: string[];
  downgrades: string[];
  unchanged: string[];
} {
  const features1 = PLAN_FEATURES[plan1];
  const features2 = PLAN_FEATURES[plan2];
  
  if (!features1 || !features2) {
    return { upgrades: [], downgrades: [], unchanged: [] };
  }
  
  const upgrades: string[] = [];
  const downgrades: string[] = [];
  const unchanged: string[] = [];
  
  // Compare each feature
  const featureNames: Array<keyof PlanFeatures> = [
    'maxProjects',
    'maxTeamMembers',
    'maxStorageGB',
    'hasAdvancedReports',
    'hasApiAccess',
    'hasCustomIntegrations',
    'hasPrioritySupport',
    'hasDedicatedSupport',
    'hasSlaGuarantee',
    'hasAdvancedSecurity',
    'hasCustomBranding',
    'hasAdvancedPermissions',
  ];
  
  featureNames.forEach(feature => {
    const value1 = features1[feature];
    const value2 = features2[feature];
    
    if (typeof value1 === 'boolean' && typeof value2 === 'boolean') {
      if (!value1 && value2) {
        upgrades.push(getFeatureDisplayName(feature));
      } else if (value1 && !value2) {
        downgrades.push(getFeatureDisplayName(feature));
      } else {
        unchanged.push(getFeatureDisplayName(feature));
      }
    } else if (typeof value1 === 'number' && typeof value2 === 'number') {
      if (value1 < value2 || (value1 !== -1 && value2 === -1)) {
        upgrades.push(getFeatureDisplayName(feature));
      } else if (value1 > value2 || (value1 === -1 && value2 !== -1)) {
        downgrades.push(getFeatureDisplayName(feature));
      } else {
        unchanged.push(getFeatureDisplayName(feature));
      }
    }
  });
  
  return { upgrades, downgrades, unchanged };
}

/**
 * Get user-friendly feature names
 */
function getFeatureDisplayName(feature: keyof PlanFeatures): string {
  const displayNames: Record<keyof PlanFeatures, string> = {
    maxProjects: 'Antall prosjekter',
    maxTeamMembers: 'Teammedlemmer',
    maxStorageGB: 'Lagringsplass',
    hasAdvancedReports: 'Avanserte rapporter',
    hasApiAccess: 'API-tilgang',
    hasCustomIntegrations: 'Tilpassede integrasjoner',
    hasPrioritySupport: 'Prioritert støtte',
    hasDedicatedSupport: 'Dedikert kundesuksessansvarlig',
    hasSlaGuarantee: 'SLA-garanti',
    hasAdvancedSecurity: 'Avansert sikkerhet',
    hasCustomBranding: 'Tilpasset branding',
    hasAdvancedPermissions: 'Avanserte tillatelser',
  };
  
  return displayNames[feature] || feature;
}

/**
 * Calculate savings for annual billing
 */
export function calculateAnnualSavings(monthlyPrice: number): {
  annualPrice: number;
  monthlySavings: number;
  totalSavings: number;
  discountPercentage: number;
} {
  const discountPercentage = 20;
  const annualPrice = Math.round(monthlyPrice * 12 * (1 - discountPercentage / 100));
  const monthlySavings = Math.round((monthlyPrice * 12 - annualPrice) / 12);
  const totalSavings = monthlyPrice * 12 - annualPrice;
  
  return {
    annualPrice,
    monthlySavings,
    totalSavings,
    discountPercentage,
  };
}
