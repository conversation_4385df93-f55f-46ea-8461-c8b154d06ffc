/**
 * PWA Installation Preferences Management
 * Handles user preferences for PWA installation prompts with GDPR compliance
 */

export interface PWAPreferences {
  /** Whether user has permanently dismissed PWA installation prompts */
  permanentlyDismissed: boolean;
  /** Timestamp of last dismissal */
  lastDismissedAt: number | null;
  /** Number of times user has dismissed the prompt */
  dismissalCount: number;
  /** Timestamp of last installation attempt */
  lastInstallAttemptAt: number | null;
  /** Whether user has successfully installed the PWA */
  hasInstalled: boolean;
  /** User's preferred re-prompting interval in days (0 = never) */
  repromptIntervalDays: number;
  /** Timestamps of significant user milestones for smart prompting */
  milestones: {
    firstProjectCreated?: number;
    firstProjectCompleted?: number;
    tenthProjectCreated?: number;
    firstWeekActive?: number;
    firstMonthActive?: number;
  };
  /** Last time we showed a milestone-based prompt */
  lastMilestonePromptAt: number | null;
  /** User's consent timestamp for preference tracking */
  consentTimestamp: number;
  /** Version of preferences schema */
  version: string;
}

const DEFAULT_PREFERENCES: PWAPreferences = {
  permanentlyDismissed: false,
  lastDismissedAt: null,
  dismissalCount: 0,
  lastInstallAttemptAt: null,
  hasInstalled: false,
  repromptIntervalDays: 7, // Default: show again after 7 days
  milestones: {},
  lastMilestonePromptAt: null,
  consentTimestamp: Date.now(),
  version: '1.0.0'
};

const STORAGE_KEY = 'jobblogg-pwa-preferences';
const MAX_DISMISSAL_COUNT = 3; // After 3 dismissals, increase interval significantly
const MILESTONE_COOLDOWN_DAYS = 14; // Wait 14 days between milestone prompts

/**
 * PWA Preferences Manager
 * Handles all PWA installation preference logic with smart prompting
 */
export class PWAPreferencesManager {
  private preferences: PWAPreferences;
  private userId: string | null = null;

  constructor(userId?: string) {
    this.userId = userId || null;
    this.preferences = this.loadPreferences();
  }

  /**
   * Load preferences from localStorage
   */
  private loadPreferences(): PWAPreferences {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) {
        return { ...DEFAULT_PREFERENCES };
      }

      const parsed = JSON.parse(stored);
      
      // Validate and migrate if needed
      if (parsed.version !== DEFAULT_PREFERENCES.version) {
        return this.migratePreferences(parsed);
      }

      return { ...DEFAULT_PREFERENCES, ...parsed };
    } catch (error) {
      console.warn('[PWA Preferences] Failed to load preferences:', error);
      return { ...DEFAULT_PREFERENCES };
    }
  }

  /**
   * Save preferences to localStorage
   */
  private savePreferences(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.preferences));
    } catch (error) {
      console.warn('[PWA Preferences] Failed to save preferences:', error);
    }
  }

  /**
   * Migrate preferences from older versions
   */
  private migratePreferences(oldPrefs: any): PWAPreferences {
    console.log('[PWA Preferences] Migrating preferences to version', DEFAULT_PREFERENCES.version);
    
    // Handle migration from legacy pwa-install-dismissed
    const legacyDismissed = localStorage.getItem('pwa-install-dismissed') === 'true';
    
    return {
      ...DEFAULT_PREFERENCES,
      permanentlyDismissed: oldPrefs.permanentlyDismissed || legacyDismissed,
      dismissalCount: oldPrefs.dismissalCount || (legacyDismissed ? 1 : 0),
      lastDismissedAt: oldPrefs.lastDismissedAt || (legacyDismissed ? Date.now() : null),
      hasInstalled: oldPrefs.hasInstalled || false,
      milestones: oldPrefs.milestones || {},
    };
  }

  /**
   * Check if PWA installation prompt should be shown
   */
  shouldShowInstallPrompt(): boolean {
    // Never show if permanently dismissed or already installed
    if (this.preferences.permanentlyDismissed || this.preferences.hasInstalled) {
      return false;
    }

    // Never show if no re-prompting desired
    if (this.preferences.repromptIntervalDays === 0) {
      return false;
    }

    // Check if enough time has passed since last dismissal
    if (this.preferences.lastDismissedAt) {
      const daysSinceLastDismissal = (Date.now() - this.preferences.lastDismissedAt) / (1000 * 60 * 60 * 24);
      const requiredInterval = this.getRequiredInterval();
      
      if (daysSinceLastDismissal < requiredInterval) {
        return false;
      }
    }

    return true;
  }

  /**
   * Get required interval based on dismissal count (progressive backoff)
   */
  private getRequiredInterval(): number {
    const baseInterval = this.preferences.repromptIntervalDays;
    
    if (this.preferences.dismissalCount >= MAX_DISMISSAL_COUNT) {
      return baseInterval * 4; // 28 days after 3+ dismissals
    } else if (this.preferences.dismissalCount >= 2) {
      return baseInterval * 2; // 14 days after 2 dismissals
    }
    
    return baseInterval; // 7 days for first dismissal
  }

  /**
   * Check if milestone-based prompt should be shown
   */
  shouldShowMilestonePrompt(milestone: keyof PWAPreferences['milestones']): boolean {
    // Don't show if permanently dismissed or already installed
    if (this.preferences.permanentlyDismissed || this.preferences.hasInstalled) {
      return false;
    }

    // Don't show if milestone already recorded
    if (this.preferences.milestones[milestone]) {
      return false;
    }

    // Check cooldown period
    if (this.preferences.lastMilestonePromptAt) {
      const daysSinceLastMilestone = (Date.now() - this.preferences.lastMilestonePromptAt) / (1000 * 60 * 60 * 24);
      if (daysSinceLastMilestone < MILESTONE_COOLDOWN_DAYS) {
        return false;
      }
    }

    return true;
  }

  /**
   * Record that user dismissed the installation prompt
   */
  recordDismissal(isPermanent: boolean = false): void {
    this.preferences.lastDismissedAt = Date.now();
    this.preferences.dismissalCount += 1;
    this.preferences.permanentlyDismissed = isPermanent;

    // Clean up legacy storage
    localStorage.removeItem('pwa-install-dismissed');

    this.savePreferences();
    
    console.log('[PWA Preferences] Recorded dismissal:', {
      isPermanent,
      dismissalCount: this.preferences.dismissalCount,
      nextPromptIn: isPermanent ? 'never' : `${this.getRequiredInterval()} days`
    });
  }

  /**
   * Record successful PWA installation
   */
  recordInstallation(): void {
    this.preferences.hasInstalled = true;
    this.preferences.lastInstallAttemptAt = Date.now();
    this.preferences.permanentlyDismissed = true; // No need to prompt anymore
    
    this.savePreferences();
    
    console.log('[PWA Preferences] Recorded successful installation');
  }

  /**
   * Record a user milestone for smart prompting
   */
  recordMilestone(milestone: keyof PWAPreferences['milestones']): void {
    this.preferences.milestones[milestone] = Date.now();
    this.savePreferences();
    
    console.log('[PWA Preferences] Recorded milestone:', milestone);
  }

  /**
   * Record that we showed a milestone-based prompt
   */
  recordMilestonePrompt(): void {
    this.preferences.lastMilestonePromptAt = Date.now();
    this.savePreferences();
  }

  /**
   * Update user's re-prompting preference
   */
  updateRepromptInterval(days: number): void {
    this.preferences.repromptIntervalDays = Math.max(0, days);
    this.savePreferences();
    
    console.log('[PWA Preferences] Updated re-prompt interval to', days, 'days');
  }

  /**
   * Reset all preferences (for testing or user request)
   */
  resetPreferences(): void {
    this.preferences = { ...DEFAULT_PREFERENCES, consentTimestamp: Date.now() };
    localStorage.removeItem('pwa-install-dismissed'); // Clean up legacy
    this.savePreferences();
    
    console.log('[PWA Preferences] Reset all preferences');
  }

  /**
   * Get current preferences (read-only)
   */
  getPreferences(): Readonly<PWAPreferences> {
    return { ...this.preferences };
  }

  /**
   * Get user-friendly status for debugging
   */
  getStatus(): {
    canShowPrompt: boolean;
    reason: string;
    nextPromptDate: Date | null;
    dismissalCount: number;
    hasInstalled: boolean;
  } {
    const canShow = this.shouldShowInstallPrompt();
    let reason = '';
    let nextPromptDate: Date | null = null;

    if (this.preferences.hasInstalled) {
      reason = 'PWA already installed';
    } else if (this.preferences.permanentlyDismissed) {
      reason = 'Permanently dismissed by user';
    } else if (this.preferences.repromptIntervalDays === 0) {
      reason = 'User disabled re-prompting';
    } else if (this.preferences.lastDismissedAt) {
      const requiredInterval = this.getRequiredInterval();
      nextPromptDate = new Date(this.preferences.lastDismissedAt + (requiredInterval * 24 * 60 * 60 * 1000));
      reason = canShow ? 'Ready to show' : `Waiting until ${nextPromptDate.toLocaleDateString('nb-NO')}`;
    } else {
      reason = 'Ready to show (first time)';
    }

    return {
      canShowPrompt: canShow,
      reason,
      nextPromptDate,
      dismissalCount: this.preferences.dismissalCount,
      hasInstalled: this.preferences.hasInstalled
    };
  }
}

/**
 * Global instance for easy access
 * Will be initialized with user ID when available
 */
let globalManager: PWAPreferencesManager | null = null;

/**
 * Get or create the global PWA preferences manager
 */
export function getPWAPreferencesManager(userId?: string): PWAPreferencesManager {
  if (!globalManager || (userId && globalManager['userId'] !== userId)) {
    globalManager = new PWAPreferencesManager(userId);
  }
  return globalManager;
}

/**
 * Convenience functions for common operations
 */
export const PWAPreferences = {
  shouldShow: (userId?: string) => getPWAPreferencesManager(userId).shouldShowInstallPrompt(),
  recordDismissal: (isPermanent: boolean = false, userId?: string) => 
    getPWAPreferencesManager(userId).recordDismissal(isPermanent),
  recordInstallation: (userId?: string) => getPWAPreferencesManager(userId).recordInstallation(),
  recordMilestone: (milestone: keyof PWAPreferences['milestones'], userId?: string) => 
    getPWAPreferencesManager(userId).recordMilestone(milestone),
  getStatus: (userId?: string) => getPWAPreferencesManager(userId).getStatus(),
  reset: (userId?: string) => getPWAPreferencesManager(userId).resetPreferences(),
};
