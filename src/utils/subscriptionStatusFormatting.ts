/**
 * Subscription status formatting utilities for JobbLogg
 * Provides dynamic, context-aware subscription status messages
 */

import { formatTimeRemaining } from './timeFormatting';

export interface SubscriptionStatusInfo {
  statusText: string;
  renewalText: string;
  expirationDate: string;
  isUrgent: boolean;
  showRenewalInfo: boolean;
}

/**
 * Format date in Norwegian format (DD.MM.YYYY HH:MM)
 */
export function formatNorwegianDate(timestamp: number, includeTime: boolean = false): string {
  const date = new Date(timestamp);
  const options: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    ...(includeTime && {
      hour: '2-digit',
      minute: '2-digit',
    })
  };
  
  return date.toLocaleDateString('nb-NO', options);
}

/**
 * Get dynamic subscription status information based on subscription state
 */
export function getSubscriptionStatusInfo(subscription: any): SubscriptionStatusInfo {
  if (!subscription) {
    return {
      statusText: 'Ingen aktiv plan',
      renewalText: 'Du må velge en plan for å få tilgang til alle funksjoner.',
      expirationDate: '',
      isUrgent: true,
      showRenewalInfo: false,
    };
  }

  const now = Date.now();
  const status = subscription.status;
  const trialEnd = subscription.trialEnd;
  const currentPeriodEnd = subscription.currentPeriodEnd;
  const planLevel = subscription.planLevel;
  const billingInterval = subscription.billingInterval;
  const cancelAtPeriodEnd = subscription.cancelAtPeriodEnd;

  // Plan names mapping
  const planNames = {
    basic: 'Liten bedrift',
    professional: 'Mellomstor bedrift',
    enterprise: 'Stor bedrift'
  };

  const planName = planNames[planLevel as keyof typeof planNames] || 'Ukjent plan';
  const billingText = billingInterval === 'year' ? 'årlig fakturering' : 'månedlig fakturering';

  // Handle trial period
  if (status === 'trialing' && trialEnd) {
    const timeRemaining = formatTimeRemaining(trialEnd);
    const isExpired = now >= trialEnd;
    
    if (isExpired) {
      return {
        statusText: `Du testet ${planName}-planen med ${billingText}. Prøveperioden er utløpt.`,
        renewalText: 'Du må velge en plan for å beholde tilgangen til alle funksjoner.',
        expirationDate: `Utløp: ${formatNorwegianDate(trialEnd, true)}`,
        isUrgent: true,
        showRenewalInfo: false,
      };
    }

    return {
      statusText: `Du tester nå ${planName}-planen med ${billingText}. Du har full tilgang til alle funksjoner i prøveperioden.`,
      renewalText: `${timeRemaining.text} av prøveperioden. Når den utløper, må du velge en plan for å beholde tilgangen.`,
      expirationDate: `Prøveperioden utløper: ${formatNorwegianDate(trialEnd, true)}`,
      isUrgent: timeRemaining.isUrgent,
      showRenewalInfo: false,
    };
  }

  // Handle active subscription
  if (status === 'active' && currentPeriodEnd) {
    const nextBillingDate = formatNorwegianDate(currentPeriodEnd);
    const timeUntilRenewal = formatTimeRemaining(currentPeriodEnd);
    
    if (cancelAtPeriodEnd) {
      return {
        statusText: `Du er på ${planName}-planen med ${billingText}. Abonnementet er satt til å avsluttes.`,
        renewalText: `Tilgangen din avsluttes ${nextBillingDate}. Abonnementet vil ikke fornyes automatisk.`,
        expirationDate: `Tilgang avsluttes: ${nextBillingDate}`,
        isUrgent: timeUntilRenewal.isUrgent,
        showRenewalInfo: false,
      };
    }

    return {
      statusText: `Du er på ${planName}-planen med ${billingText}. Du har full tilgang til alle funksjoner.`,
      renewalText: `Abonnementet fornyes automatisk ${nextBillingDate}. Du kan endre eller avbryte når som helst.`,
      expirationDate: `Neste fakturering: ${nextBillingDate}`,
      isUrgent: false,
      showRenewalInfo: true,
    };
  }

  // Handle past due subscription
  if (status === 'past_due' && currentPeriodEnd) {
    const overdueDate = formatNorwegianDate(currentPeriodEnd);
    
    return {
      statusText: `Din ${planName}-plan har forfalt betaling. Tilgangen er begrenset.`,
      renewalText: `Betalingen forfalt ${overdueDate}. Oppdater betalingsmetoden din for å gjenopprette full tilgang.`,
      expirationDate: `Forfalt: ${overdueDate}`,
      isUrgent: true,
      showRenewalInfo: false,
    };
  }

  // Handle cancelled subscription
  if (status === 'canceled') {
    const cancelledDate = subscription.cancelledAt ? formatNorwegianDate(subscription.cancelledAt) : 'Ukjent dato';
    
    return {
      statusText: `Din ${planName}-plan er avbrutt. Du har ikke lenger tilgang til alle funksjoner.`,
      renewalText: 'Du kan reaktivere abonnementet ditt eller velge en ny plan når som helst.',
      expirationDate: `Avbrutt: ${cancelledDate}`,
      isUrgent: true,
      showRenewalInfo: false,
    };
  }

  // Handle incomplete subscription
  if (status === 'incomplete' || status === 'incomplete_expired') {
    return {
      statusText: `Din ${planName}-plan er ikke fullført. Betalingen må bekreftes.`,
      renewalText: 'Fullfør betalingsprosessen for å aktivere abonnementet ditt.',
      expirationDate: '',
      isUrgent: true,
      showRenewalInfo: false,
    };
  }

  // Handle unpaid subscription
  if (status === 'unpaid' && currentPeriodEnd) {
    const unpaidDate = formatNorwegianDate(currentPeriodEnd);
    
    return {
      statusText: `Din ${planName}-plan har ubetalt faktura. Tilgangen er begrenset.`,
      renewalText: `Betal den utestående fakturaen for å gjenopprette full tilgang.`,
      expirationDate: `Ubetalt siden: ${unpaidDate}`,
      isUrgent: true,
      showRenewalInfo: false,
    };
  }

  // Handle paused subscription
  if (status === 'paused') {
    return {
      statusText: `Din ${planName}-plan er midlertidig pauset. Tilgangen er begrenset.`,
      renewalText: 'Reaktiver abonnementet ditt for å gjenopprette full tilgang.',
      expirationDate: '',
      isUrgent: false,
      showRenewalInfo: false,
    };
  }

  // Fallback for unknown status
  return {
    statusText: `Du er på ${planName}-planen med ${billingText}.`,
    renewalText: 'Kontakt support hvis du har spørsmål om abonnementet ditt.',
    expirationDate: '',
    isUrgent: false,
    showRenewalInfo: false,
  };
}

/**
 * Get simplified status text for compact displays
 */
export function getSimpleStatusText(subscription: any): string {
  if (!subscription) return 'Ingen plan';

  const status = subscription.status;
  const planLevel = subscription.planLevel;
  const planNames = {
    basic: 'Liten bedrift',
    professional: 'Mellomstor bedrift', 
    enterprise: 'Stor bedrift'
  };

  const planName = planNames[planLevel as keyof typeof planNames] || 'Ukjent plan';

  switch (status) {
    case 'trialing':
      const trialEnd = subscription.trialEnd;
      if (trialEnd && Date.now() >= trialEnd) {
        return 'Prøveperiode utløpt';
      }
      return `${planName} (prøveperiode)`;
    
    case 'active':
      return `${planName}`;
    
    case 'past_due':
      return `${planName} (forfalt)`;
    
    case 'canceled':
      return 'Avbrutt';
    
    case 'incomplete':
    case 'incomplete_expired':
      return 'Ikke fullført';
    
    case 'unpaid':
      return `${planName} (ubetalt)`;
    
    case 'paused':
      return `${planName} (pauset)`;
    
    default:
      return planName;
  }
}

/**
 * Get renewal action text based on subscription state
 */
export function getRenewalActionText(subscription: any): string {
  if (!subscription) return 'Velg plan';

  const status = subscription.status;
  const cancelAtPeriodEnd = subscription.cancelAtPeriodEnd;

  switch (status) {
    case 'trialing':
      const trialEnd = subscription.trialEnd;
      if (trialEnd && Date.now() >= trialEnd) {
        return 'Velg plan';
      }
      return 'Oppgrader nå';
    
    case 'active':
      if (cancelAtPeriodEnd) {
        return 'Reaktiver';
      }
      return 'Administrer';
    
    case 'past_due':
    case 'unpaid':
      return 'Oppdater betaling';
    
    case 'canceled':
      return 'Reaktiver';
    
    case 'incomplete':
    case 'incomplete_expired':
      return 'Fullfør betaling';
    
    case 'paused':
      return 'Reaktiver';
    
    default:
      return 'Administrer';
  }
}
