/**
 * Time formatting utilities for JobbLogg
 * Provides precise time remaining calculations for trial periods
 */

export interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  totalHours: number;
  totalMinutes: number;
  isLessThanDay: boolean;
  isLessThanHour: boolean;
}

/**
 * Calculate precise time remaining until a timestamp
 */
export function getTimeRemaining(endTimestamp: number): TimeRemaining {
  const now = Date.now();
  const diff = Math.max(0, endTimestamp - now);

  // Use Math.ceil for days to show "days remaining" properly
  // This ensures that 6 days, 23 hours, 59 minutes shows as "7 days remaining"
  const days = Math.ceil(diff / (24 * 60 * 60 * 1000));
  const hours = Math.floor((diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
  const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000));

  const totalHours = Math.floor(diff / (60 * 60 * 1000));
  const totalMinutes = Math.floor(diff / (60 * 1000));

  return {
    days,
    hours,
    minutes,
    totalHours,
    totalMinutes,
    isLessThanDay: totalHours < 24,
    isLessThanHour: totalMinutes < 60
  };
}

/**
 * Format time remaining for display in Norwegian
 */
export function formatTimeRemaining(endTimestamp: number): {
  text: string;
  shortText: string;
  isUrgent: boolean;
} {
  const time = getTimeRemaining(endTimestamp);
  
  // If expired
  if (endTimestamp <= Date.now()) {
    return {
      text: 'Prøveperioden er utløpt',
      shortText: 'Utløpt',
      isUrgent: true
    };
  }
  
  // Less than 1 hour - show minutes
  if (time.isLessThanHour) {
    const minutes = Math.max(1, time.minutes); // Show at least 1 minute
    return {
      text: `${minutes} ${minutes === 1 ? 'minutt' : 'minutter'} igjen`,
      shortText: `${minutes}m`,
      isUrgent: true
    };
  }
  
  // Less than 24 hours - show hours and minutes
  if (time.isLessThanDay) {
    if (time.minutes === 0) {
      return {
        text: `${time.totalHours} ${time.totalHours === 1 ? 'time' : 'timer'} igjen`,
        shortText: `${time.totalHours}t`,
        isUrgent: time.totalHours <= 6
      };
    } else {
      return {
        text: `${time.totalHours} ${time.totalHours === 1 ? 'time' : 'timer'} og ${time.minutes} ${time.minutes === 1 ? 'minutt' : 'minutter'} igjen`,
        shortText: `${time.totalHours}t ${time.minutes}m`,
        isUrgent: time.totalHours <= 6
      };
    }
  }
  
  // More than 24 hours - show days (and hours if less than 3 days)
  if (time.days < 3 && time.hours > 0) {
    return {
      text: `${time.days} ${time.days === 1 ? 'dag' : 'dager'} og ${time.hours} ${time.hours === 1 ? 'time' : 'timer'} igjen`,
      shortText: `${time.days}d ${time.hours}t`,
      isUrgent: time.days <= 2
    };
  }
  
  // 3+ days - just show days
  return {
    text: `${time.days} ${time.days === 1 ? 'dag' : 'dager'} igjen`,
    shortText: `${time.days}d`,
    isUrgent: time.days <= 2
  };
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use formatTimeRemaining instead
 */
export function getDaysLeft(endTimestamp: number): number {
  const now = Date.now();
  return Math.max(0, Math.ceil((endTimestamp - now) / (24 * 60 * 60 * 1000)));
}
