/**
 * User role determination utilities for JobbLogg
 * Provides consistent role detection across the application
 */

import type { User } from '@clerk/clerk-react';

export type UserRole = 'contractor' | 'customer';

/**
 * Determine if a user is a contractor based on their registration status
 * In JobbLogg, contractors are users who have completed the contractor onboarding
 * and have a company registered in the system
 */
export function determineUserRole(user: User | null | undefined, context?: 'shared-project' | 'contractor-dashboard'): UserRole {
  if (!user) {
    // Default to customer for unauthenticated users (typically shared project access)
    return 'customer';
  }

  // If we're in a shared project context, user is likely a customer
  if (context === 'shared-project') {
    return 'customer';
  }

  // If we're in contractor dashboard context, user is likely a contractor
  if (context === 'contractor-dashboard') {
    return 'contractor';
  }

  // Check if user has contractor-specific metadata
  // This would be set during contractor onboarding completion
  const isContractor = user.publicMetadata?.isContractor === true ||
                      user.publicMetadata?.hasCompany === true ||
                      user.publicMetadata?.role === 'contractor';

  // For development: Default to contractor for authenticated users without context
  // TODO: Implement proper user metadata detection when contractor onboarding is complete
  return isContractor ? 'contractor' : 'contractor';
}

/**
 * Check if a user is a registered contractor
 */
export function isContractor(user: User | null | undefined): boolean {
  return determineUserRole(user) === 'contractor';
}

/**
 * Check if a user is a customer
 */
export function isCustomer(user: User | null | undefined): boolean {
  return determineUserRole(user) === 'customer';
}

/**
 * Get user role display name in Norwegian
 */
export function getUserRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'contractor':
      return 'Leverandør';
    case 'customer':
      return 'Kunde';
    default:
      return 'Bruker';
  }
}

/**
 * Hook-like function to get user role (for use in components)
 * This provides a consistent interface similar to other hooks
 */
export function getUserRole(user: User | null | undefined, context?: 'shared-project' | 'contractor-dashboard'): {
  role: UserRole;
  isContractor: boolean;
  isCustomer: boolean;
  displayName: string;
} {
  const role = determineUserRole(user, context);

  return {
    role,
    isContractor: role === 'contractor',
    isCustomer: role === 'customer',
    displayName: getUserRoleDisplayName(role)
  };
}

/**
 * Determine user role for chat context specifically
 * This is used when we need to explicitly determine role for chat functionality
 */
export function getChatUserRole(user: User | null | undefined, isSharedProject: boolean = false): UserRole {
  if (isSharedProject) {
    return 'customer';
  }

  return determineUserRole(user, 'contractor-dashboard');
}
