# Stripe Products Setup for Test Environment

**Date:** 2025-09-07  
**Environment:** Test/Development (enchanted-quail-174)  
**Purpose:** Create identical product structure to production for testing

## Products Created

### 1. Basic Plan - "Liten bedrift"
- **Product ID:** `prod_T0eoZmTHg7PAg8`
- **Description:** JobbLogg Basic Plan - For small businesses with 1-9 employees
- **Metadata:**
  - `plan_level`: basic
  - `employee_range`: 1-9
  - `max_seats`: 9

**Prices:**
- **Monthly:** `price_1S4dVtRqXwHRnsDwJ4PTZ9Vj` - 299.00 NOK/month
- **Yearly:** `price_1S4dW1RqXwHRnsDwFYZErPJP` - 2,870.00 NOK/year (20% discount)

### 2. Professional Plan - "Mellomstor bedrift"
- **Product ID:** `prod_T0epto8uyWw9ue`
- **Description:** JobbLogg Professional Plan - For medium businesses with 10-49 employees
- **Metadata:**
  - `plan_level`: professional
  - `employee_range`: 10-49
  - `max_seats`: 49

**Prices:**
- **Monthly:** `price_1S4dW9RqXwHRnsDw7lnAM9x7` - 999.00 NOK/month
- **Yearly:** `price_1S4dWGRqXwHRnsDwfdzAZEA8` - 9,590.00 NOK/year (20% discount)

### 3. Enterprise Plan - "Stor bedrift"
- **Product ID:** `prod_T0ep1PVMMgZBUd`
- **Description:** JobbLogg Enterprise Plan - For large businesses with 50-249 employees
- **Metadata:**
  - `plan_level`: enterprise
  - `employee_range`: 50-249
  - `max_seats`: 249

**Prices:**
- **Monthly:** `price_1S4dWPRqXwHRnsDwPF9uQPqz` - 2,999.00 NOK/month
- **Yearly:** `price_1S4dWXRqXwHRnsDwd1SScrvA` - 28,790.00 NOK/year (20% discount)

## Pricing Structure Verification

All prices follow the JobbLogg pricing model:
- **Tax-exclusive pricing** (VAT added on top)
- **20% discount for annual billing**
- **Norwegian Kroner (NOK) currency**

### Price Calculations:
- Basic: 299 × 12 × 0.8 = 2,870 NOK/year
- Professional: 999 × 12 × 0.8 = 9,590 NOK/year  
- Enterprise: 2,999 × 12 × 0.8 = 28,790 NOK/year

## Files Updated

### 1. convex/stripe/config.ts
Updated test mode price IDs in `STRIPE_CONFIG.products.test`:
```typescript
test: {
  basic: {
    monthly: "price_1S4dVtRqXwHRnsDwJ4PTZ9Vj",
    yearly: "price_1S4dW1RqXwHRnsDwFYZErPJP"
  },
  professional: {
    monthly: "price_1S4dW9RqXwHRnsDw7lnAM9x7",
    yearly: "price_1S4dWGRqXwHRnsDwfdzAZEA8"
  },
  enterprise: {
    monthly: "price_1S4dWPRqXwHRnsDwPF9uQPqz",
    yearly: "price_1S4dWXRqXwHRnsDwd1SScrvA"
  }
}
```

### 2. src/hooks/useCheckout.ts
Updated test mode price IDs in `STRIPE_PRICE_IDS` function.

### 3. convex/planChanges.ts
Updated to use `getStripePrices()` from config.ts instead of environment variables:
```typescript
import { getStripePrices } from "./stripe/config";

function getPlanPriceIds() {
  const prices = getStripePrices();
  return {
    basic: {
      monthly: prices.basic.monthly,
      annual: prices.basic.yearly,
    },
    // ... etc
  };
}
```

## Stripe CLI Commands Used

```bash
# Create Products
stripe products create --name="Liten bedrift" --description="JobbLogg Basic Plan - For small businesses with 1-9 employees" -d "metadata[plan_level]=basic" -d "metadata[employee_range]=1-9" -d "metadata[max_seats]=9"

stripe products create --name="Mellomstor bedrift" --description="JobbLogg Professional Plan - For medium businesses with 10-49 employees" -d "metadata[plan_level]=professional" -d "metadata[employee_range]=10-49" -d "metadata[max_seats]=49"

stripe products create --name="Stor bedrift" --description="JobbLogg Enterprise Plan - For large businesses with 50-249 employees" -d "metadata[plan_level]=enterprise" -d "metadata[employee_range]=50-249" -d "metadata[max_seats]=249"

# Create Prices (amounts in øre - multiply by 100)
# Basic Plan
stripe prices create --product="prod_T0eoZmTHg7PAg8" --unit-amount=29900 --currency=nok --recurring.interval=month -d "metadata[plan_level]=basic" -d "metadata[billing_interval]=month"
stripe prices create --product="prod_T0eoZmTHg7PAg8" --unit-amount=287000 --currency=nok --recurring.interval=year -d "metadata[plan_level]=basic" -d "metadata[billing_interval]=year"

# Professional Plan  
stripe prices create --product="prod_T0epto8uyWw9ue" --unit-amount=99900 --currency=nok --recurring.interval=month -d "metadata[plan_level]=professional" -d "metadata[billing_interval]=month"
stripe prices create --product="prod_T0epto8uyWw9ue" --unit-amount=959000 --currency=nok --recurring.interval=year -d "metadata[plan_level]=professional" -d "metadata[billing_interval]=year"

# Enterprise Plan
stripe prices create --product="prod_T0ep1PVMMgZBUd" --unit-amount=299900 --currency=nok --recurring.interval=month -d "metadata[plan_level]=enterprise" -d "metadata[billing_interval]=month"
stripe prices create --product="prod_T0ep1PVMMgZBUd" --unit-amount=2879000 --currency=nok --recurring.interval=year -d "metadata[plan_level]=enterprise" -d "metadata[billing_interval]=year"
```

## Verification

Run these commands to verify the setup:
```bash
stripe products list
stripe prices list
```

## Next Steps

1. ✅ Products and prices created in test environment
2. ✅ Configuration files updated with new price IDs
3. ✅ All code references updated to use new price IDs
4. ✅ TypeScript compilation verified
5. 🔄 Test subscription flows with new price IDs
6. 🔄 Verify billing period synchronization works correctly
7. 🔄 Test trial-to-paid conversions with new products

## Notes

- All products are created in **test mode** only
- Production environment price IDs remain unchanged
- The system automatically detects test vs live mode based on the Stripe secret key
- VAT (25%) is handled automatically by Stripe's tax system
