# Stripe → Convex Subscription Sync: Period-Date Correction and Mapping Enforcement (2025‑09‑06)

This document summarizes the systematic fixes to ensure Stripe → Convex synchronization always writes correct current billing period dates and never creates orphaned subscription records.

## Summary

- Enforced a one-to-one user ↔ Stripe customer mapping and blocked writes without a resolvable `userId`.
- Guaranteed period dates (`currentPeriodStart`, `currentPeriodEnd`) are resolved before any DB write using Stripe Subscription and a safe fallback to Upcoming Invoice.
- Added structured logging before each write to make verification easy in Convex logs.
- Preserved idempotent updates with <PERSON><PERSON> as the source of truth.

---

## Key changes

1) Authoritative user-customer mapping
- Schema: Added `users.stripeCustomerId` + index `users.by_stripe_customer_id`.
- Webhook handlers: resolve `userId` by `stripeCustomerId` before writing; abort if missing.
- Upsert mutation: reject writes with missing `userId`; also backfill `users.stripeCustomerId` when known.

2) Period date resolution
- For every relevant webhook, resolve `current_period_start`/`end` from the Stripe Subscription.
- If missing, use `invoices.retrieveUpcoming({ customer, subscription })` to derive `period_start`/`period_end`.
- If still unavailable in very short windows, skip unsafe writes (avoid persisting trial-anchored dates).

3) Structured logging
- Before each DB write, log a single line containing all critical fields:
  - eventType, stripeCustomerId, stripeSubscriptionId, userId, status, billingInterval, currentPeriodStart/End (ISO), planLevel, seats, transition (e.g. `trial→active`).

---

## Code excerpts (key modifications)

Note: Excerpts only; see repo files for full context.

### convex/stripe/fulfill.ts — checkout.session.completed
```ts
const mapping = await ctx.runQuery("subscriptions:getByStripeCustomerIdInternal", { stripeCustomerId: customerId });
const resolvedUserId = mapping?.userId;
if (!resolvedUserId) {
  console.log(`❌ MAPPING_FAILED: stripeCustomerId=${customerId} - no userId found, aborting upsert`);
  return;
}

// Resolve periods, with Upcoming Invoice fallback
let periodStartMs = sub.current_period_start ? sub.current_period_start * 1000 : undefined;
let periodEndMs   = sub.current_period_end   ? sub.current_period_end   * 1000 : undefined;
if (periodStartMs === undefined || periodEndMs === undefined) {
  const upcoming = await stripe.invoices.retrieveUpcoming({ customer: customerId, subscription: sub.id });
  const ps = upcoming?.period_start ?? upcoming?.lines?.data?.[0]?.period?.start;
  const pe = upcoming?.period_end   ?? upcoming?.lines?.data?.[0]?.period?.end;
  if (typeof ps === 'number' && typeof pe === 'number') {
    periodStartMs = ps * 1000; periodEndMs = pe * 1000;
  }
}

// Structured log prior to DB write
console.log(`🔄 SUBSCRIPTION_UPSERT: eventType=${event.type} stripeCustomerId=${customerId} ` +
  `stripeSubscriptionId=${sub.id} userId=${resolvedUserId} status=${sub.status} ` +
  `billingInterval=${interval} currentPeriodStart=${new Date(periodStartMs!).toISOString()} ` +
  `currentPeriodEnd=${new Date(periodEndMs!).toISOString()} planLevel=${plan} seats=1 transition=${transition}`);

await ctx.runMutation(internal.subscriptions.upsertFromStripe.upsertFromStripe, {
  userId: resolvedUserId,
  stripeSubscriptionId: sub.id,
  stripeCustomerId: customerId,
  status: sub.status,
  planLevel: plan,
  billingInterval: interval,
  currentPeriodStart: periodStartMs,
  currentPeriodEnd: periodEndMs,
  trialStart: sub.trial_start ? sub.trial_start * 1000 : undefined,
  trialEnd:   sub.trial_end   ? sub.trial_end   * 1000 : undefined,
  seats: 1,
});
```

### convex/stripe/fulfill.ts — customer.subscription.created / updated
```ts
const mappingC = await ctx.runQuery("subscriptions:getByStripeCustomerIdInternal", { stripeCustomerId: customerId });
if (!mappingC?.userId) {
  console.log(`❌ MAPPING_FAILED: stripeCustomerId=${customerId} - no userId found, aborting upsert`);
  return;
}
// ... resolve periods similarly (with fallback) and log SUBSCRIPTION_UPSERT ...
await ctx.runMutation(internal.subscriptions.upsertFromStripe.upsertFromStripe, {
  userId: mappingC.userId,
  stripeSubscriptionId: sub.id,
  stripeCustomerId: customerId,
  // ...
});
```

### convex/subscriptions/upsertFromStripe.ts — guard + users mapping backfill
```ts
if (!args.userId) {
  console.log(`❌ UPSERT_REJECTED: missing userId for stripeSubscriptionId=${args.stripeSubscriptionId} stripeCustomerId=${args.stripeCustomerId}`);
  return { action: "skipped_missing_user" } as const;
}

// Ensure the users row carries the authoritative Stripe customer mapping
const user = await ctx.db
  .query("users")
  .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId!))
  .first();
if (user && user.stripeCustomerId !== args.stripeCustomerId) {
  await ctx.db.patch(user._id, { stripeCustomerId: args.stripeCustomerId, updatedAt: Date.now() });
}
```

### convex/schema.ts — users: stripeCustomerId + index
```ts
// In users table definition
stripeCustomerId: v.optional(v.string()),

// Indexes
.index("by_stripe_customer_id", ["stripeCustomerId"])
```

---

## What to look for in Convex logs

1) Structured “SUBSCRIPTION_UPSERT” lines before each write
- Example:
```
🔄 SUBSCRIPTION_UPSERT: eventType=checkout.session.completed stripeCustomerId=cus_… stripeSubscriptionId=sub_… userId=user_… status=active billingInterval=month currentPeriodStart=2025-09-06T12:29:45.000Z currentPeriodEnd=2025-10-06T12:29:45.000Z planLevel=professional seats=1 transition=trial→active
```
- Expect currentPeriodStart/End to reflect payment time → next renewal (not trialEnd).

2) Mapping failures are explicit and safe
- If mapping is broken:
```
❌ MAPPING_FAILED: stripeCustomerId=cus_… - no userId found, aborting upsert
```
- No DB write occurs.

3) Upsert without userId is blocked
```
❌ UPSERT_REJECTED: missing userId for stripeSubscriptionId=sub_… stripeCustomerId=cus_…
```

4) Period fallback usage (when Stripe delay exists)
```
🧮 Fallback periods (subscription.created|subscription.updated|invoice.payment_succeeded): { start: '…', end: '…' }
```

5) Known edge log
- In one checkout path, `invoices.retrieveUpcoming` may not be available in that execution context. The handler logs it and proceeds safely without writing stale dates.

---

## Why this fixes the original billing-cycle issue

- Previously, `checkout.session.completed` sometimes wrote a subscription row without valid `currentPeriodStart/End`, leaving the DB anchored to the original trial end.
- Now, every write is preceded by:
  - A verified `stripeCustomerId → userId` mapping (or the write is aborted), and
  - Period resolution from Stripe (with fallback), or a safe skip if still unavailable.
- Therefore, the DB reflects the real billing cycle dates once payment completes, matching Stripe’s authoritative state.

---

## Next steps for testing

1) Simulate a trial → paid upgrade
- Confirm logs show a `SUBSCRIPTION_UPSERT` with `currentPeriodEnd` ≈ 1 month from payment.
- Ensure no upsert attempts with `userId: undefined`.

2) Verify UI
- The “next renewal” date should match Stripe’s expected renewal date (e.g., ~Oct 6 if paid Sep 6).

3) Optional hardening
- If desired, skip the one checkout fallback path that throws `invoices.retrieveUpcoming is not a function` and rely on the subsequent `subscription.updated`/`invoice.payment_succeeded` for periods; behavior is already safe, but this would remove the noisy log.

---

## Notes
- Stripe remains the authority; Convex is idempotently updated.
- Trial timestamps are preserved for history.
- No automatic commits were made.

