// Quick test to see what the debug function returns
// Run this in your browser console or add it to a component

// Replace with your actual Clerk user ID
const testUserId = "your-clerk-user-id-here";

// Call the debug function
convex.query("teamManagement:debugTeamMemberCountComparison", {
  userId: testUserId
}).then(result => {
  console.log('🔍 Debug Results:', result);
  
  if (result.error) {
    console.error('❌ Error:', result.error);
    return;
  }
  
  console.log('📊 Company ID:', result.companyId);
  console.log('👥 Total members found:', result.totalMembersFound);
  console.log('🔢 getTeamMemberCount active count:', result.getTeamMemberCount.activeCount);
  console.log('🔢 getTeamMembers active count:', result.getTeamMembers.activeCount);
  console.log('✅ Counts match:', result.countsMatch);
  
  console.log('\n📋 Members from getTeamMemberCount:');
  result.getTeamMemberCount.members.forEach((member, index) => {
    console.log(`${index + 1}. ${member.displayName} (${member.role}) - Active: ${member.isActive}, Status: ${member.invitationStatus}`);
  });
  
  console.log('\n📋 Members from getTeamMembers:');
  result.getTeamMembers.members.forEach((member, index) => {
    console.log(`${index + 1}. ${member.displayName} (${member.role}) - Active: ${member.isActive}, Status: ${member.invitationStatus}`);
  });
}).catch(error => {
  console.error('❌ Debug function failed:', error);
});
